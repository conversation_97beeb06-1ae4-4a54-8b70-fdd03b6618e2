from pypinyin import pinyin, Style
from const import PROJECT_ROOT, VTUBER_NAME_DICT


def convert_to_pinyin(text):
    return "".join([item[0] for item in pinyin(text, style=Style.NORMAL)])


def get_en_role_name(role_name):
    if role_name in VTUBER_NAME_DICT:
        return VTUBER_NAME_DICT[role_name]
    else:
        return "xingtong"


def get_zh_role_name(role_name):
    if role_name in VTUBER_NAME_DICT:
        return role_name
    else:
        for key, val in VTUBER_NAME_DICT.items():
            if val == role_name:
                return key
    return NotImplementedError


# input role_name , nick name is also allowed
# output folder_role_name and url url = f'https://github.com/LC1332/Haruhi-2-Dev/raw/main/data/character_in_zip/{role_name}.zip'
def get_folder_role_name(role_name):
    if role_name in VTUBER_NAME_DICT:
        folder_role_name = VTUBER_NAME_DICT[role_name]
        url = f"https://github.com/LC1332/Haruhi-2-Dev/raw/main/data/character_in_zip/{folder_role_name}.zip"
        return folder_role_name, url
    else:
        print("role_name {} not found, using haruhi as default".format(role_name))
        return get_folder_role_name("xingtong")


def get_mid_with_role_name(role_name):
    users_file_path = f"{PROJECT_ROOT}/config/target.txt"
    with open(users_file_path, "r", encoding="utf-8") as file:
        lines = file.readlines()
    for line in lines:
        parts = line.strip().split(", ")
        if parts[1] == role_name:
            return parts[0]
    raise Exception(f"Character {role_name} not found in target.txt")
