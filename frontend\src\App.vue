<script setup lang="ts">
import { computed, provide, ref, watch} from 'vue';
import { RouterView, useRoute, useRouter } from 'vue-router';

const router = useRouter();
const route = useRoute()
const isLoading = ref(false);
const selectedVTuber = ref('星瞳');
const isSidebarOpen = ref(true);
const isDarkMode = ref(false);

// User Dropdown State
const isUserDropdownOpen = ref(false);
const username = ref(localStorage.getItem('username') || '');
const email = ref(localStorage.getItem('email') || '');

// Analysis Icons Dropdown State
const isAnalysisMenuOpen = ref(false);
const activeAnalysisNav = ref<string | null>(null);
let analysisMenuTimeoutId: number | undefined = undefined;

// Provide the isLoading ref to child components
provide('isLoading', isLoading);
provide('selectedVTuber', selectedVTuber)
provide('isDarkMode', isDarkMode)

const vtubers = [
  ['星瞳', '星瞳_Official'],
  ['扇宝', '扇宝'],
  ['安可', '安可anko_Official'],
  ['恬豆', '恬豆发芽了'],
  ['沐霂', '沐霂是MUMU呀'],
  ['又一', '又一充电中'],
  ['梨安', '梨安不迷路'],
  ['娜娜米', '七海Nana7mi'],
  ['小可', '小可学妹'],
  ['兰音', '兰音Reine'],
  ['嘉然', '嘉然今天吃什么'],
  ['乃琳', '乃琳Queen'],
  ['贝拉', '贝拉kira'],
  ['東雪蓮', '東雪蓮Official'],
  ['冰糖', '冰糖IO'],
  ['早稻叽', '早稻叽'],
  ['明前奶绿', '明前奶绿'],
  ['莉姬', '莉姬LIJI'],
  ['露米', '露米Lumi_Official'],
  ['露早', '露早GOGO'],
  ['柚恩', '柚恩不加糖'],
  ['塔菲', '永雏塔菲'],
  ['阿梓', '阿梓从小就很可爱'],
  ['东爱璃', '东爱璃lovely'],
  ['白神遥', '白神遥Haruka'],
  ['hanser', 'hanser'],
  ['泠鸢', '泠鸢yousa'],
  ['多多', '多多poi'],
  ['墨茶', '墨茶offical'],
  ['折原露露', '折原露露'],
  ['雫るる', '雫るる'],
  ['雪糕', '雪糕cheese'],
  ['伊万', '伊万酱哒油'],
  ['花花', '花花Haya'],
  ['蕾尔娜', '蕾尔娜Leona'],
  ['汐音', '汐音_Shine']
]

provide('vtubers', vtubers)

const selectVTuber = (vtuber: string) => {
  selectedVTuber.value = vtuber
  // Only navigate to PersonalSpace if not already on a valid route
  if (route.name === 'comments') {
    router.push({ name: 'comments', params: { vtuber } })
  } else if (route.name === 'dynamics') {
    router.push({ name: 'dynamics', params: { vtuber } })
  } else if (route.name === 'videos') {
    router.push({ name: 'videos', params: { vtuber } })
  } else if (route.name === 'live-stream') {
    router.push({ name: 'live-stream', params: { vtuber } })
  } else {
    router.push({ name: 'personalSpace', params: { vtuber } })
  }
}

const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
  document.documentElement.classList.toggle('dark', isDarkMode.value)
}

watch(selectedVTuber, newVTuber => {
  console.log(`Selected VTuber changed to: ${newVTuber}`);
});

const toggleUserDropdown = () => {
  isUserDropdownOpen.value = !isUserDropdownOpen.value;
};

const closeUserDropdown = () => {
  isUserDropdownOpen.value = false;
};

declare global {
  interface HTMLElement {
    clickOutsideEvent?: (event: MouseEvent) => void;
  }
}

const vClickOutside = {
  beforeMount(el: HTMLElement, binding: any) {
    el.clickOutsideEvent = function(event: MouseEvent) {
      if (!(el === event.target || el.contains(event.target as Node))) {
        binding.value();
      }
    };
    document.addEventListener('click', el.clickOutsideEvent);
  },
  unmounted(el: HTMLElement) {
    if (el.clickOutsideEvent) {
      document.removeEventListener('click', el.clickOutsideEvent);
    }
  }
};

const logout = () => {
  isUserDropdownOpen.value = false; // Close dropdown
  // clear any user session/token from localStorage or a store
  localStorage.removeItem('isLoggedIn');
  router.push('/login');
};

const openAnalysisMenu = (navItemName: string) => {
  if (analysisMenuTimeoutId) {
    clearTimeout(analysisMenuTimeoutId);
    analysisMenuTimeoutId = undefined;
  }
  // Show menu if hovering over 'personalSpace' and it's a relevant view
  if (navItemName === 'personalSpace') {
    isAnalysisMenuOpen.value = true;
    activeAnalysisNav.value = navItemName;
  }
};

const closeAnalysisMenu = () => {
  isAnalysisMenuOpen.value = false;
  activeAnalysisNav.value = null;
};

const closeAnalysisMenuWithDelay = () => {
  analysisMenuTimeoutId = setTimeout(() => {
    closeAnalysisMenu();
  }, 200); // 200ms delay to allow moving mouse into the dropdown
};

const handleAnalysisLinkClick = () => {
  closeAnalysisMenu(); // Close menu when a link inside is clicked
};


const isPersonalAnalysis = computed(() => {
  return [
    'personalSpace',
    'dynamics',
    'videos',
    'comments',
    'live-stream',
  ].includes(route.name as string)
})

const showLayout = computed(() => {
  const nonLayoutRoutes = ['login', 'signup'];
  return !nonLayoutRoutes.includes(route.name as string);
});

const analysisIcons = [
  { name: 'personalSpace', icon: 'fa-user', title: '个人空间' },
  { name: 'dynamics', icon: 'fa-chart-line', title: '动态分析' },
  { name: 'videos', icon: 'fa-video', title: '视频分析' },
  { name: 'comments', icon: 'fa-comments', title: '评论分析' },
  { name: 'live-stream', icon: 'fa-broadcast-tower', title: '直播分析' },
]
</script>

<template>
  <div :class="['flex h-screen flex-col', isDarkMode ? 'dark' : '']">
    <!-- Header -->
    <header
      v-if="showLayout"
      class="bg-white dark:bg-gray-800 shadow-md px-6 py-3 flex justify-between items-center sticky top-0 z-50"
    >
      <div class="flex items-center space-x-10">
        <div class="flex items-center">
          <img
            src="/pics/icon_169.png"
            alt="App Logo"
            class="h-10 w-auto mr-3"
          >
        </div>

        <!-- Navigation items -->
        <nav class="flex space-x-8">
          <RouterLink
            :to="{ name: 'home' }"
            class="nav-link"
          >
            首页
          </RouterLink>
          <!-- Personal Analysis with Dropdown -->
          <div class="relative"
               @mouseenter="openAnalysisMenu('personalSpace')"
               @mouseleave="closeAnalysisMenuWithDelay">
            <RouterLink
              :to="{ name: 'personalSpace', params: { vtuber: selectedVTuber } }"
              class="nav-link"
              :class="{ 'router-link-active': isPersonalAnalysis }"
              @focus="openAnalysisMenu('personalSpace')"
              @blur="closeAnalysisMenuWithDelay"
            >
              个人分析
            </RouterLink>
            <div v-if="isAnalysisMenuOpen && activeAnalysisNav === 'personalSpace'"
                 class="absolute left-0 mt-2 w-56 bg-white dark:bg-gray-700 rounded-md shadow-xl py-1 z-50"
                 @mouseenter="openAnalysisMenu('personalSpace')" @mouseleave="closeAnalysisMenuWithDelay">
                <router-link
                  v-for="item in analysisIcons"
                  :key="item.name"
                  :to="{ name: item.name, params: { vtuber: selectedVTuber } }"
                  class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-150"
                  @click="handleAnalysisLinkClick"
                >
                  <i :class="['fas', item.icon, 'mr-3 w-4 text-center']"></i>{{ item.title }}
                </router-link>
            </div>
          </div>
          <RouterLink
            :to="{ name: 'comparison' }"
            class="nav-link"
          >
            VUP对比
          </RouterLink>
          <RouterLink
            :to="{ name: 'userAnalyze' }"
            class="nav-link"
          >
            舰长流向
          </RouterLink>
          <RouterLink
            :to="{ name: 'creatorInfo' }"
            class="nav-link"
          >
            创作者分析
          </RouterLink>
          <RouterLink
            :to="{ name: 'announcement' }"
            class="nav-link"
          >
            公告
          </RouterLink>
        </nav>
      </div>
      <div class="flex items-center space-x-4">
        <!-- Theme Toggle -->
        <div class="flex items-center">
          <!-- <span class="mr-2 text-sm text-gray-600 dark:text-gray-400">主题</span> -->
          <button
            @click="toggleDarkMode"
            class="bg-gray-200 dark:bg-gray-700 p-2 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-blue-500"
            :aria-label="
              isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'
            "
          >
            <i
              :class="[
                'fas text-lg',
                isDarkMode
                  ? 'fa-sun text-yellow-400'
                  : 'fa-moon text-indigo-400',
              ]"
            ></i>
          </button>
        </div>

        <!-- User Avatar Dropdown -->
        <div class="relative ml-3" v-click-outside="closeUserDropdown">
          <button @click.stop="toggleUserDropdown"
                  class="flex text-sm bg-gray-800 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
                  aria-expanded="false" aria-haspopup="true">
            <span class="sr-only">Open user menu</span>
            <img class="h-10 w-10 rounded-full object-cover"
                 src="/pics/icon_head.png"
                 alt="User Avatar">
            <!-- Fallback Icon if image fails to load, or as default -->
            <!-- <span v-else class="inline-flex items-center justify-center h-10 w-10 rounded-full bg-gray-600">
              <i class="fas fa-user text-xl text-gray-300"></i>
            </span> -->
          </button>
          <transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div v-if="isUserDropdownOpen"
                 class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg py-1 bg-white dark:bg-gray-700 ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
                 role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">
              <div class="px-4 py-3">
                <!-- <p class="text-sm text-gray-900 dark:text-white"> -->
                <!-- {{ username }}  -->
                 <!-- </p> -->
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  {{ email }}
                </p>
              </div>
              <div class="border-t border-gray-200 dark:border-gray-600"></div>
              <a href="#" @click.prevent="router.push({ name: 'personalSpace', params: { vtuber: selectedVTuber } })"
                 class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"
                 role="menuitem" tabindex="-1" id="user-menu-item-0">个人主页</a>
              <a href="#" @click.prevent="logout"
                 class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"
                 role="menuitem" tabindex="-1" id="user-menu-item-1">登出</a>
              <!-- Example of another item -->
              <!-- <a href="#"
                 class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"
                 role="menuitem" tabindex="-1" id="user-menu-item-2">设置</a> -->
            </div>
          </transition>
        </div>
      </div>
    </header>

    <!-- Main Content Area (Sidebar + RouterView) -->
    <div class="flex-1 flex overflow-hidden bg-gray-100 dark:bg-gray-900">
      <!-- Sidebar -->
      <aside
        v-if="showLayout && isPersonalAnalysis"
        :class="[
          'text-white dark:text-gray-300 flex flex-col transition-all duration-300 w-20 mx-2 rounded-md shadow-lg',
        ]"
      >
        <div
          class="border-b border-gray-200 dark:border-gray-700 flex items-center justify-between"
        >
        </div>
        <nav class="flex-grow overflow-y-auto sidebar-scroll">
          <div class="mt-2 p-2">
            <div class="flex flex-col items-center space-y-4">
              <button
                v-for="[shortName, fullName] in vtubers"
                :key="shortName"
                @click="selectVTuber(shortName)"
                class="w-16 h-16 rounded-full overflow-hidden focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-150 relative group"
                :class="{ 'ring-2 ring-blue-500': selectedVTuber === shortName }"
              >
                <img
                  :src="`/vtubers/${shortName}.jpg`"
                  :alt="fullName"
                  class="w-full h-full object-cover"
                />
                <div
                  class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                >
                  <span class="text-white text-xs">{{ shortName }}</span>
                </div>
              </button>
            </div>
          </div>
        </nav>
      </aside>

      <!-- Content Area -->
      <div class="flex-1 overflow-y-auto p-8 relative">
        <!-- Background grid pattern -->
        <div class="absolute inset-0 pointer-events-none bg-grid-purple opacity-10 dark:opacity-5"></div>
        <div class="flex flex-col space-y-4 relative z-10">
          <!-- Router view -->
          <RouterView />
        </div>
      </div>
    </div>
  </div>
</template>

<style>
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css');

.icon-up {
  position: relative;
  top: -2px;
}

body {
  font-family: 'Roboto', sans-serif;
}

.dark {
  color-scheme: dark;
}

.dark body {
  background-color: #1a202c;
  color: #e2e8f0;
}

body {
  transition: background-color 0.3s ease, color 0.3s ease;
}

.nav-link {
  color: #131314;
  font-size: 1.25rem;
  font-weight: 600;
  transition: color 0.2s ease;
  padding-bottom: 2px;
  border-bottom: 2px solid transparent;
}

.nav-link:hover {
  color: #90cdf4;
  border-bottom-color: #90cdf4;
}

.router-link-active {
  color: #0080ff;
  border-bottom-color: #0080ff;
}

.dark .nav-link {
  color: #171718;
}

.dark .nav-link:hover {
  color: #90cdf4;
  border-bottom-color: #90cdf4;
}

.dark .router-link-active {
  color: #63b3ed;
  border-bottom-color: #63b3ed;
}

/* Sidebar scrollbar styles */
.sidebar-scroll::-webkit-scrollbar {
  width: 10px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: #1a202c; /* bg-gray-900 */
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background-color: #a5aaed; /* bg-gray-700 */
  border-radius: 4px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background-color: #7192c7; /* bg-gray-600 */
}

.live-status-indicator {
  transform: translate(25%, 25%);
  z-index: 10;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
