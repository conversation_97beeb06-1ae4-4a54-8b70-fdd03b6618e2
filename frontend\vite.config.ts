import { fileURLToPath, URL } from 'node:url'

import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 80,
    open: true,
    cors: true,

    proxy: {
      '^/urlApi/.*': {
        target: 'http://localhost:8653',
        changeOrigin: true,
        secure: true,
        ws: true,
        rewrite: path => path.replace(/^\/urlApi/, ''),
      },
      '/uka_api': {
        target: 'https://ukamnads.icu/',
        changeOrigin: true,
        ws: true,
        secure: true,
        rewrite: path => path.replace(/^\/uka_api/, ''),
      },
    },
  },
})
