<template>
  <div class="creator-info">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">创作者数据分析</h1>
      <p class="page-description">查看创作者的详细数据统计和分析报告</p>

      <!-- 使用说明 -->
      <details class="usage-guide">
        <summary>📖 使用说明</summary>
        <div class="guide-content">
          <h4>如何使用：</h4>
          <ol>
            <li><strong>输入用户ID：</strong>在下方输入框中输入要查询的创作者用户ID</li>
            <li><strong>选择查询类型：</strong>
              <ul>
                <li><strong>概览数据：</strong>获取创作者的总体统计信息</li>
                <li><strong>日期范围：</strong>查询指定时间段内的数据变化</li>
                <li><strong>单日数据：</strong>查看某一天的详细数据</li>
              </ul>
            </li>
            <li><strong>点击获取数据：</strong>配置完成后点击"获取数据"按钮</li>
            <li><strong>查看结果：</strong>在下方的统计卡片和分析选项卡中查看数据</li>
          </ol>
          <p><strong>提示：</strong>可以使用快速日期按钮快速选择常用的时间范围。</p>
        </div>
      </details>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <div class="control-group">
        <label for="uid-input">用户ID:</label>
        <input
          id="uid-input"
          v-model="currentUid"
          type="text"
          placeholder="请输入用户ID"
          class="uid-input"
        />
      </div>

      <div class="control-group">
        <label for="date-type">查询类型:</label>
        <select id="date-type" v-model="queryType" class="date-type-select">
          <option value="overview">概览数据</option>
          <option value="date-range">日期范围</option>
          <option value="single-date">单日查询</option>
        </select>
      </div>

      <!-- 日期范围选择器 -->
      <div v-if="queryType === 'date-range'" class="date-range-group">
        <div class="date-input-group">
          <label for="start-date">开始日期:</label>
          <input
            id="start-date"
            v-model="startDate"
            type="date"
            class="date-input"
            :max="endDate || today"
          />
        </div>
        <div class="date-input-group">
          <label for="end-date">结束日期:</label>
          <input
            id="end-date"
            v-model="endDate"
            type="date"
            class="date-input"
            :min="startDate"
            :max="today"
          />
        </div>
        <div class="quick-date-buttons">
          <button @click="setQuickDateRange(7)" class="quick-date-btn">最近7天</button>
          <button @click="setQuickDateRange(30)" class="quick-date-btn">最近30天</button>
          <button @click="setQuickDateRange(90)" class="quick-date-btn">最近90天</button>
        </div>
      </div>

      <!-- 单日选择器 -->
      <div v-if="queryType === 'single-date'" class="single-date-group">
        <label for="single-date">选择日期:</label>
        <input
          id="single-date"
          v-model="singleDate"
          type="date"
          class="date-input"
          :max="today"
        />
        <div class="quick-date-buttons">
          <button @click="setQuickSingleDate(0)" class="quick-date-btn">今天</button>
          <button @click="setQuickSingleDate(1)" class="quick-date-btn">昨天</button>
          <button @click="setQuickSingleDate(7)" class="quick-date-btn">一周前</button>
        </div>
      </div>

      <button @click="fetchData" class="fetch-button" :disabled="loading || !isFormValid">
        <span v-if="loading" class="button-spinner"></span>
        {{ loading ? '加载中...' : '获取数据' }}
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载数据...</p>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="clearError" class="clear-error-button">清除错误</button>
    </div>

    <!-- 数据展示区域 -->
    <div v-if="!loading && !error" class="data-container">
      <!-- 概览统计卡片 -->
      <div v-if="overviewData" class="overview-section">
        <h2 class="section-title">概览统计</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">👥</div>
            <div class="stat-value">{{ formatNumber(overviewData.total_fans) }}</div>
            <div class="stat-label">总粉丝数</div>
            <div class="stat-change positive" v-if="overviewData.incr_fans > 0">
              +{{ formatNumber(overviewData.incr_fans) }}
            </div>
            <div class="stat-change neutral" v-else-if="overviewData.incr_fans === 0">
              无变化
            </div>
            <div class="stat-change negative" v-else>
              {{ formatNumber(overviewData.incr_fans) }}
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">▶️</div>
            <div class="stat-value">{{ formatNumber(overviewData.total_click) }}</div>
            <div class="stat-label">总播放数</div>
            <div class="stat-change positive" v-if="overviewData.incr_click > 0">
              +{{ formatNumber(overviewData.incr_click) }}
            </div>
            <div class="stat-change neutral" v-else-if="overviewData.incr_click === 0">
              无变化
            </div>
            <div class="stat-change negative" v-else>
              {{ formatNumber(overviewData.incr_click) }}
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">👍</div>
            <div class="stat-value">{{ formatNumber(overviewData.total_like) }}</div>
            <div class="stat-label">总点赞数</div>
            <div class="stat-change positive" v-if="overviewData.inc_like > 0">
              +{{ formatNumber(overviewData.inc_like) }}
            </div>
            <div class="stat-change neutral" v-else-if="overviewData.inc_like === 0">
              无变化
            </div>
            <div class="stat-change negative" v-else>
              {{ formatNumber(overviewData.inc_like) }}
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">🪙</div>
            <div class="stat-value">{{ formatNumber(overviewData.total_coin) }}</div>
            <div class="stat-label">总投币数</div>
            <div class="stat-change positive" v-if="overviewData.inc_coin > 0">
              +{{ formatNumber(overviewData.inc_coin) }}
            </div>
            <div class="stat-change neutral" v-else-if="overviewData.inc_coin === 0">
              无变化
            </div>
            <div class="stat-change negative" v-else>
              {{ formatNumber(overviewData.inc_coin) }}
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">💬</div>
            <div class="stat-value">{{ formatNumber(overviewData.total_reply) }}</div>
            <div class="stat-label">总评论数</div>
            <div class="stat-change positive" v-if="overviewData.incr_reply > 0">
              +{{ formatNumber(overviewData.incr_reply) }}
            </div>
            <div class="stat-change neutral" v-else-if="overviewData.incr_reply === 0">
              无变化
            </div>
            <div class="stat-change negative" v-else>
              {{ formatNumber(overviewData.incr_reply) }}
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">⭐</div>
            <div class="stat-value">{{ formatNumber(overviewData.total_fav) }}</div>
            <div class="stat-label">总收藏数</div>
            <div class="stat-change positive" v-if="overviewData.inc_fav > 0">
              +{{ formatNumber(overviewData.inc_fav) }}
            </div>
            <div class="stat-change neutral" v-else-if="overviewData.inc_fav === 0">
              无变化
            </div>
            <div class="stat-change negative" v-else>
              {{ formatNumber(overviewData.inc_fav) }}
            </div>
          </div>
        </div>

        <!-- 数据趋势图表 -->
        <div class="charts-section">
          <h3 class="chart-title">数据趋势</h3>
          <div class="chart-container">
            <div ref="overviewChart" class="chart"></div>
          </div>
        </div>
      </div>

      <!-- 数据分析选项卡 -->
      <div class="analysis-section">
        <div class="tab-container">
          <div class="tab-header">
            <button
              v-for="tab in analysisTabs"
              :key="tab.key"
              @click="activeTab = tab.key"
              :class="['tab-button', { active: activeTab === tab.key }]"
            >
              {{ tab.label }}
            </button>
          </div>

          <div class="tab-content">
            <!-- 涨粉分析 -->
            <div v-if="activeTab === 'attention'" class="tab-panel">
              <h3>涨粉分析</h3>
              <div v-if="attentionData && attentionData.mergedData" class="analysis-content">
                <!-- 数据摘要卡片 -->
                <div class="attention-summary-cards">
                  <div class="summary-card">
                    <div class="summary-icon">📈</div>
                    <div class="summary-value">{{ formatNumber(attentionData.mergedData.summary.netFansIncrease) }}</div>
                    <div class="summary-label">总净增粉丝</div>
                  </div>
                  <div class="summary-card">
                    <div class="summary-icon">👀</div>
                    <div class="summary-value">{{ formatNumber(attentionData.mergedData.summary.visitorPlayCount) }}</div>
                    <div class="summary-label">游客播放总数</div>
                  </div>
                  <div class="summary-card">
                    <div class="summary-icon">➕</div>
                    <div class="summary-value">{{ formatNumber(attentionData.mergedData.summary.newFollowCount) }}</div>
                    <div class="summary-label">新增关注总数</div>
                  </div>
                  <div class="summary-card">
                    <div class="summary-icon">📊</div>
                    <div class="summary-value">{{ formatNumber(attentionData.mergedData.summary.avgDailyIncrease) }}</div>
                    <div class="summary-label">日均涨粉</div>
                  </div>
                </div>

                <!-- 数据概览 -->
                <div class="data-summary">
                  <p class="summary-text">
                    分析了 {{ attentionData.mergedData.totalRecords }} 条记录，
                    时间范围：{{ attentionData.mergedData.dateRange.start }} 至 {{ attentionData.mergedData.dateRange.end }}
                  </p>
                </div>

                <!-- 图表容器 -->
                <div class="chart-container">
                  <div ref="attentionChart" class="chart"></div>
                </div>

                <!-- 详细数据展开 -->
                <details class="raw-data-details">
                  <summary>查看处理后的数据结构</summary>
                  <pre class="raw-data">{{ JSON.stringify(attentionData.mergedData, null, 2) }}</pre>
                </details>

                <details class="raw-data-details">
                  <summary>查看原始数据</summary>
                  <pre class="raw-data">{{ JSON.stringify(attentionData.data, null, 2) }}</pre>
                </details>
              </div>
              <div v-else class="no-data">
                <div class="no-data-icon">📈</div>
                <p>暂无涨粉分析数据</p>
                <p class="no-data-hint">请先获取数据或检查网络连接</p>
              </div>
            </div>

            <!-- 播放分析 -->
            <div v-if="activeTab === 'archive'" class="tab-panel">
              <h3>播放分析</h3>
              <div v-if="archiveData" class="analysis-content">
                <div class="data-summary">
                  <p class="summary-text">播放分析数据已加载，包含播放量趋势和来源分析</p>
                </div>
                <div class="chart-container">
                  <div ref="archiveChart" class="chart"></div>
                </div>
                <details class="raw-data-details">
                  <summary>查看原始数据</summary>
                  <pre class="raw-data">{{ JSON.stringify(archiveData, null, 2) }}</pre>
                </details>
              </div>
              <div v-else class="no-data">
                <div class="no-data-icon">📺</div>
                <p>暂无播放分析数据</p>
                <p class="no-data-hint">请先获取数据或检查网络连接</p>
              </div>
            </div>

            <!-- 视频数据 -->
            <div v-if="activeTab === 'video'" class="tab-panel">
              <h3>视频数据</h3>
              <div v-if="videoData" class="analysis-content">
                <div class="data-summary">
                  <p class="summary-text">视频数据已加载，包含视频概览和详细统计</p>
                </div>
                <div class="chart-container">
                  <div ref="videoChart" class="chart"></div>
                </div>
                <details class="raw-data-details">
                  <summary>查看原始数据</summary>
                  <pre class="raw-data">{{ JSON.stringify(videoData, null, 2) }}</pre>
                </details>
              </div>
              <div v-else class="no-data">
                <div class="no-data-icon">🎬</div>
                <p>暂无视频数据</p>
                <p class="no-data-hint">请先获取数据或检查网络连接</p>
              </div>
            </div>

            <!-- 粉丝分析 -->
            <div v-if="activeTab === 'fan'" class="tab-panel">
              <h3>粉丝分析</h3>
              <div v-if="fanData" class="analysis-content">
                <div class="data-summary">
                  <p class="summary-text">粉丝分析数据已加载，包含粉丝画像和行为分析</p>
                </div>
                <div class="chart-container">
                  <div ref="fanChart" class="chart"></div>
                </div>
                <details class="raw-data-details">
                  <summary>查看原始数据</summary>
                  <pre class="raw-data">{{ JSON.stringify(fanData, null, 2) }}</pre>
                </details>
              </div>
              <div v-else class="no-data">
                <div class="no-data-icon">👥</div>
                <p>暂无粉丝分析数据</p>
                <p class="no-data-hint">请先获取数据或检查网络连接</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无数据状态 -->
      <div v-if="!overviewData && !attentionData && !archiveData && !videoData && !fanData" class="no-data-container">
        <div class="no-data-icon">📊</div>
        <h3>暂无数据</h3>
        <p>请检查用户ID是否正确，或者尝试选择不同的时间范围</p>
        <p class="no-data-hint">提示：确保后端服务正常运行，并且数据库中有相关数据</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'
import { api } from '../api';
import * as echarts from 'echarts'
import type {
  CreatorOverviewStat,
  CreatorAttentionAnalyze,
  CreatorArchiveAnalyze,
  CreatorVideoOverview,
  CreatorFanOverview
} from '../types/interfaces'

const loading = ref(false)
const error = ref('')
const currentUid = ref('401315430') // default uid
const queryType = ref('overview')
const startDate = ref('')
const endDate = ref('')
const singleDate = ref('')
const activeTab = ref('attention')

const overviewChart = ref<HTMLElement>()
const attentionChart = ref<HTMLElement>()
const archiveChart = ref<HTMLElement>()
const videoChart = ref<HTMLElement>()
const fanChart = ref<HTMLElement>()

let chartInstances: { [key: string]: echarts.ECharts | null } = {
  overview: null,
  attention: null,
  archive: null,
  video: null,
  fan: null
}

const overviewData = ref<CreatorOverviewStat | null>(null)
const attentionData = ref<CreatorAttentionAnalyze | null>(null)
const archiveData = ref<CreatorArchiveAnalyze | null>(null)
const videoData = ref<CreatorVideoOverview | null>(null)
const fanData = ref<CreatorFanOverview | null>(null)

const analysisTabs = [
  { key: 'attention', label: '涨粉分析' },
  { key: 'archive', label: '播放分析' },
  { key: 'video', label: '视频数据' },
  { key: 'fan', label: '粉丝分析' }
]

// 计算属性
const today = computed(() => {
  return new Date().toISOString().split('T')[0]
})

const isFormValid = computed(() => {
  if (!currentUid.value.trim()) return false

  if (queryType.value === 'date-range') {
    return startDate.value && endDate.value && startDate.value <= endDate.value
  }

  if (queryType.value === 'single-date') {
    return singleDate.value
  }

  return true
})

// 工具函数
const formatNumber = (num: number | null | undefined): string => {
  if (num === null || num === undefined || isNaN(num)) {
    return '0'
  }

  if (num >= 100000000) {
    return (num / 100000000).toFixed(1) + '亿'
  } else if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}

// 获取变化状态的CSS类
const getChangeClass = (value: number | null | undefined): string => {
  if (value === null || value === undefined || isNaN(value)) {
    return 'neutral'
  }

  if (value > 0) {
    return 'positive'
  } else if (value < 0) {
    return 'negative'
  }
  return 'neutral'
}

// 验证数据完整性
const validateData = (data: any): boolean => {
  if (!data) return false

  // 检查是否为空对象
  if (typeof data === 'object' && Object.keys(data).length === 0) {
    return false
  }

  return true
}

const clearError = () => {
  error.value = ''
}

// 快速日期范围选择
const setQuickDateRange = (days: number) => {
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - days)

  endDate.value = end.toISOString().split('T')[0]
  startDate.value = start.toISOString().split('T')[0]
}

// 快速单日选择
const setQuickSingleDate = (daysAgo: number) => {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  singleDate.value = date.toISOString().split('T')[0]
}

// 初始化图表
const initChart = async (chartType: string = 'overview') => {
  await nextTick()

  const chartRefs = {
    overview: overviewChart,
    attention: attentionChart,
    archive: archiveChart,
    video: videoChart,
    fan: fanChart
  }

  const chartRef = chartRefs[chartType as keyof typeof chartRefs]

  if (chartRef?.value && !chartInstances[chartType]) {
    chartInstances[chartType] = echarts.init(chartRef.value)

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      Object.values(chartInstances).forEach(instance => {
        instance?.resize()
      })
    })
  }
}

// 更新概览图表数据
const updateOverviewChart = (data: CreatorOverviewStat) => {
  const chartInstance = chartInstances.overview
  if (!chartInstance) return

  const option = {
    title: {
      text: '数据概览',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['总计数据', '新增数据'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['粉丝', '播放', '点赞', '投币', '评论', '收藏', '分享']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + '万'
          }
          return value.toString()
        }
      }
    },
    series: [
      {
        name: '总计数据',
        type: 'bar',
        data: [
          data.total_fans,
          data.total_click,
          data.total_like,
          data.total_coin,
          data.total_reply,
          data.total_fav,
          data.total_share
        ],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#3b82f6' },
            { offset: 1, color: '#1d4ed8' }
          ])
        }
      },
      {
        name: '新增数据',
        type: 'bar',
        data: [
          data.incr_fans,
          data.incr_click,
          data.inc_like,
          data.inc_coin,
          data.incr_reply,
          data.inc_fav,
          data.inc_share
        ],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#10b981' },
            { offset: 1, color: '#059669' }
          ])
        }
      }
    ]
  }

  chartInstance.setOption(option)
}

// 数据处理和合并函数
const parseAndMergeAttentionData = (rawData: any) => {
  if (!rawData || !rawData.data || !Array.isArray(rawData.data)) {
    console.warn('涨粉分析数据格式不正确:', rawData)
    return null
  }

  const mergedData = {
    totalRecords: rawData.data.length,
    dateRange: {
      start: '',
      end: ''
    },
    summary: {
      netFansIncrease: 0,
      visitorPlayCount: 0,
      newFollowCount: 0,
      avgDailyIncrease: 0
    },
    trends: {
      dates: [] as string[],
      netFansData: [] as number[],
      visitorPlayData: [] as number[],
      newFollowData: [] as number[]
    },
    detailedAnalysis: [] as any[]
  }

  try {
    // 处理每条记录
    rawData.data.forEach((record: any, index: number) => {
      if (!record.analyze_data) return

      let analyzedData: any
      try {
        // 解析 analyze_data JSON 字符串
        analyzedData = typeof record.analyze_data === 'string'
          ? JSON.parse(record.analyze_data)
          : record.analyze_data
      } catch (parseError) {
        console.error(`解析第${index + 1}条记录的analyze_data失败:`, parseError)
        return
      }

      // 提取日期信息
      const recordDate = new Date(record.create_time * 1000).toISOString().split('T')[0]
      mergedData.trends.dates.push(recordDate)

      // 提取关键指标数据
      if (analyzedData) {
        // 净增粉丝数据
        const netFans = analyzedData.net_fans_increase || analyzedData.fans_increase || 0
        mergedData.trends.netFansData.push(netFans)
        mergedData.summary.netFansIncrease += netFans

        // 游客播放数据
        const visitorPlay = analyzedData.visitor_play_count || analyzedData.play_count || 0
        mergedData.trends.visitorPlayData.push(visitorPlay)
        mergedData.summary.visitorPlayCount += visitorPlay

        // 新增关注数据
        const newFollow = analyzedData.new_follow_count || analyzedData.follow_count || 0
        mergedData.trends.newFollowData.push(newFollow)
        mergedData.summary.newFollowCount += newFollow

        // 保存详细分析数据
        mergedData.detailedAnalysis.push({
          date: recordDate,
          createTime: record.create_time,
          rawData: analyzedData,
          processedData: {
            netFansIncrease: netFans,
            visitorPlayCount: visitorPlay,
            newFollowCount: newFollow
          }
        })
      }
    })

    // 计算日期范围
    if (mergedData.trends.dates.length > 0) {
      const sortedDates = [...mergedData.trends.dates].sort()
      mergedData.dateRange.start = sortedDates[0]
      mergedData.dateRange.end = sortedDates[sortedDates.length - 1]
    }

    // 计算平均值
    const recordCount = mergedData.detailedAnalysis.length
    if (recordCount > 0) {
      mergedData.summary.avgDailyIncrease = Math.round(mergedData.summary.netFansIncrease / recordCount)
    }

    console.log('涨粉分析数据合并完成:', mergedData)
    return mergedData

  } catch (error) {
    console.error('合并涨粉分析数据时发生错误:', error)
    return null
  }
}

// 更新涨粉分析图表
const updateAttentionChart = (mergedData: any) => {
  const chartInstance = chartInstances.attention
  if (!chartInstance || !mergedData) return

  const option = {
    title: {
      text: '涨粉趋势分析',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: (params: any) => {
        let result = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].axisValue}</div>`
        params.forEach((param: any) => {
          const value = param.value !== null ? formatNumber(param.value) : '无数据'
          result += `<div style="margin: 4px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; margin-right: 8px;"></span>
            ${param.seriesName}: ${value}
          </div>`
        })
        return result
      }
    },
    legend: {
      data: ['净增粉丝', '游客播放', '新增关注'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: mergedData.trends.dates,
      axisLabel: {
        rotate: 45,
        formatter: (value: string) => {
          // 格式化日期显示
          const date = new Date(value)
          return `${date.getMonth() + 1}/${date.getDate()}`
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '粉丝数',
        position: 'left',
        axisLabel: {
          formatter: (value: number) => formatNumber(value)
        }
      },
      {
        type: 'value',
        name: '播放/关注数',
        position: 'right',
        axisLabel: {
          formatter: (value: number) => formatNumber(value)
        }
      }
    ],
    series: [
      {
        name: '净增粉丝',
        type: 'line',
        yAxisIndex: 0,
        data: mergedData.trends.netFansData,
        itemStyle: {
          color: '#3b82f6'
        },
        lineStyle: {
          width: 3
        },
        symbol: 'circle',
        symbolSize: 6,
        smooth: true
      },
      {
        name: '游客播放',
        type: 'line',
        yAxisIndex: 1,
        data: mergedData.trends.visitorPlayData,
        itemStyle: {
          color: '#10b981'
        },
        lineStyle: {
          width: 2
        },
        symbol: 'diamond',
        symbolSize: 5,
        smooth: true
      },
      {
        name: '新增关注',
        type: 'line',
        yAxisIndex: 1,
        data: mergedData.trends.newFollowData,
        itemStyle: {
          color: '#f59e0b'
        },
        lineStyle: {
          width: 2
        },
        symbol: 'triangle',
        symbolSize: 5,
        smooth: true
      }
    ]
  }

  chartInstance.setOption(option)
}

// 更新其他图表的通用方法
const updateAnalysisChart = (chartType: string, data: any, title: string) => {
  const chartInstance = chartInstances[chartType]
  if (!chartInstance || !data) return

  // 简单的数据可视化，显示数据结构
  const option = {
    title: {
      text: title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: title,
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1, name: '数据已加载' },
          { value: 0, name: '等待分析' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)
}

// 获取数据的主要函数
const fetchData = async () => {
  if (!currentUid.value.trim()) {
    error.value = '请输入有效的用户ID'
    return
  }

  // 验证日期范围
  if (queryType.value === 'date-range') {
    if (!startDate.value || !endDate.value) {
      error.value = '请选择开始和结束日期'
      return
    }
    if (startDate.value > endDate.value) {
      error.value = '开始日期不能晚于结束日期'
      return
    }

    // 检查日期范围是否过大（超过1年）
    const start = new Date(startDate.value)
    const end = new Date(endDate.value)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays > 365) {
      error.value = '日期范围不能超过365天'
      return
    }
  }

  if (queryType.value === 'single-date') {
    if (!singleDate.value) {
      error.value = '请选择查询日期'
      return
    }

    // 检查日期是否为未来日期
    const selectedDate = new Date(singleDate.value)
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    if (selectedDate > today) {
      error.value = '不能查询未来日期的数据'
      return
    }
  }

  loading.value = true
  error.value = ''

  try {
    // 根据查询类型获取不同的数据
    if (queryType.value === 'overview') {
      await fetchOverviewData()
    } else if (queryType.value === 'date-range') {
      await fetchDateRangeData()
    } else if (queryType.value === 'single-date') {
      await fetchSingleDateData()
    }
  } catch (err: any) {
    console.error('Error fetching data:', err)

    // 根据错误类型提供更友好的错误信息
    if (err.response?.status === 404) {
      error.value = '未找到该用户的数据，请检查用户ID是否正确'
    } else if (err.response?.status === 500) {
      error.value = '服务器内部错误，请稍后重试'
    } else if (err.response?.status === 403) {
      error.value = '访问被拒绝，请检查权限设置'
    } else if (err.code === 'NETWORK_ERROR' || err.message?.includes('Network Error')) {
      error.value = '网络连接失败，请检查网络设置和后端服务是否正常运行'
    } else if (err.message?.includes('timeout')) {
      error.value = '请求超时，请稍后重试'
    } else {
      error.value = err.message || '获取数据失败，请稍后重试'
    }

    // 清空数据
    overviewData.value = null
    attentionData.value = null
    archiveData.value = null
    videoData.value = null
    fanData.value = null
  } finally {
    loading.value = false
  }
}

// 获取概览数据
const fetchOverviewData = async () => {
  try {
    // 使用默认的日期范围查询（最近30天）
    const uid = currentUid.value
    const today = new Date()
    const thirtyDaysAgo = new Date(today)
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const endDateStr = today.toISOString().split('T')[0]
    const startDateStr = thirtyDaysAgo.toISOString().split('T')[0]

    console.log(`获取概览数据: uid=${uid}, 时间范围=${startDateStr} 到 ${endDateStr}`)

    const [overview, attention, archive, video, fan] = await Promise.all([
      api.getCreatorOverviewStatByDateRange(uid, startDateStr, endDateStr),
      api.getCreatorAttentionAnalyzeByDateRange(uid, startDateStr, endDateStr),
      api.getCreatorArchiveAnalyzeByDateRange(uid, startDateStr, endDateStr),
      api.getCreatorVideoCompareByDateRange(uid, startDateStr, endDateStr),
      api.getCreatorFanOverviewByDateRange(uid, startDateStr, endDateStr)
    ])

    console.log('API 响应数据:', { overview, attention, archive, video, fan })

    // 处理概览数据 - 取最新的一条记录
    if (overview && overview.data && overview.data.length > 0) {
      overviewData.value = overview.data[0]
      console.log('概览数据设置成功:', overviewData.value)
    } else {
      console.warn('概览数据为空或格式不正确:', overview)
      overviewData.value = null
    }

    // 处理涨粉分析数据 - 使用新的数据处理函数
    if (attention && attention.data) {
      const mergedAttentionData = parseAndMergeAttentionData(attention)
      attentionData.value = {
        ...attention,
        mergedData: mergedAttentionData
      }
      if (mergedAttentionData) {
        console.log('涨粉分析数据处理完成:', mergedAttentionData)
      }
    } else {
      attentionData.value = null
    }

    // 处理其他数据
    archiveData.value = archive && archive.data ? archive : null
    videoData.value = video && video.data ? video : null
    fanData.value = fan && fan.data ? fan : null

    console.log('所有数据处理完成:', {
      overview: overviewData.value,
      attention: attentionData.value,
      archive: archiveData.value,
      video: videoData.value,
      fan: fanData.value
    })

    // 初始化图表
    if (overviewData.value) {
      await initChart('overview')
      updateOverviewChart(overviewData.value)
    }

    if (attentionData.value && attentionData.value.mergedData) {
      await initChart('attention')
      updateAttentionChart(attentionData.value.mergedData)
    }

    if (archiveData.value) {
      await initChart('archive')
      updateAnalysisChart('archive', archiveData.value, '播放分析')
    }

    if (videoData.value) {
      await initChart('video')
      updateAnalysisChart('video', videoData.value, '视频数据')
    }

    if (fanData.value) {
      await initChart('fan')
      updateAnalysisChart('fan', fanData.value, '粉丝分析')
    }
  } catch (err) {
    console.error('获取概览数据时发生错误:', err)
    throw err
  }
}

const fetchDateRangeData = async () => {
  try {
    const uid = currentUid.value
    const start = startDate.value
    const end = endDate.value

    console.log(`获取日期范围数据: uid=${uid}, 时间范围=${start} 到 ${end}`)

    const [overview, attention, archive, video, fan] = await Promise.all([
      api.getCreatorOverviewStatByDateRange(uid, start, end),
      api.getCreatorAttentionAnalyzeByDateRange(uid, start, end),
      api.getCreatorArchiveAnalyzeByDateRange(uid, start, end),
      api.getCreatorVideoCompareByDateRange(uid, start, end),
      api.getCreatorFanOverviewByDateRange(uid, start, end)
    ])

    console.log('日期范围API响应数据:', { overview, attention, archive, video, fan })

    // 处理概览数据 - 取最新的一条记录
    if (overview && overview.data && overview.data.length > 0) {
      overviewData.value = overview.data[0]
      console.log('日期范围概览数据设置成功:', overviewData.value)
    } else {
      console.warn('日期范围概览数据为空或格式不正确:', overview)
      overviewData.value = null
    }

    // 处理涨粉分析数据 - 使用新的数据处理函数
    if (attention && attention.data) {
      const mergedAttentionData = parseAndMergeAttentionData(attention)
      attentionData.value = {
        ...attention,
        mergedData: mergedAttentionData
      }
      if (mergedAttentionData) {
        console.log('日期范围涨粉分析数据处理完成:', mergedAttentionData)
      }
    } else {
      attentionData.value = null
    }

    // 处理其他数据
    archiveData.value = archive && archive.data ? archive : null
    videoData.value = video && video.data ? video : null
    fanData.value = fan && fan.data ? fan : null

    // 初始化图表
    if (overviewData.value) {
      await initChart('overview')
      updateOverviewChart(overviewData.value)
    }

    if (attentionData.value && attentionData.value.mergedData) {
      await initChart('attention')
      updateAttentionChart(attentionData.value.mergedData)
    }

    if (archiveData.value) {
      await initChart('archive')
      updateAnalysisChart('archive', archiveData.value, '播放分析')
    }

    if (videoData.value) {
      await initChart('video')
      updateAnalysisChart('video', videoData.value, '视频数据')
    }

    if (fanData.value) {
      await initChart('fan')
      updateAnalysisChart('fan', fanData.value, '粉丝分析')
    }
  } catch (err) {
    console.error('获取日期范围数据时发生错误:', err)
    throw err
  }
}

const fetchSingleDateData = async () => {
  try {
    const uid = currentUid.value
    const date = singleDate.value

    console.log(`获取单日数据: uid=${uid}, 日期=${date}`)

    const [overview, attention, archive, video, fan] = await Promise.all([
      api.getCreatorOverviewStatByDate(uid, date),
      api.getCreatorAttentionAnalyzeByDate(uid, date),
      api.getCreatorArchiveAnalyzeByDate(uid, date),
      api.getCreatorVideoCompareByDate(uid, date),
      api.getCreatorFanOverviewByDate(uid, date)
    ])

    console.log('单日API响应数据:', { overview, attention, archive, video, fan })

    // 处理概览数据 - 取最新的一条记录
    if (overview && overview.data && overview.data.length > 0) {
      overviewData.value = overview.data[0]
      console.log('单日概览数据设置成功:', overviewData.value)
    } else {
      console.warn('单日概览数据为空或格式不正确:', overview)
      overviewData.value = null
    }

    // 处理涨粉分析数据 - 使用新的数据处理函数
    if (attention && attention.data) {
      const mergedAttentionData = parseAndMergeAttentionData(attention)
      attentionData.value = {
        ...attention,
        mergedData: mergedAttentionData
      }
      if (mergedAttentionData) {
        console.log('单日涨粉分析数据处理完成:', mergedAttentionData)
      }
    } else {
      attentionData.value = null
    }

    // 处理其他数据
    archiveData.value = archive && archive.data ? archive : null
    videoData.value = video && video.data ? video : null
    fanData.value = fan && fan.data ? fan : null

    // 初始化图表
    if (overviewData.value) {
      await initChart('overview')
      updateOverviewChart(overviewData.value)
    }

    if (attentionData.value && attentionData.value.mergedData) {
      await initChart('attention')
      updateAttentionChart(attentionData.value.mergedData)
    }

    if (archiveData.value) {
      await initChart('archive')
      updateAnalysisChart('archive', archiveData.value, '播放分析')
    }

    if (videoData.value) {
      await initChart('video')
      updateAnalysisChart('video', videoData.value, '视频数据')
    }

    if (fanData.value) {
      await initChart('fan')
      updateAnalysisChart('fan', fanData.value, '粉丝分析')
    }
  } catch (err) {
    console.error('获取单日数据时发生错误:', err)
    throw err
  }
}

onMounted(() => {
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  endDate.value = today.toISOString().split('T')[0]
  startDate.value = yesterday.toISOString().split('T')[0]
  singleDate.value = yesterday.toISOString().split('T')[0]

  runBasicTests()
})

const runBasicTests = () => {
  console.log('🧪 Running CreatorInfo component tests...')

  const testNumbers = [0, 1234, 12345, 123456789, null, undefined, NaN]
  console.log('📊 Testing formatNumber function:')
  testNumbers.forEach(num => {
    const formatted = formatNumber(num)
    console.log(`  formatNumber(${num}) = "${formatted}"`)
  })

  const testValues = [10, -5, 0, null, undefined]
  console.log('🎨 Testing getChangeClass function:')
  testValues.forEach(val => {
    const className = getChangeClass(val)
    console.log(`  getChangeClass(${val}) = "${className}"`)
  })

  const testData = [{}, null, undefined, { test: 'data' }, []]
  console.log('✅ Testing validateData function:')
  testData.forEach(data => {
    const isValid = validateData(data)
    console.log(`  validateData(${JSON.stringify(data)}) = ${isValid}`)
  })

  console.log('📝 Testing form validation:')
  console.log(`  isFormValid (initial) = ${isFormValid.value}`)

  console.log('✨ All basic tests completed successfully!')
}
</script>

<style scoped>
.creator-info {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 10px 0;
}

.page-description {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
}

.usage-guide {
  margin-top: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
}

.usage-guide summary {
  background: #e5e7eb;
  padding: 12px 16px;
  cursor: pointer;
  font-weight: 600;
  color: #374151;
  user-select: none;
}

.usage-guide summary:hover {
  background: #d1d5db;
}

.guide-content {
  padding: 16px;
  background: white;
}

.guide-content h4 {
  margin: 0 0 12px 0;
  color: #1f2937;
}

.guide-content ol {
  margin: 0 0 16px 0;
  padding-left: 20px;
}

.guide-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.guide-content li {
  margin: 6px 0;
  line-height: 1.5;
}

.guide-content p {
  margin: 12px 0 0 0;
  color: #6b7280;
  font-style: italic;
}

/* 控制面板 */
.control-panel {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: end;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.uid-input,
.date-type-select,
.date-input {
  padding: 10px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.uid-input:focus,
.date-type-select:focus,
.date-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.date-range-group,
.single-date-group {
  display: flex;
  gap: 16px;
  align-items: end;
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-date-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.quick-date-btn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-date-btn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.fetch-button {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  height: fit-content;
}

.fetch-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.fetch-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误信息 */
.error-container {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  text-align: center;
}

.error-message {
  color: #dc2626;
  margin: 0 0 12px 0;
  font-weight: 500;
}

.clear-error-button {
  background: #dc2626;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
}

/* 数据容器 */
.data-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* 概览统计 */
.overview-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 20px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  animation: slideInUp 0.6s ease-out;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 12px;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 0.9rem;
  color: #6b7280;
  margin-bottom: 8px;
}

.stat-change {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-block;
}

.stat-change.positive {
  background: #dcfce7;
  color: #16a34a;
}

.stat-change.negative {
  background: #fee2e2;
  color: #dc2626;
}

.stat-change.neutral {
  background: #f3f4f6;
  color: #6b7280;
}

/* 图表部分 */
.charts-section {
  margin-top: 30px;
  padding-top: 30px;
  border-top: 1px solid #e5e7eb;
}

.chart-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 20px 0;
}

.chart-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.chart {
  width: 100%;
  height: 400px;
}

/* 分析部分 */
.analysis-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-container {
  width: 100%;
}

.tab-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
}

.tab-button {
  flex: 1;
  padding: 16px 20px;
  background: none;
  border: none;
  font-size: 1rem;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.tab-button:hover {
  background: #e5e7eb;
  color: #374151;
}

.tab-button.active {
  background: white;
  color: #3b82f6;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #3b82f6;
}

.tab-content {
  padding: 24px;
}

.tab-panel {
  min-height: 300px;
  animation: fadeInContent 0.4s ease-out;
}

@keyframes fadeInContent {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.tab-panel h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 20px 0;
}

.analysis-content {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.data-summary {
  background: #e0f2fe;
  border-left: 4px solid #0288d1;
  padding: 16px;
  margin-bottom: 20px;
  border-radius: 0 8px 8px 0;
}

.summary-text {
  margin: 0;
  color: #01579b;
  font-weight: 500;
}

.raw-data-details {
  margin-top: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.raw-data-details summary {
  background: #f8f9fa;
  padding: 12px 16px;
  cursor: pointer;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.raw-data-details summary:hover {
  background: #e5e7eb;
}

.raw-data {
  background: #1f2937;
  color: #f9fafb;
  padding: 16px;
  margin: 0;
  overflow-x: auto;
  font-size: 0.85rem;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
}

/* 涨粉分析摘要卡片 */
.attention-summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.summary-card {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  border: 1px solid #e0f2fe;
  transition: all 0.3s ease;
  cursor: pointer;
}

.summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
}

.summary-icon {
  font-size: 2rem;
  margin-bottom: 12px;
}

.summary-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1e40af;
  margin-bottom: 8px;
}

.summary-label {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
}

.no-data {
  text-align: center;
  color: #6b7280;
  padding: 60px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #d1d5db;
}

.no-data-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.no-data p {
  margin: 8px 0;
}

.no-data-hint {
  font-size: 0.9rem;
  color: #9ca3af;
  font-style: italic;
}

.no-data-container {
  text-align: center;
  color: #6b7280;
  padding: 80px 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #d1d5db;
  margin: 20px 0;
}

.no-data-container .no-data-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.no-data-container h3 {
  font-size: 1.5rem;
  margin: 0 0 16px 0;
  color: #374151;
}

.no-data-container p {
  margin: 8px 0;
  line-height: 1.6;
}

.no-data-container .no-data-hint {
  font-size: 0.9rem;
  color: #9ca3af;
  font-style: italic;
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .creator-info {
    padding: 16px;
  }

  .page-title {
    font-size: 2rem;
  }

  .control-panel {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .date-range-group,
  .single-date-group {
    flex-direction: column;
    align-items: stretch;
  }

  .quick-date-buttons {
    justify-content: center;
    margin-top: 12px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .tab-header {
    flex-wrap: wrap;
  }

  .tab-button {
    flex: none;
    min-width: 50%;
  }

  .chart {
    height: 300px;
  }

  .attention-summary-cards {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.8rem;
  }

  .page-description {
    font-size: 1rem;
  }

  .control-panel {
    padding: 16px;
  }

  .stat-value {
    font-size: 1.5rem;
  }

  .stat-icon {
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .tab-button {
    min-width: 100%;
    text-align: left;
    padding: 12px 16px;
  }

  .chart {
    height: 250px;
  }

  .quick-date-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .quick-date-btn {
    width: 100%;
    margin-bottom: 8px;
  }

  .attention-summary-cards {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .summary-card {
    padding: 16px;
  }

  .summary-value {
    font-size: 1.5rem;
  }

  .summary-icon {
    font-size: 1.5rem;
  }
}
</style>
