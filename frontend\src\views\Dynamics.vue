<template>
  <div>
      <div class="flex justify-between items-center mb-6">
        <h2
          class="text-3xl font-bold text-gray-800 dark:text-white flex items-center"
        >
          动态分析
          <div class="relative group ml-2">
            <svg
              class="w-4 h-4 text-gray-500 cursor-pointer"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M18 10c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm-8-6a6 6 0 100 12A6 6 0 0010 4zm0 9a1 1 0 110-2 1 1 0 010 2zm0-4a1 1 0 01-1-1V7a1 1 0 112 0v1a1 1 0 01-1 1z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <div
              class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap"
            >
              热度值通过转发、评论、点赞三项计算
            </div>
          </div>
        </h2>

          <!-- Enable drag switch -->
          <div class="flex items-center">
            <span class="mr-2 text-gray-700 dark:text-gray-300">允许拖拽</span>
            <label class="switch">
              <input type="checkbox" v-model="isDraggable" />
              <span class="slider round"></span>
            </label>
          </div>
        </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
      <h3 class="text-xl font-bold mb-4 text-gray-800 dark:text-white">
        选选想看的时间范围吧
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >起始日期</label
          >
          <input
            type="date"
            v-model="startDate"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >结束日期</label
          >
          <input
            type="date"
            v-model="endDate"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >动态数</label
          >
          <input
            type="number"
            v-model="topN"
            min="1"
            max="100"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
      </div>
      <div class="flex items-center mt-4 justify-between">
      <button
        @click="fetchData"
        class="mt-4 px-4 py-2 bg-pink-400 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
      >
        更新数据
      </button>
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
            <label for="sort-by"class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >排序方式:</label>
            <select
              id="sort-by"
              v-model="sortKey"
              class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white py-1 pl-2 pr-8 text-sm"
            >
              <option value="pubtime">按发布时间</option>
              <option value="heat">按热度</option>
              <option value="share_num">按分享数</option>
              <option value="comment_num">按评论数</option>
              <option value="like_num">按喜欢数</option>
            </select>
          </div>
          <!-- Sort order selection -->
          <div class="relative">
            <label for="sort-order" class="text-sm font-medium text-gray-700 dark:text-gray-300"
            >排序方向:</label>
            <select
              id="sort-order"
              v-model="sortOrder"
              class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white py-1 pl-2 pr-8 text-sm"
            >
              <option value="desc">倒序</option>
              <option value="asc">正序</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <div v-if="loading" class="text-center text-gray-600 dark:text-gray-300">
      <p>正在赶来的路上...</p>
    </div>
    <div v-else-if="error" class="text-center text-red-600 dark:text-red-400">
      <p>{{ error }}</p>
    </div>
    <div
      v-else-if="sortedDynamics.length === 0"
      class="text-center text-gray-600 dark:text-gray-300"
    >
      <p>没有找到动态捏</p>
    </div>
    <div v-else>
      <VueDraggable
        v-model="sortedDynamics"
        item-key="id"
        :disabled="!isDraggable"
        :key="'dynamics-' + (isDraggable ? 'draggable' : 'static')"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        <template #item="{ element }">
          <div
            class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
            :class="{
              'cursor-move': isDraggable,
              'cursor-default': !isDraggable,
            }"
          >
            <div class="p-4 flex flex-col h-full">
              <div class="flex items-center mb-4">
                <img
                  :src="basicInfo.face"
                  :alt="element.name"
                  class="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <h3
                    class="font-semibold text-lg text-gray-800 dark:text-white"
                  >
                    {{ element.name }}
                  </h3>
                  <p class="text-gray-500 dark:text-gray-400 text-sm">
                    {{ element.pubtime }}
                  </p>
                </div>
              </div>
              <p
                class="text-gray-700 dark:text-gray-300 mb-3 flex-grow"
                :class="{
                  'text-lg': element.content.length < 100,
                  'text-xl': element.content.length < 50,
                }"
              >
                {{ element.content }}
              </p>
              <div
                class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mt-auto mb-1"
              >
              <span class="flex items-center mr-4">
                  <i class="fas fa-share-alt mr-1"></i> <!-- Share icon -->
                  {{ element.share_num }}
                </span>
                <span class="flex items-center mr-4">
                  <i class="fas fa-comment mr-1"></i> <!-- Comment icon -->
                  {{ element.comment_num }}
                </span>
                <span class="flex items-center">
                  <i class="fas fa-thumbs-up mr-1"></i> <!-- Like icon -->
                  {{ element.like_num }}
                </span>
              </div>
              <div
                class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mt-auto"
              >
                <a
                  :href="element.url"
                  target="_blank"
                  class="flex items-center hover:text-blue-500"
                >
                  <i class="fas fa-external-link-alt mr-1"></i>
                  Link
                </a>
                <span v-if="element.topic" class="flex items-center">
                  <i class="fas fa-hashtag mr-1"></i>
                  {{ element.topic }}
                </span>
                <span class="flex items-center">
                  <i class="fas fa-fire mr-1"></i> <!-- Heat icon -->
                  {{ element.heat }}
                </span>
              </div>
            </div>
          </div>
        </template>
      </VueDraggable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, watch } from 'vue'
import type { Ref } from 'vue'
import { api } from '../api'
import VueDraggable from 'vuedraggable'
import dayjs from 'dayjs'

interface BasicInfo {
  name: string
  face: string
}

interface Dynamic {
  id: number
  name: string
  content: string
  topic: string
  url: string
  heat: number
  pubtime: string
  share_num: number,
  comment_num: number,
  like_num: number
}

const isLoading = inject('isLoading') as Ref<boolean>
const selectedVTuber = inject('selectedVTuber') as Ref<string>
const isDarkMode = inject('isDarkMode') as Ref<boolean>
const loading = ref(true)
const error = ref('')
const dynamics = ref<Dynamic[]>([])
const basicInfo = ref<BasicInfo>({
  name: '',
  face: '',
})
const isDraggable = ref(false);

const startDate = ref(dayjs().subtract(90, 'day').format('YYYY-MM-DD'));
const endDate = ref(dayjs().format('YYYY-MM-DD'));
const topN = ref(12);

const sortKey = ref('pubtime'); // 排序键，默认为发布时间
const sortOrder = ref('desc'); // 排序顺序，默认为倒序 (desc)

const fetchData = async () => {
  try {
    isLoading.value = true
    loading.value = true
    error.value = ''
    const [dynamicsData, basicInfoData] = await Promise.all([
      api.getTopNDynamics(
        selectedVTuber.value,
        startDate.value,
        endDate.value,
        topN.value
      ),
      api.getBasicInfo(selectedVTuber.value),
    ])
    dynamics.value = dynamicsData
    basicInfo.value = {
      name: basicInfoData.name,
      face: basicInfoData.face,
    }
  } catch (err) {
    console.error('Error fetching dynamics:', err)
    error.value = '服务器开小差了😴，请稍后再试'
  } finally {
    isLoading.value = false
    loading.value = false
  }
}

onMounted(async () => {
  // const vtuberParam = route.params.vtuber
  // console.log('Initial route vtuber param:', vtuberParam)
  // if (vtuberParam) {
  //   selectedVTuber.value = vtuberParam as string
  // }
  await fetchData()
})

import { computed } from 'vue'; // 导入 computed

watch(selectedVTuber, fetchData); // 监听 selectedVTuber 变化并重新获取数据

// 根据 sortKey 和 sortOrder 对 dynamics 进行排序的计算属性
const sortedDynamics = computed(() => {
  if (!dynamics.value) return []; // 如果 dynamics 为空，则返回空数组
  const sorted = [...dynamics.value].sort((a, b) => {
    let comparison = 0;
    switch (sortKey.value) {
      case 'pubtime':
        comparison = new Date(a.pubtime).getTime() - new Date(b.pubtime).getTime(); // 按发布时间
        break;
      case 'heat':
        comparison = a.heat - b.heat; // 按热度
        break;
      case 'share_num':
        comparison = a.share_num - b.share_num; // 按分享数
        break;
      case 'comment_num':
        comparison = a.comment_num - b.comment_num; // 按评论数
        break;
      case 'like_num':
        comparison = a.like_num - b.like_num; // 按喜欢数
        break;
      default:
        comparison = 0; // 默认不排序
    }
    return sortOrder.value === 'asc' ? comparison : -comparison; // 根据排序顺序返回
  });
  return sorted;
});
</script>

<style scoped>
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: '';
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #2196f3;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}
</style>
