{"name": "bi-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/chart.js": "^2.9.41", "@types/dompurify": "^3.0.5", "@types/marked": "^5.0.2", "@vuepic/vue-datepicker": "^10.0.0", "axios": "^1.6.2", "chart.js": "^4.4.1", "core-js": "^3.34.0", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "echarts": "^5.5.1", "element-plus": "^2.9.0", "marked": "^15.0.12", "pinia": "^2.2.6", "vue": "^3.5.13", "vue-calendar-3": "^2.6.6", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-typescript": "^11.0.3", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "~5.3.3", "vite": "^5.0.8", "vue-tsc": "^2.1.10"}}