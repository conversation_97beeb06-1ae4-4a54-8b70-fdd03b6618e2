from bilibili_api import comment, sync
from bilibili_api import user, sync, Credential, live
from const import (
    BUVID4,
    SESSDATA,
    BILI_JCT,
    PROJECT_ROOT,
    BUVID3,
    DEDEUSERID,
    TIEBA_LIST,
    PGSQL_CONFIG,
    BDUSS,
    VTUBER_NAME_DICT,
)

credential = Credential(
                sessdata=SESSDATA,
                bili_jct=BILI_JCT,
                buvid3=BUVID3,
                dedeuserid=DEDEUSERID,
                buvid4=BUVID4,
            )


async def main():
    # 存储评论
    comments = []
    # 刷新次数（约等于页码）
    page = 1
    # 每次提供的 offset (pagination_str)
    pag = ""

    while True:
        # 获取评论
        c = await comment.get_comments(346611208, comment.CommentResourceType.DYNAMIC_DRAW, page, comment.OrderType.TIME, credential )

        print(c)

        break
        replies = c['replies']
        if replies is None:
            # 未登陆时只能获取到前20条评论
            # 此时增加页码会导致c为空字典
            break

        # 存储评论
        comments.extend(replies)
        # 增加页码
        page += 1

        # 只刷新 5 次（约等于 5 页）
        if page > 5:
            break

    # 打印评论
    for cmt in comments:
        print(f"{cmt['member']['uname']}: {cmt['content']['message']}")

    # 打印评论总数
    print(f"\n\n共有 {len(comments)} 条评论（不含子评论）")


sync(main())