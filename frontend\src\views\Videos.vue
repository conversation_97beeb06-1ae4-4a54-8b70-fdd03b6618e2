<template>
  <div class="space-y-8">
    <div class="flex justify-between items-center mb-6">
      <h2
        class="text-3xl font-bold text-gray-800 dark:text-white flex items-center"
      >
        视频分析
        <div class="relative group ml-2">
          <svg
            class="w-4 h-4 text-gray-500 cursor-pointer"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              d="M18 10c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm-8-6a6 6 0 100 12A6 6 0 0010 4zm0 9a1 1 0 110-2 1 1 0 010 2zm0-4a1 1 0 01-1-1V7a1 1 0 112 0v1a1 1 0 01-1 1z"
              clip-rule="evenodd"
            ></path>
          </svg>
          <div
            class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap"
          >
            热度值通过播放量、评论、点赞、投币、收藏和热门标签六项计算
          </div>
        </div>
      </h2>
      <div class="flex items-center">
        <span class="mr-2 text-gray-700 dark:text-gray-300">Enable drag:</span>
        <label class="switch">
          <input type="checkbox" v-model="isDraggable" />
          <span class="slider round"></span>
        </label>
      </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
      <h3 class="text-xl font-bold mb-4 text-gray-800 dark:text-white">
        选选想看的时间范围吧
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >起始日期</label
          >
          <input
            type="date"
            v-model="startDate"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >结束日期</label
          >
          <input
            type="date"
            v-model="endDate"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >视频数</label
          >
          <input
            type="number"
            v-model="topN"
            min="1"
            max="100"
            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
        </div>
      </div>
      <div class="flex items-center mt-4 justify-between">
        <button
          @click="fetchData"
          class="px-4 py-2 bg-pink-400 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
        >
          更新数据
        </button>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <label
              for="sortBy"
              class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >排序方式:</label
            >
            <select
              id="sortBy"
              v-model="sortBy"
              class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white py-1 pl-2 pr-8 text-sm"
            >
              <option value="pubtime">发布时间</option>
              <option value="heat">热度</option>
              <option value="play_num">播放数</option>
              <option value="comment_num">评论数</option>
              <option value="like_num">喜欢数</option>
              <option value="coin_num">投币数</option>
              <option value="favorite_num">收藏数</option>
            </select>
          </div>
          <div class="flex items-center space-x-2">
            <label
              for="sortOrder"
              class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >排序方向:</label
            >
            <select
              id="sortOrder"
              v-model="sortOrder"
              class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white py-1 pl-2 pr-8 text-sm"
            >
              <option value="desc">降序</option>
              <option value="asc">升序</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <div v-if="loading" class="text-center text-gray-600 dark:text-gray-300">
      <p>正在赶来的路上...</p>
    </div>
    <div v-else-if="error" class="text-center text-red-600 dark:text-red-400">
      <p>{{ error }}</p>
    </div>
    <div
      v-else-if="videos.length === 0"
      class="text-center text-gray-600 dark:text-gray-300"
    >
      <p>没找到视频捏.</p>
    </div>
    <div v-else class="flex">
      <div class="w-4/5 pr-4">
        <VueDraggable
          v-model="videos"
          item-key="bvid"
          :disabled="!isDraggable"
          :key="'videos-' + (isDraggable ? 'draggable' : 'static')"
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          :animation="0"
        >
          <template #item="{ element }">
            <div
              class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
              :class="{
                'cursor-move': isDraggable,
                'cursor-default': !isDraggable,
              }"
            >
              <img
                :src="element.face"
                :alt="element.name"
                class="w-full h-48 object-cover cursor-pointer"
                @click="selectVideo(element)"
              />
              <div class="p-4">
                <h3
                  class="font-semibold text-lg text-gray-800 dark:text-white mb-2"
                >
                  {{ element.name }}
                </h3>
                <div
                  class="flex justify-between items-center text-sm text-gray-600 dark:text-gray-400"
                >
                  <span>{{ element.pubtime }}</span>
                  <div class="flex items-center">
                    <i
                      class="fas fa-fire mr-1"
                      :class="getHeatClass(element.heat)"
                    ></i>
                  <span>{{ element.heat }}</span>
                  </div>
                </div>
                <!-- 新增的播放量、评论数、点赞数、投币数、收藏数显示 -->
                <div class="flex flex-wrap items-center text-sm text-gray-600 dark:text-gray-400 mt-2">
                  <div class="flex items-center ml-2 mr-8 mb-1">
                    <i class="fas fa-play-circle mr-1"></i>
                    <span>{{ element.play_num }}</span>
                  </div>
                  <div class="flex items-center mr-8 mb-1">
                    <i class="fas fa-comment mr-1"></i>
                    <span>{{ element.comment_num }}</span>
                  </div>
                  <div class="flex items-center mr-8 mb-1">
                    <i class="fas fa-thumbs-up mr-1"></i>
                    <span>{{ element.like_num }}</span>
                  </div>
                  <div class="flex items-center mr-8 mb-1">
                    <i class="fas fa-coins mr-1"></i>
                    <span>{{ element.coin_num }}</span>
                  </div>
                  <div class="flex items-center mr-8 mb-1">
                    <i class="fas fa-star mr-1"></i>
                    <span>{{ element.favorite_num }}</span>
                  </div>
                </div>
                <div class="flex justify-between items-center mt-4">
                  <a
                    :href="'https://www.bilibili.com/video/' + element.bvid"
                    target="_blank"
                    class="inline-block px-4 py-2 bg-pink-400 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition duration-150 ease-in-out"
                  >
                    前往观看
                  </a>
                  <span
                    v-if="element.honor_count > 0"
                    class="text-sm text-gray-600 dark:text-gray-400 bg-yellow-200 dark:bg-yellow-600 rounded px-2 py-1"
                  >
                    {{ element.honor_short }}
                  </span>
                </div>
              </div>
            </div>
          </template>
        </VueDraggable>
      </div>
      <div class="w-1/4 bg-gray-200 dark:bg-gray-800 rounded-lg shadow-md p-4">
        <div class="flex flex-col space-y-4">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <h3
              class="text-lg font-bold mb-4 text-gray-800 dark:text-white flex items-center"
            >
              视频摘要生成
              <div class="relative group ml-2">
                <svg
                  class="w-4 h-4 text-gray-500 cursor-pointer"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    d="M18 10c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm-8-6a6 6 0 100 12A6 6 0 0010 4zm0 9a1 1 0 110-2 1 1 0 010 2zm0-4a1 1 0 01-1-1V7a1 1 0 112 0v1a1 1 0 01-1 1z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <div
                  class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap"
                >
                  视频摘要当前采用B站AI摘要生成，可能存在部分视频无摘要生成，后续会逐步更新
                </div>
              </div>
            </h3>

            <div v-if="selectedVideoSummary === ''">
              <p class="text-gray-600 dark:text-gray-300">
                该视频还没生成摘要TAT，试试别的吧
              </p>
            </div>
            <div v-else>
              <p class="text-gray-600 dark:text-gray-300 mb-4">
                {{ selectedVideoSummary }}
              </p>
            </div>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <h3
              class="text-lg font-semibold mb-2 text-gray-800 dark:text-white"
            >
              近期播放量趋势
            </h3>
            <div ref="chartContainer" style="width: 100%; height: 300px"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, watch, nextTick, onUnmounted } from 'vue'
import type { Ref } from 'vue'
import { api } from '../api'
import VueDraggable from 'vuedraggable'
import * as echarts from 'echarts'
import dayjs from 'dayjs'

interface Video {
  bvid: string
  name: string
  face: string
  heat: number
  play_num: number
  comment_num: number
  like_num: number
  coin_num: number
  favorite_num: number
  pubtime: string
  honor_short: string
  hornor_count: number
}

const isLoading = inject('isLoading') as Ref<boolean>
const selectedVTuber = inject('selectedVTuber') as Ref<string>
const isDarkMode = inject('isDarkMode') as Ref<boolean>
const loading = ref(true)
const error = ref('')
const videos = ref<Video[]>([])
const isDraggable = ref(false)

const startDate = ref(dayjs().subtract(90, 'day').format('YYYY-MM-DD'))
const endDate = ref(dayjs().format('YYYY-MM-DD'))
const topN = ref(100)
const sortBy = ref('pubtime');
const sortOrder = ref('desc'); // 排序顺序，默认为降序 'desc'，可选 'asc' 升序

// New state for selected video summary and recent views data
const selectedVideoSummary = ref('')
const recentViews = ref<[string, string, string, string][]>([])

// Chart related refs
const chartContainer = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

const fetchData = async () => {
  try {
    isLoading.value = true
    loading.value = true
    error.value = ''
    const videosData = await api.getTopNVideos(
      selectedVTuber.value,
      startDate.value,
      endDate.value,
      topN.value,
      sortBy.value,
      sortOrder.value
    );



    // 根据 sortBy 和 sortOrder 在前端进行排序
    videos.value = videosData.sort((a: Video, b: Video) => {
      let valA: any;
      let valB: any;

      // 处理发布时间排序
      if (sortBy.value === 'pubtime') {
        valA = new Date(a.pubtime);
        valB = new Date(b.pubtime);
      } else {
        // 处理其他数值类型排序
        valA = (a as any)[sortBy.value];
        valB = (b as any)[sortBy.value];
      }

      if (valA < valB) {
        return sortOrder.value === 'asc' ? -1 : 1;
      }
      if (valA > valB) {
        return sortOrder.value === 'asc' ? 1 : -1;
      }
      return 0;
    });
  } catch (err) {
    console.error('Error fetching videos:', err)
    error.value = 'Failed to load videos. Please try again later.'
  } finally {
    isLoading.value = false
    loading.value = false
  }
}

const fetchConclusionData = async (video: Video) => {
  try {
    isLoading.value = true
    loading.value = true
    error.value = ''
    const conclusionData = await api.getVideoConclusion(
      selectedVTuber.value,
      video.bvid
    )
    selectedVideoSummary.value = conclusionData
  } catch (err) {
    console.error('Error fetching conclusion data:', err)
    error.value = 'Failed to load video conclusion. Please try again later.'
  } finally {
    isLoading.value = false
    loading.value = false
  }
}

const fetchKLineData = async (video: Video) => {
  try {
    const kLineData = await api.getRencentVideoDayViews(
      selectedVTuber.value,
      video.bvid,
      30
    )

    if (kLineData.length === 0) {
      error.value = 'No data found for the selected video.'
      return
    }
    kLineData.reverse()
    const dates = kLineData.map(
      (item: any) => item.datetime
    )
    const viewCounts = kLineData.map((item: any) =>
      parseInt(item.view_num)
    )
    const growths = kLineData.map((item: any) =>
      parseInt(item.view_rise_num)
    )

    updateChart(dates, viewCounts, growths)
  } catch (err) {
    console.error('Error fetching K-line data:', err)
    error.value = 'Failed to load K-line data. Please try again later.'
  }
}

const initChart = () => {
  if (chartContainer.value) {
    chart = echarts.init(chartContainer.value)
  }
}

const updateChart = (
  dates: string[],
  viewCounts: number[],
  growths: number[]
) => {
  initChart()
  const maxValue = Math.max(...viewCounts)
  const minValue = Math.min(...viewCounts)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    xAxis: {
      type: 'category',
      data: dates,
    },
    yAxis: {
      type: 'value',
      name: '播放量',
      min: minValue * 0.999995,
      max: maxValue * 1.000005,
    },
    series: [
      {
        name: '播放量',
        type: 'line',
        data: viewCounts,
      },
    ],
  }

  chart?.setOption(option)
}

onMounted(() => {
  fetchData()
  initChart()
})

watch(selectedVTuber, fetchData)
watch(sortBy, fetchData) // 监听 sortBy 变化，重新获取数据
watch(sortOrder, fetchData) // 监听 sortOrder 变化，重新获取数据

const getHeatClass = (heat: number) => {
  if (heat > 8) return 'text-red-800'
  if (heat > 6) return 'text-orange-500'
  return 'text-yellow-400'
}

// New function to handle video selection
const selectVideo = (video: Video) => {
  fetchConclusionData(video)
  fetchKLineData(video)
}

onUnmounted(() => {
  if (chart) {
    chart.dispose()
  }
})

// Resize chart when window size changes
window.addEventListener('resize', () => {
  chart?.resize()
})
</script>

<style scoped>
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: '';
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #2196f3;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}
</style>
