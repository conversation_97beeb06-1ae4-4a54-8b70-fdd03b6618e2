<script setup lang="ts">
import { ref, onMounted, computed, inject } from 'vue'
import { api } from '../api'
import { useRouter } from 'vue-router'

interface VTuberInfo {
  name: string // Full name
  shortName: string // Short name
  face: string
  sign: string
  birthday: string
  cover_url: string
  follower_num: number
  follower_rise_num: number
  dahanghai_num: number
  dahanghai_rise_num: number
  if_live: boolean
  live_url: string
  live_title: string
  live_cover: string
  live_roomid: string
}

const vtuberInfo = ref<VTuberInfo[]>([])
const router = useRouter()
const searchQuery = ref('')
const isLoading = ref(true)
const liveFilter = ref('all')
const sortBy = ref('none')
const showTrend = ref('rise')
const isStatsVisible = ref(false)
const selectedTimePeriod = ref(1) // Default to 1 day (当日)

const vtubersList = inject<string[][]>('vtubers', [])

onMounted(async () => {
  try {
    isLoading.value = true
    const [totalInfo, dahanghaiInfo, liveStatus] = await Promise.all([
      api.getAllTotalInfo(),
      api.getAllDahanghaiAndFollowerStuff(selectedTimePeriod.value),
      api.getAllliveStatus(),
    ])

    const totalData = totalInfo.data || []
    const dahanghaiData = dahanghaiInfo.data || []
    const liveData = liveStatus || []

    vtuberInfo.value = totalData.map((tdItem: any) => {
      const fullNameFromApi = tdItem.name


      const vtubersEntry = vtubersList.find(v => v[1] === fullNameFromApi);
      const shortNameFromApp = vtubersEntry ? vtubersEntry[0] : fullNameFromApi.split('_')[0];

      const dahanghaiEntry = dahanghaiData.find((dItem: any) => dItem.name === shortNameFromApp)
      const liveEntry = liveData.find((lItem: any) => lItem.name === fullNameFromApi)

      const vtuberData: VTuberInfo = {
        name: fullNameFromApi,
        shortName: shortNameFromApp,
        face: tdItem.face,
        sign: tdItem.sign,
        birthday: tdItem.birthday,
        cover_url: tdItem.cover_url,

        follower_num: dahanghaiEntry ? dahanghaiEntry.follower_num : 0,
        follower_rise_num: dahanghaiEntry ? dahanghaiEntry.follower_rise_num : 0,
        dahanghai_num: dahanghaiEntry ? dahanghaiEntry.dahanghai_num : 0,
        dahanghai_rise_num: dahanghaiEntry ? dahanghaiEntry.dahanghai_rise_num : 0,

        if_live: liveEntry ? liveEntry.if_live : false,
        live_url: liveEntry ? liveEntry.live_url : '',
        live_title: liveEntry ? liveEntry.live_title : '',
        live_cover: liveEntry ? liveEntry.live_cover : '',
        live_roomid: liveEntry ? liveEntry.live_roomid : '',
      }
      return vtuberData
    })
  } catch (error) {
    console.error('Error fetching VTuber data:', error)
  } finally {
    isLoading.value = false
  }
})

const selectVTuber = (vtuber: string) => {
  const vtubers = inject('vtubers') as [string, string][];
  const shortname = vtubers.find(([_, full]) => full === vtuber)?.[0];
  if (shortname) {
    router.push({ name: 'personalSpace', params: { vtuber: shortname } });
  }
}

const filteredAndSortedVTubers = computed(() => {
  let result = vtuberInfo.value

  if (searchQuery.value) {
    result = result.filter(vtuber =>
      vtuber.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (liveFilter.value !== 'all') {
    result = result.filter(vtuber =>
      liveFilter.value === 'live' ? vtuber.if_live : !vtuber.if_live
    )
  }

  switch (sortBy.value) {
    case 'followers-desc':
      return [...result].sort((a, b) => b.follower_num - a.follower_num)
    case 'followers-asc':
      return [...result].sort((a, b) => a.follower_num - b.follower_num)
    case 'dahanghai-desc':
      return [...result].sort((a, b) => b.dahanghai_num - a.dahanghai_num)
    case 'dahanghai-asc':
      return [...result].sort((a, b) => a.dahanghai_num - b.dahanghai_num)
    case 'followers-rise-desc':
      return [...result].sort(
        (a, b) => b.follower_rise_num - a.follower_rise_num
      )
    case 'followers-rise-asc':
      return [...result].sort(
        (a, b) => a.follower_rise_num - b.follower_rise_num
      )
    case 'dahanghai-rise-desc':
      return [...result].sort(
        (a, b) => b.dahanghai_rise_num - a.dahanghai_rise_num
      )
    case 'dahanghai-rise-asc':
      return [...result].sort(
        (a, b) => a.dahanghai_rise_num - b.dahanghai_rise_num
      )
    default:
      return result
  }
})

const followersChartData = computed(() => {
  let data = [...vtuberInfo.value]
    .filter(v =>
      showTrend.value === 'rise'
        ? v.follower_rise_num > 0
        : v.follower_rise_num < 0
    )
    .sort((a, b) =>
      showTrend.value === 'rise'
        ? b.follower_rise_num - a.follower_rise_num
        : a.follower_rise_num - b.follower_rise_num
    )
    .slice(0, 10)

  return {
    labels: data.map(v => v.name),
    values: data.map(v => v.follower_rise_num),
  }
})

const dahanghaiChartData = computed(() => {
  let data = [...vtuberInfo.value]
    .filter(v =>
      showTrend.value === 'rise'
        ? v.dahanghai_rise_num > 0
        : v.dahanghai_rise_num < 0
    )
    .sort((a, b) =>
      showTrend.value === 'rise'
        ? b.dahanghai_rise_num - a.dahanghai_rise_num
        : a.dahanghai_rise_num - b.dahanghai_rise_num
    )
    .slice(0, 10)

  return {
    labels: data.map(v => v.name),
    values: data.map(v => v.dahanghai_rise_num),
  }
})

const searchVTuber = () => {
  if (filteredAndSortedVTubers.value.length === 0) {
    alert('还未收录该主播╰(*°▽°*)╯，自定义收录功能正在上线')
  }
}

const toggleStats = () => {
  isStatsVisible.value = !isStatsVisible.value
}

// Handle time period change and fetch new data
const handleTimePeriodChange = async (period: number) => {
  selectedTimePeriod.value = period
  try {
    isLoading.value = true
    const [totalInfo, dahanghaiInfo, liveStatus] = await Promise.all([
      api.getAllTotalInfo(),
      api.getAllDahanghaiAndFollowerStuff(period),
      api.getAllliveStatus(),
    ])

    const totalData = totalInfo.data || []
    const dahanghaiData = dahanghaiInfo.data || []
    const liveData = liveStatus || []

    vtuberInfo.value = totalData.map((tdItem: any) => {
      const fullNameFromApi = tdItem.name
      const vtubersEntry = vtubersList.find(v => v[1] === fullNameFromApi);
      const shortNameFromApp = vtubersEntry ? vtubersEntry[0] : fullNameFromApi.split('_')[0];

      const dahanghaiEntry = dahanghaiData.find((dItem: any) => dItem.name === shortNameFromApp)
      const liveEntry = liveData.find((lItem: any) => lItem.name === fullNameFromApi)

      const vtuberData: VTuberInfo = {
        name: fullNameFromApi,
        shortName: shortNameFromApp,
        face: tdItem.face,
        sign: tdItem.sign,
        birthday: tdItem.birthday,
        cover_url: tdItem.cover_url,
        follower_num: dahanghaiEntry ? dahanghaiEntry.follower_num : 0,
        follower_rise_num: dahanghaiEntry ? dahanghaiEntry.follower_rise_num : 0,
        dahanghai_num: dahanghaiEntry ? dahanghaiEntry.dahanghai_num : 0,
        dahanghai_rise_num: dahanghaiEntry ? dahanghaiEntry.dahanghai_rise_num : 0,
        if_live: liveEntry ? liveEntry.if_live : false,
        live_url: liveEntry ? liveEntry.live_url : '',
        live_title: liveEntry ? liveEntry.live_title : '',
        live_cover: liveEntry ? liveEntry.live_cover : '',
        live_roomid: liveEntry ? liveEntry.live_roomid : '',
      }
      return vtuberData
    })
  } catch (error) {
    console.error('Error fetching VTuber data:', error)
  } finally {
    isLoading.value = false
  }
}

// Get time period label for display
const getTimePeriodLabel = (period: number) => {
  switch (period) {
    case 1: return '当日'
    case 3: return '三天'
    case 7: return '本周'
    case 30: return '本月'
    case 365: return '本年'
    default: return '当日'
  }
}
</script>

<template>
  <div class="container mx-auto px-4 py-8 min-h-screen bg-gray-50">
    <div class="flex justify-center mb-6">
      <img
        src="/pics/icon.png"
        alt="App Icon"
        class="h-36 w-36 object-contain transition-all duration-300 hover:scale-110 hover:rotate-6"
      >
    </div>

    <!-- Search Bar -->
    <div class="mb-6">
      <div class="flex items-center justify-center max-w-2xl mx-auto">
        <div class="relative w-full">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索VUP..."
            class="w-full px-4 py-2 rounded-full border-2 border-indigo-500 focus:outline-none focus:border-indigo-600 focus:ring-2 focus:ring-indigo-200 transition-all duration-300 pl-12"
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-6 w-6 absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
        <button
          @click="searchVTuber"
          class="bg-indigo-500 text-white pl-4 px-4 py-2 rounded-l-lg rounded-r-lg hover:bg-indigo-700 transition-colors duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 whitespace-nowrap"
        >
          搜索
        </button>
      </div>
    </div>

    <!-- Time Period Selection -->
    <div class="flex justify-end mb-4 items-center">
      <div v-if="isLoading" class="mr-2">
        <!-- Running cat animation -->
        <img src="/loading/002.gif" alt="Loading..." class="h-10 w-10" />
      </div>
      <div class="flex flex-wrap gap-3">
        <label
          v-for="period in [1, 3, 7, 30, 365]"
          :key="period"
          class="flex items-center cursor-pointer"
        >
          <input
            type="radio"
            :value="period"
            v-model="selectedTimePeriod"
            @change="handleTimePeriodChange(period)"
            class="sr-only"
          />
          <div
            :class="[
              'px-4 py-2 rounded-full transition-all duration-300 text-sm font-medium',
              selectedTimePeriod === period
                ? 'bg-blue-500 text-white shadow-md'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200',
            ]"
          >
            {{ getTimePeriodLabel(period) }}
          </div>
        </label>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="mb-6 bg-white rounded-2xl shadow-lg p-6 relative">
      <!-- Collapse Toggle Button -->
      <button
        @click="toggleStats"
        class="absolute top-6 right-6 p-1.5 rounded-full hover:bg-gray-100 transition-colors duration-300"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-3.5 w-3.5 transform transition-transform duration-300 text-gray-500"
          :class="{ 'rotate-180': !isStatsVisible }"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          stroke-width="2"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>

      <!-- Title - only visible when collapsed -->
      <h3
        v-if="!isStatsVisible"
        class="text-xl font-semibold text-gray-700 mb-0 pl-1 flex items-center"
      >
        {{ getTimePeriodLabel(selectedTimePeriod) }}涨跌榜单⬇️
        <div class="relative group ml-2">
          <svg
            class="w-4 h-4 text-gray-500 cursor-pointer"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              d="M18 10c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm-8-6a6 6 0 100 12A6 6 0 0010 4zm0 9a1 1 0 110-2 1 1 0 010 2zm0-4a1 1 0 01-1-1V7a1 1 0 112 0v1a1 1 0 01-1 1z"
              clip-rule="evenodd"
            ></path>
          </svg>
          <div
            class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap"
          >
            该榜单数据计算由：当前时间数据-对应时间段前数据 得出
          </div>
        </div>
      </h3>

      <!-- Content -->
      <div
        class="overflow-hidden transition-all duration-300 mt-2"
        :style="{ maxHeight: isStatsVisible ? '1000px' : '0px' }"
      >
        <div class="flex flex-wrap gap-4 mb-6 font-semibold">
          <button
            @click="showTrend = 'rise'"
            :class="[
              'px-6 py-2 rounded-full transition-all duration-300',
              showTrend === 'rise'
                ? 'bg-red-500 text-white shadow-md'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200',
            ]"
          >
            涨榜
          </button>
          <button
            @click="showTrend = 'fall'"
            :class="[
              'px-6 py-2 rounded-full transition-all duration-300',
              showTrend === 'fall'
                ? 'bg-green-500 text-white shadow-md'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200',
            ]"
          >
            跌榜
          </button>
        </div>

        <div class="grid md:grid-cols-2 gap-2 flex flex-wrap">
          <!-- Followers Chart -->
          <div class="bg-white rounded-xl overflow-x-hidden">
            <h3 class="text-xl font-semibold mb-4 text-gray-800 pl-12">
              粉丝变化榜
            </h3>
            <div class="relative space-y-2 pr-20">
              <div
                v-for="(value, index) in followersChartData.values"
                :key="index"
                class="flex items-center gap-2 group"
              >
                <div
                  class="w-20 text-right text-sm font-medium text-gray-600 font-semibold"
                >
                  {{ followersChartData.labels[index] }}
                </div>
                <div class="flex-1 relative">
                  <div
                    class="h-6 rounded-lg transition-all duration-500 group-hover:opacity-90 flex items-center"
                    :style="{
                      width: `${
                        (Math.abs(value) /
                          Math.max(
                            ...followersChartData.values.map(Math.abs)
                          )) *
                        85
                      }%`,
                      backgroundColor:
                        value > 0 ? 'rgb(239, 68, 68)' : 'rgb(34, 197, 94)',
                    }"
                  ></div>
                  <span
                    class="absolute top-1/2 -translate-y-1/2 transform translate-x-1 text-sm text-gray-700 font-medium whitespace-nowrap"
                    :style="{
                      left: `${
                        (Math.abs(value) /
                          Math.max(
                            ...followersChartData.values.map(Math.abs)
                          )) *
                        85
                      }%`,
                    }"
                  >
                    {{ value.toLocaleString() }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <!-- Dahanghai Chart -->
          <div class="bg-white rounded-xl overflow-x-hidden">
            <h3 class="text-xl font-semibold mb-4 text-gray-800 pl-12">
              大航海变化榜
            </h3>
            <div class="relative space-y-2 pr-20">
              <div
                v-for="(value, index) in dahanghaiChartData.values"
                :key="index"
                class="flex items-center gap-2 group"
              >
                <div
                  class="w-20 text-right text-sm font-medium text-gray-600 font-semibold"
                >
                  {{ dahanghaiChartData.labels[index] }}
                </div>
                <div class="flex-1 relative">
                  <div
                    class="h-6 rounded-lg transition-all duration-500 group-hover:opacity-90 flex items-center"
                    :style="{
                      width: `${
                        (Math.abs(value) /
                          Math.max(
                            ...dahanghaiChartData.values.map(Math.abs)
                          )) *
                        85
                      }%`,
                      backgroundColor:
                        value > 0 ? 'rgb(239, 68, 68)' : 'rgb(34, 197, 94)',
                    }"
                  ></div>
                  <span
                    class="absolute top-1/2 -translate-y-1/2 transform translate-x-1 text-sm text-gray-700 font-medium whitespace-nowrap"
                    :style="{
                      left: `${
                        (Math.abs(value) /
                          Math.max(
                            ...dahanghaiChartData.values.map(Math.abs)
                          )) *
                        85
                      }%`,
                    }"
                  >
                    {{ value.toLocaleString() }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="flex flex-wrap gap-4 items-center mb-8 pl-4">
      <div class="flex gap-2">
        <button
          v-for="filter in ['all', 'live', 'offline']"
          :key="filter"
          @click="liveFilter = filter"
          :class="[
            'px-6 py-2 rounded-full transition-all duration-300',
            liveFilter === filter
              ? 'bg-indigo-600 text-white shadow-lg'
              : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200',
          ]"
        >
          {{
            filter === 'all' ? '全部' : filter === 'live' ? '直播中' : '未直播'
          }}
        </button>
      </div>

      <select
        v-model="sortBy"
        class="px-6 py-2 rounded-full bg-white border border-gray-200 focus:outline-none focus:ring-2 focus:ring-indigo-200 transition-all duration-300"
      >
        <option value="none">默认排序</option>
        <option value="followers-desc">粉丝数 (高到低)</option>
        <option value="followers-asc">粉丝数 (低到高)</option>
        <option value="dahanghai-desc">大航海数 (高到低)</option>
        <option value="dahanghai-asc">大航海数 (低到高)</option>
        <option value="followers-rise-desc">粉丝增长 (高到低)</option>
        <option value="followers-rise-asc">粉丝增长 (低到高)</option>
        <option value="dahanghai-rise-desc">大航海增长 (高到低)</option>
        <option value="dahanghai-rise-asc">大航海增长 (低到高)</option>
      </select>
    </div>

    <!-- Loading Skeleton -->
    <div
      v-if="isLoading"
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
    >
      <div
        v-for="i in 6"
        :key="i"
        class="bg-white rounded-2xl shadow-lg overflow-hidden animate-pulse"
      >
        <div class="h-48 bg-gray-300"></div>
        <div class="p-6 space-y-4">
          <div class="h-6 bg-gray-300 rounded w-3/4"></div>
          <div class="h-4 bg-gray-300 rounded w-1/2"></div>
          <div class="h-4 bg-gray-300 rounded w-full"></div>
          <div class="h-4 bg-gray-300 rounded w-2/3"></div>
        </div>
      </div>
    </div>

    <!-- VTuber Cards -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="vtuber in filteredAndSortedVTubers"
        :key="vtuber.name"
        class="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:scale-105"
      >
        <div class="relative">
          <img
            :src="vtuber.live_cover || vtuber.cover_url"
            :alt="vtuber.name"
            class="w-full h-36 object-cover"
          />
        </div>
        <div class="p-4">
          <div class="flex items-center mb-4">
            <img
              :src="vtuber.face"
              :alt="vtuber.name"
              class="w-16 h-16 rounded-full mr-4 cursor-pointer"
              @click="selectVTuber(vtuber.name)"
            />
            <div class="flex-grow">
              <div class="flex items-center justify-between">
                <h2
                  class="text-xl font-semibold text-gray-800 cursor-pointer"
                  @click="selectVTuber(vtuber.name)"
                >
                  {{ vtuber.name }}
                </h2>
                <p
                  v-if="vtuber.if_live"
                  class="text-sm font-semibold text-green-500 ml-2"
                >
                  正在营业中
                </p>
                <p
                  v-else
                  class="text-sm font-semibold text-gray-600 dark:text-gray-400 ml-2"
                >
                  下班状态中
                </p>
              </div>
              <p class="text-sm text-gray-600">{{ vtuber.birthday }}</p>
            </div>
          </div>
          <p class="text-gray-700 mb-3 sign">{{ vtuber.sign }}</p>
          <div class="flex justify-between text-sm text-gray-600 mb-2">
            <span>粉丝数: {{ vtuber.follower_num.toLocaleString() }}</span>
            <span
              :class="
                vtuber.follower_rise_num > 0 ? 'text-red-500' : 'text-green-500'
              "
            >
              {{ vtuber.follower_rise_num > 0 ? '+' : ''
              }}{{ vtuber.follower_rise_num.toLocaleString() }}
            </span>
          </div>
          <div class="flex justify-between text-sm text-gray-600 mb-2">
            <span>大航海数: {{ vtuber.dahanghai_num.toLocaleString() }}</span>
            <span
              :class="
                vtuber.dahanghai_rise_num > 0
                  ? 'text-red-500'
                  : 'text-green-500'
              "
            >
              {{ vtuber.dahanghai_rise_num > 0 ? '+' : ''
              }}{{ vtuber.dahanghai_rise_num.toLocaleString() }}
            </span>
          </div>
          <div v-if="vtuber.if_live" class="bg-indigo-100 p-2 rounded-md">
            <h3 class="font-semibold text-indigo-700 mb-1">-当前直播-</h3>
            <p class="text-sm text-indigo-600 mb-1">{{ vtuber.live_title }}</p>
            <a
              :href="vtuber.live_url"
              target="_blank"
              class="text-xs text-indigo-500 hover:underline"
              >前往观看✨</a
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.sign {
  display: block;
  min-height: 3em;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
