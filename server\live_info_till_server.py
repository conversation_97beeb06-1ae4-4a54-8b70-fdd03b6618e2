import psycopg2
import asyncio
import copy
import math
import requests
from requests.adapters import H<PERSON><PERSON><PERSON>pter
import http.cookies
from typing import Optional
from datetime import datetime
import time
import aiohttp
from urllib3 import Retry
import uuid
from psycopg2 import OperationalError

from vendor.blivedm import blivedm
import vendor.blivedm.blivedm.models.web as web_models

from sql.sql_const import *
from backend.tools.query_live_info import (
    query_average_enter_room_count_by_room_and_datetime,
    query_average_online_rank_count_by_room_and_datetime,
    query_danmu_by_room_and_datetime,
    query_interact_word_count_by_room_and_datetime,
    query_max_online_rank_count_by_room_and_datetime,
    query_pay_count_by_room_and_live_start_end_time,
    query_live_start_time_by_end_time,
    query_total_enter_room_count_by_room_and_datetime
)
from const import *
from logger import logger


# Based on https://github.com/Zhmz/blivedm/blob/dev/main.py

connection = psycopg2.connect(**PGSQL_CONFIG)
cursor = connection.cursor()


def table_exists(table_name, schema_name="public"):
    full_table_name = f"{schema_name}.{table_name}"
    try:
        cursor.execute(exist_table_sql, (full_table_name,))
        result = cursor.fetchone()
        return result[0] if result else False
    except OperationalError as e:
        logger.info(f"数据库连接失败: {e}")
        return False

# 已创建的danmu表缓存
created_danmu_tables = set()
created_enter_room_info_tables = set()
if not table_exists("gift_table"):
    cursor.execute(create_gift_table_sql)
    logger.info("gift_table created successfully")
if not table_exists("combo_send_table"):
    cursor.execute(create_combo_send_table_sql)
    logger.info("combo_send_table created successfully")
if not table_exists("buy_guard_table"):
    cursor.execute(create_buy_guard_table_sql)
    logger.info("buy_guard_table created successfully")
if not table_exists("user_toast_v2_table"):
    cursor.execute(create_user_toast_v2_table_sql)
    logger.info("user_toast_v2_table created successfully")
if not table_exists("super_chat_table"):
    cursor.execute(create_super_chat_table_sql)
    logger.info("super_chat_table created successfully")
if not table_exists("interact_word_table"):
    cursor.execute(create_interact_word_table_sql)
    logger.info("interact_word_table created successfully")
if not table_exists("watch_change_table"):
    cursor.execute(create_watch_change_table_sql)
    logger.info("watch_change_table created successfully")
if not table_exists("like_info_update_table"):
    cursor.execute(create_like_info_update_table_sql)
    logger.info("like_info_update_table created successfully")

# 创建分钟表
if not table_exists("online_rank_count_minute_table"):
    cursor.execute(create_online_rank_count_minute_table_sql)
    logger.info("online_rank_count_minute_table created successfully")
if not table_exists("interact_word_count_minute_table"):
    cursor.execute(create_interact_word_count_minute_table_sql)
    logger.info("interact_word_count_minute_table created successfully")
if not table_exists("enter_room_count_minute_table"):
    cursor.execute(create_enter_room_count_minute_table_sql)
    logger.info("enter_room_count_minute_table created successfully")
if not table_exists("danmu_count_minute_table"):
    cursor.execute(create_danmu_count_minute_table_sql)
    logger.info("danmu_count_minute_table created successfully")
if not table_exists("income_minute_table"):
    cursor.execute(create_income_minute_table_sql)
    logger.info("income_minute_table created successfully")
if not table_exists("live_status_minute_table"):
    cursor.execute(create_live_status_minute_table_sql)
    logger.info("live_status_minute_table created successfully")
if not table_exists("active_watcher_count_minute_table"):
    cursor.execute(create_active_watcher_count_minute_table_sql)
    logger.info("active_watcher_count_minute_table created successfully")

# 创建场次表
if not table_exists("live_session_table"):
    cursor.execute(create_live_session_table_sql)
    logger.info("live_session_table created successfully")

connection.commit()

session: Optional[aiohttp.ClientSession] = None


async def main():
    init_session()
    try:
        # await run_single_client()
        await run_multi_clients()
    finally:
        await session.close()
    # TODO: 数据库关闭


def init_session():
    cookies = http.cookies.SimpleCookie()
    cookies["SESSDATA"] = SESSDATA
    cookies["buvid3"] = BUVID3
    cookies["SESSDATA"]["domain"] = "bilibili.com"

    global session
    session = aiohttp.ClientSession()
    session.cookie_jar.update_cookies(cookies)


async def run_single_client():
    """
    演示监听一个直播间
    """
    room_id = ""
    client = blivedm.BLiveClient(room_id, session=session)
    handler = MyHandler()
    client.set_handler(handler)

    client.start()
    try:
        await asyncio.sleep(5)
        client.stop()

        await client.join()
    finally:
        await client.stop_and_close()


async def run_multi_clients():
    """
    演示同时监听多个直播间
    """
    room_file_path = f"{PROJECT_ROOT}/config/room.txt"
    if os.path.exists(room_file_path):
        with open(room_file_path, "r", encoding="utf-8") as file:
            target_room_ids = [line.split(",")[1].strip() for line in file.readlines()]
    else:
        logger.error(f"file {room_file_path} not exists")
        return

    clients = [
        blivedm.BLiveClient(room_id, session=session) for room_id in target_room_ids
    ]
    handler = MyHandler()
    
    # 初始化所有房间的直播状态
    for room_id in target_room_ids:
        # 确保房间已初始化
        handler.ensure_room_initialized(room_id)
        # 获取房间的直播状态
        handler.get_live_status(room_id)
    
    for client in clients:
        client.set_handler(handler)
        client.start()

    try:
        await asyncio.gather(*(client.join() for client in clients))
    finally:
        await asyncio.gather(*(client.stop_and_close() for client in clients))


class MyHandler(blivedm.BaseHandler):
    # # 演示如何添加自定义回调
    # _CMD_CALLBACK_DICT = blivedm.BaseHandler._CMD_CALLBACK_DICT.copy()
    #
    # # 看过数消息回调
    # def __watched_change_callback(self, client: blivedm.BLiveClient, command: dict):
    #     logger.info(f'[{client.room_id}] WATCHED_CHANGE: {command}')
    # _CMD_CALLBACK_DICT['WATCHED_CHANGE'] = __watched_change_callback  # noqa
    
    # 房间直播状态字典，用于跟踪每个房间的直播状态和liveid
    room_live_status = {}
    
    # 确保房间在room_live_status中初始化
    def ensure_room_initialized(self, room_id):
        if room_id not in self.room_live_status:
            self.room_live_status[room_id] = {
                "live_status": -1,  # -1: 未知, 0: 未开播, 1: 直播中, 2: 轮播中
                "live_id": None,  # 当前直播场次的UUID
                "start_time": None  # 开播时间
            }
        
        # 确保房间在is_in_live_symbol_dict中初始化
        if str(room_id) not in self.is_in_live_symbol_dict:
            # 默认设置为True，让数据能够被保存
            self.is_in_live_symbol_dict[str(room_id)] = True

    # db存入计数器
    danmu_db_index = 0
    gift_db_index = 0
    combo_send_db_index = 0
    buy_guard_db_index = 0
    user_toast_v2_db_index = 0
    super_chat_db_index = 0
    interact_word_db_index = 0

    # 写入数据库的池子的阈值
    danmu_db_threshold = 100
    gift_db_threshold = 3
    combo_send_db_threshold = 1
    buy_guard_db_threshold = 1
    user_toast_v2_db_threshold = 1
    super_chat_db_threshold = 1
    interact_word_db_threshold = 100

    # db数据对象池
    danmu_pool = []
    danmu_commit_pool = []
    gift_pool = []
    gift_commit_pool = []
    combo_send_pool = []
    combo_send_commit_pool = []
    buy_guard_pool = []
    buy_guard_commit_pool = []
    user_toast_v2_pool = []
    user_toast_v2_commit_pool = []
    super_chat_pool = []
    super_chat_commit_pool = []
    interact_word_pool = []
    interact_word_commit_pool = []

    # 进入房间信息池
    enter_room_info_pools = {}
    enter_room_info_commit_pools = {}
    enter_room_info_db_indices = {}
    enter_room_info_db_threshold = 50

    """场次表的临时数据"""
    # 看过人数
    watch_change_dict = {}
    # 点赞人数
    like_info_update_dict = {}
    # 付费次数（暂时不用，或者后面给分钟表用）
    pay_count_dict = {}

    """分钟表的缓存数据"""
    # 高能榜已存的分钟（要区分每个主播/房间号）
    to_save_minute_online_rank_count_minute_dict = {}
    # 待存付费榜人数
    temp_online_rank_count_minute_dict = {}

    # 互动次数已存的分钟（要区分每个主播/房间号）
    to_save_minute_interact_word_count_minute_dict = {}
    # 待存互动次数
    temp_interact_word_count_minute_dict = {}
    
    # 活跃观众数量已存的分钟（要区分每个主播/房间号）
    to_save_minute_active_watcher_count_minute_dict = {}
    # 待存活跃观众数量
    temp_active_watcher_count_minute_dict = {}
    # 活跃观众用户ID集合（要区分每个主播/房间号）
    active_watcher_user_ids_dict = {}

    # 进入房间人次已存的分钟（要区分每个主播/房间号）
    to_save_minute_enter_room_count_minute_dict = {}
    # 待存进入房间人次
    temp_enter_room_count_minute_dict = {}

    # 弹幕数分钟表
    to_save_minute_danmu_count_minute_dict = {}
    # 待存弹幕数
    temp_danmu_count_minute_dict = {}

    # 每分钟营收数据 已存的分钟（要区分每个主播/房间号）
    to_save_minute_income_minute_dict = {}
    # 每分钟营收数据
    temp_income_minute_dict = {}

    # # 直播状态数据 已存的分钟（要区分每个主播/房间号）
    # to_save_minute_live_status_minute_dict = {}
    # 每分钟更新直播状态
    temp_live_status_minute_dict = {}

    """未开播的DB屏蔽"""
    # 每个直播间对应一个bool值
    is_in_live_symbol_dict = {}

    def _on_heartbeat(
        self, client: blivedm.BLiveClient, message: web_models.HeartbeatMessage
    ):
        logger.info(f"[{client.room_id}] 心跳")

    # 按room_id分表存储的弹幕池
    danmu_pools = {}
    danmu_commit_pools = {}
    danmu_db_indices = {}
    
    def _on_danmaku(
        self, client: blivedm.BLiveClient, message: web_models.DanmakuMessage
    ):
        # 确保房间已初始化
        self.ensure_room_initialized(client.room_id)
        
        # # 判断当前房间是否处于开播状态 Temp off
        # if str(client.room_id) not in self.is_in_live_symbol_dict.keys():
        #     return
        # if not self.is_in_live_symbol_dict[str(client.room_id)]:
        #     return
            
        seconds = message.timestamp / 1000
        dt = datetime.fromtimestamp(seconds).strftime("%Y-%m-%d %H:%M:%S")
        logger.info(
            f"[{client.room_id}] [{dt}] --这是一条弹幕-- {message.uname}：{message.msg}"
        )

        # 确保该room_id的表已创建
        room_id = str(client.room_id)
        if room_id not in created_danmu_tables:
            table_name = get_danmu_table_name(room_id)
            if not table_exists(table_name):
                create_sql = get_create_danmu_table_sql(room_id)
                cursor.execute(create_sql)
                connection.commit()
                logger.info(f"{table_name} created successfully")
            created_danmu_tables.add(room_id)

        if room_id not in created_enter_room_info_tables:
            table_name = get_enter_room_user_info_table_name(room_id)
            if not table_exists(table_name):
                create_sql = get_create_enter_room_user_info_table_sql(room_id)
                cursor.execute(create_sql)
                connection.commit()
                logger.info(f"{table_name} created successfully")
            created_enter_room_info_tables.add(room_id)

        # 初始化该room_id的池子（如果不存在）
        if room_id not in self.danmu_pools:
            self.danmu_pools[room_id] = []
        if room_id not in self.danmu_commit_pools:
            self.danmu_commit_pools[room_id] = []
        if room_id not in self.danmu_db_indices:
            self.danmu_db_indices[room_id] = 0

        current_liveid = self.room_live_status.get(client.room_id, {}).get('live_id')

        params = {
            "room_id": client.room_id,
            "live_id": current_liveid,
            "rnd": message.rnd,
            "dm_type": message.dm_type,
            "user_id": message.uid,
            "user_name": message.uname,
            "user_face": message.face,
            "message": message.msg,
            "vip": message.vip,
            "svip": message.svip,
            "privilege_type": message.privilege_type,
            "medal_level": message.medal_level,
            "medal_name": message.medal_name,
            "medal_room_id": message.medal_room_id,
            "medal_room_name": message.runame,
            "timestamp": message.timestamp,
            "datetime": dt,
        }

        # 存入对应room_id的对象池
        self.danmu_pools[room_id].append(params)
        self.danmu_db_indices[room_id] += 1
        if self.danmu_db_indices[room_id] >= self.danmu_db_threshold:
            self.danmu_commit_pools[room_id] = copy.deepcopy(self.danmu_pools[room_id])
            self.danmu_pools[room_id] = []

            logger.info(
                f"insert danmu to {get_danmu_table_name(room_id)} successfully, count = "
                + str(self.danmu_db_indices[room_id])
                + ", pool count = "
                + str(len(self.danmu_commit_pools[room_id]))
            )
            self.danmu_db_indices[room_id] = 0
            
            # 使用对应room_id的插入SQL
            insert_sql = get_insert_danmu_table_sql(room_id)
            cursor.executemany(insert_sql, self.danmu_commit_pools[room_id])
            connection.commit()

        # 需要保存分钟表（弹幕算在互动次数里）
        # 取出当前房间号的缓存数据，分钟数和房间观众数
        if (
            client.room_id
            not in self.to_save_minute_interact_word_count_minute_dict.keys()
        ):
            self.to_save_minute_interact_word_count_minute_dict[client.room_id] = 0
        if client.room_id not in self.temp_interact_word_count_minute_dict.keys():
            self.temp_interact_word_count_minute_dict[client.room_id] = 0

        cur_minute = math.floor(seconds / 60)
        # 这里只负责更新数据，放在其他地方存
        if (
            cur_minute
            != self.to_save_minute_interact_word_count_minute_dict[client.room_id]
        ):
            # # 执行sql存数据库（互动次数分钟表），存的是上一分钟的数据
            if self.to_save_minute_interact_word_count_minute_dict[client.room_id] != 0:
                self.save_interact_word_count_minute_to_db(client.room_id)
            # 更新记录分钟和缓存人数
            self.temp_interact_word_count_minute_dict[client.room_id] = 0
            self.to_save_minute_interact_word_count_minute_dict[client.room_id] = (
                cur_minute
            )
        else:
            # 进入房间不计入互动次数
            if message.msg_type != 1:
                # 在同一分钟内不断自增
                self.temp_interact_word_count_minute_dict[client.room_id] += 1

        # 保存弹幕数分钟表
        if client.room_id not in self.to_save_minute_danmu_count_minute_dict.keys():
            self.to_save_minute_danmu_count_minute_dict[client.room_id] = 0
        if client.room_id not in self.temp_danmu_count_minute_dict.keys():
            self.temp_danmu_count_minute_dict[client.room_id] = 0

        cur_minute = math.floor(seconds / 60)
        # 检查当前分钟是否需要更新
        if cur_minute != self.to_save_minute_danmu_count_minute_dict[client.room_id]:
            # 执行sql存数据库（弹幕数分钟表），存的是上一分钟的数据
            if self.to_save_minute_danmu_count_minute_dict[client.room_id] != 0:
                self.save_danmu_count_minute_to_db(client.room_id)
            # 更新记录分钟和缓存弹幕数
            self.temp_danmu_count_minute_dict[client.room_id] = 1  # 当前弹幕计数为1
            self.to_save_minute_danmu_count_minute_dict[client.room_id] = cur_minute
        else:
            # 在同一分钟内不断自增
            self.temp_danmu_count_minute_dict[client.room_id] += 1

    def _on_gift(self, client: blivedm.BLiveClient, message: web_models.GiftMessage):
        # 确保房间已初始化
        self.ensure_room_initialized(client.room_id)
        
        # gift的时间戳是秒
        seconds = message.timestamp
        dt = datetime.fromtimestamp(seconds).strftime("%Y-%m-%d %H:%M:%S")
        logger.info(
            f"[{client.room_id}] [{dt}] {message.uname} 赠送{message.gift_name}x{message.num}"
            f" （{message.coin_type}瓜子x{message.total_coin}）"
        )

        current_liveid = self.room_live_status.get(client.room_id, {}).get('live_id')

        params = {
            "room_id": client.room_id,
            "live_id": current_liveid,
            "rnd": message.rnd,
            "user_id": message.uid,
            "user_name": message.uname,
            "user_face": message.face,
            "gift_id": message.gift_id,
            "gift_type": message.gift_type,
            "gift_name": message.gift_name,
            "gift_img_basic": message.gift_img_basic,
            "gift_action": message.action,
            "gift_num": message.num,
            "gift_per_price": message.price,
            "coin_type": message.coin_type,
            "total_coin": message.total_coin,
            "privilege_type": message.guard_level,
            "medal_level": message.medal_level,
            "medal_name": message.medal_name,
            "medal_room_id": message.medal_room_id,
            "medal_room_uid": message.medal_ruid,
            "timestamp": message.timestamp * 1000,  # 改为毫秒存入数据库
            "datetime": dt,
        }

        # 存入对象池
        self.gift_pool.append(params)
        self.gift_db_index += 1
        if self.gift_db_index >= self.gift_db_threshold:
            self.gift_commit_pool = copy.deepcopy(self.gift_pool)
            self.gift_pool = []

            logger.info(
                "insert gift successfully,count = "
                + str(self.gift_db_index)
                + ", pool count = "
                + str(len(self.gift_commit_pool))
            )
            self.gift_db_index = 0
            cursor.executemany(insert_gift_table_sql, self.gift_commit_pool)
            connection.commit()

        # 付费次数
        if client.room_id not in self.pay_count_dict.keys():
            self.pay_count_dict[client.room_id] = 0
        self.pay_count_dict[client.room_id] += 1
        logger.info(
            f"[{client.room_id}] [{dt}] 付费次数： {self.pay_count_dict[client.room_id]}"
        )

        # 需要保存营收分钟表
        # 取出当前房间号的缓存数据，分钟数和营收数
        current_income = round(float(message.total_coin) / 1000, 1)
        self.accumulate_income_minute(client.room_id, seconds, current_income)

    def accumulate_income_minute(self, room_id, seconds, current_income):
        # 需要保存营收分钟表
        # 取出当前房间号的缓存数据，分钟数和营收数
        if room_id not in self.to_save_minute_income_minute_dict.keys():
            self.to_save_minute_income_minute_dict[room_id] = 0
        if room_id not in self.temp_income_minute_dict.keys():
            self.temp_income_minute_dict[room_id] = 0

        cur_minute = math.floor(seconds / 60)
        # 这里是送礼物才会触发，要写到主动下发的位置（高能榜人数）
        if cur_minute == self.to_save_minute_income_minute_dict[room_id]:
            # 在同一分钟内不断自增
            self.temp_income_minute_dict[room_id] += current_income
        logger.info(f"[{room_id}] 当次收入：{current_income}")

    def save_income_minute_to_db(self, room_id):
        # 判断当前房间是否处于开播状态
        if str(room_id) not in self.is_in_live_symbol_dict.keys():
            return
        if not self.is_in_live_symbol_dict[str(room_id)]:
            return
        
        # 需要算出待存的秒级时间戳
        to_save_second = self.to_save_minute_income_minute_dict[room_id] * 60
        to_save_datetime = datetime.fromtimestamp(to_save_second).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        
        # 获取当前liveid
        current_liveid = self.room_live_status.get(room_id, {}).get('live_id')
        
        params = {
            "room_id": room_id,
            "live_id": current_liveid,
            "income": self.temp_income_minute_dict[room_id],
            "timestamp": to_save_second,
            "datetime": to_save_datetime,
        }

        cursor.execute(insert_income_minute_table_sql, params)
        connection.commit()
        logger.info(
            f"[{room_id}] [{to_save_datetime}] 存入DB，累计营收：{self.temp_income_minute_dict[room_id]}"
        )

    def save_danmu_count_minute_to_db(self, room_id):
        # 判断当前房间是否处于开播状态
        if str(room_id) not in self.is_in_live_symbol_dict.keys():
            return
        if not self.is_in_live_symbol_dict[str(room_id)]:
            return
        # 需要算出待存的秒级时间戳
        to_save_second = self.to_save_minute_danmu_count_minute_dict[room_id] * 60
        to_save_datetime = datetime.fromtimestamp(to_save_second).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        
        # 获取当前liveid
        current_liveid = self.room_live_status.get(room_id, {}).get('live_id')
        
        params = {
            "room_id": room_id,
            "live_id": current_liveid,
            "count": self.temp_danmu_count_minute_dict[room_id],
            "timestamp": to_save_second,
            "datetime": to_save_datetime,
        }

        cursor.execute(insert_danmu_count_minute_table_sql, params)
        connection.commit()
        logger.info(
            f"[{room_id}] [{to_save_datetime}] 存入DB，当前分钟弹幕数：{self.temp_danmu_count_minute_dict[room_id]}"
        )

    def _on_combo_send(
        self, client: blivedm.BLiveClient, message: web_models.ComboSendMessage
    ):
        # 确保房间已初始化
        self.ensure_room_initialized(client.room_id)
        
        seconds = int(round(time.time()))  # 单位：秒
        dt = datetime.fromtimestamp(seconds).strftime("%Y-%m-%d %H:%M:%S")
        logger.info(
            f"[{client.room_id}] [{dt}] {message.username} 礼物连击 {message.gift_name}x{message.total_num}"
            f"，combo_num = {message.combo_num}，（瓜子x{message.combo_total_coin}）"
        )

        current_liveid = self.room_live_status.get(client.room_id, {}).get('live_id')

        params = {
            "room_id": client.room_id,
            "live_id": current_liveid,
            "user_id": message.uid,
            "user_name": message.username,
            "gift_id": message.gift_id,
            "gift_name": message.gift_name,
            "total_num": message.total_num,
            "combo_id": message.combo_id,
            "combo_num": message.combo_num,
            "combo_total_coin": message.combo_total_coin,
            "action": message.action,
            "batch_combo_id": message.batch_combo_id,
            "batch_combo_num": message.batch_combo_num,
            "r_uid": message.r_uid,
            "r_uname": message.r_uname,
            "timestamp": seconds * 1000,
            "datetime": dt,
        }

        # 存入对象池
        self.combo_send_pool.append(params)
        self.combo_send_db_index += 1
        if self.combo_send_db_index >= self.combo_send_db_threshold:
            self.combo_send_commit_pool = copy.deepcopy(self.combo_send_pool)
            self.combo_send_pool = []

            # #礼物连击有报错，会导致数据库操作中断，先不存这个combosend，对其他数据没有影响
            # logger.info("insert combo_send successfully,count = "+str(self.combo_send_db_index)+", pool count = "+str(len(self.combo_send_commit_pool)))
            self.combo_send_db_index = 0
            # cursor.executemany(insert_combo_send_table_sql, self.combo_send_commit_pool)
            # connection.commit()

    def _on_buy_guard(
        self, client: blivedm.BLiveClient, message: web_models.GuardBuyMessage
    ):
        # 确保房间已初始化
        self.ensure_room_initialized(client.room_id)
        
        seconds = message.start_time
        dt = datetime.fromtimestamp(seconds).strftime("%Y-%m-%d %H:%M:%S")
        logger.info(
            f"[{client.room_id}] [{dt}] {message.username} 上舰，guard_level={message.guard_level}"
        )

        current_liveid = self.room_live_status.get(client.room_id, {}).get('live_id')

        params = {
            "room_id": client.room_id,
            "live_id": current_liveid,
            "user_id": message.uid,
            "user_name": message.username,
            "privilege_type": message.guard_level,
            "gift_id": message.gift_id,
            "gift_name": message.gift_name,
            "gift_num": message.num,
            "gift_per_price": message.price,
            "timestamp": message.start_time * 1000,
            "datetime": dt,
        }

        # 存入对象池
        self.buy_guard_pool.append(params)
        self.buy_guard_db_index += 1
        if self.buy_guard_db_index >= self.buy_guard_db_threshold:
            self.buy_guard_commit_pool = copy.deepcopy(self.buy_guard_pool)
            self.buy_guard_pool = []

            logger.info(
                "insert buy_guard successfully,count = "
                + str(self.buy_guard_db_index)
                + ", pool count = "
                + str(len(self.buy_guard_commit_pool))
            )
            self.buy_guard_db_index = 0
            cursor.executemany(insert_buy_guard_table_sql, self.buy_guard_commit_pool)
            connection.commit()

        # 付费次数
        if client.room_id not in self.pay_count_dict.keys():
            self.pay_count_dict[client.room_id] = 0
        self.pay_count_dict[client.room_id] += 1
        logger.info(
            f"[{client.room_id}] [{dt}] 付费次数： {self.pay_count_dict[client.room_id]}"
        )

        # 需要保存营收分钟表
        # 取出当前房间号的缓存数据，分钟数和营收数
        # 舰长使用的是金瓜子
        current_income = round(float(message.price * message.num) / 1000, 1)
        self.accumulate_income_minute(client.room_id, seconds, current_income)

    def _on_user_toast_v2(
        self, client: blivedm.BLiveClient, message: web_models.UserToastV2Message
    ):
        # 确保房间已初始化
        self.ensure_room_initialized(client.room_id)
        
        seconds = message.start_time
        dt = datetime.fromtimestamp(seconds).strftime("%Y-%m-%d %H:%M:%S")
        logger.info(
            f"[{client.room_id}] [{dt}] {message.username} 上舰，guard_level={message.guard_level}"
        )

        current_liveid = self.room_live_status.get(client.room_id, {}).get('live_id')

        params = {
            "room_id": client.room_id,
            "live_id": current_liveid,
            "user_id": message.uid,
            "user_name": message.username,
            "privilege_type": message.guard_level,
            "gift_id": message.gift_id,
            "gift_num": message.num,
            "gift_per_price": message.price,
            "gift_unit": message.unit,
            "source": message.source,
            "toast_msg": message.toast_msg,
            "timestamp": message.start_time * 1000,
            "datetime": dt,
        }

        # 存入对象池
        self.user_toast_v2_pool.append(params)
        self.user_toast_v2_db_index += 1
        if self.user_toast_v2_db_index >= self.user_toast_v2_db_threshold:
            self.user_toast_v2_commit_pool = copy.deepcopy(self.user_toast_v2_pool)
            self.user_toast_v2_pool = []

            logger.info(
                "insert user_toast_v2 successfully,count = "
                + str(self.user_toast_v2_db_index)
                + ", pool count = "
                + str(len(self.user_toast_v2_commit_pool))
            )
            self.user_toast_v2_db_index = 0
            cursor.executemany(
                insert_user_toast_v2_table_sql, self.user_toast_v2_commit_pool
            )
            connection.commit()

    def _on_super_chat(
        self, client: blivedm.BLiveClient, message: web_models.SuperChatMessage
    ):
        # 确保房间已初始化
        self.ensure_room_initialized(client.room_id)
        
        seconds = message.start_time
        dt = datetime.fromtimestamp(seconds).strftime("%Y-%m-%d %H:%M:%S")
        logger.info(
            f"[{client.room_id}] [{dt}] 醒目留言 ¥{message.price} {message.uname}：{message.message}"
        )

        current_liveid = self.room_live_status.get(client.room_id, {}).get('live_id')

        params = {
            "room_id": client.room_id,
            "live_id": current_liveid,
            "user_id": message.uid,
            "user_name": message.uname,
            "user_face": message.face,
            "user_level": message.user_level,
            "privilege_type": message.guard_level,
            "super_chat_id": message.id,
            "price": message.price,
            "super_chat_msg": message.message,
            "available_timestamp": message.time,
            "gift_id": message.gift_id,
            "gift_name": message.gift_name,
            "gift_num": message.gift_num,
            "medal_level": message.medal_level,
            "medal_name": message.medal_name,
            "medal_room_id": message.medal_room_id,
            "medal_room_uid": message.medal_ruid,
            "start_timestamp": message.start_time * 1000,
            "end_timestamp": message.end_time * 1000,
            "datetime": dt,
        }

        # 存入对象池
        self.super_chat_pool.append(params)
        self.super_chat_db_index += 1
        if self.super_chat_db_index >= self.super_chat_db_threshold:
            self.super_chat_commit_pool = copy.deepcopy(self.super_chat_pool)
            self.super_chat_pool = []

            logger.info(
                "insert super_chat successfully,count = "
                + str(self.super_chat_db_index)
                + ", pool count = "
                + str(len(self.super_chat_commit_pool))
            )
            self.super_chat_db_index = 0
            cursor.executemany(insert_super_chat_table_sql, self.super_chat_commit_pool)
            connection.commit()

        # 付费次数
        if client.room_id not in self.pay_count_dict.keys():
            self.pay_count_dict[client.room_id] = 0
        self.pay_count_dict[client.room_id] += 1
        logger.info(
            f"[{client.room_id}] [{dt}] 付费次数： {self.pay_count_dict[client.room_id]}"
        )

        # 需要保存营收分钟表
        # 取出当前房间号的缓存数据，分钟数和营收数
        # SC使用的是人民币
        current_income = round(float(message.price * message.gift_num), 1)
        self.accumulate_income_minute(client.room_id, seconds, current_income)

    def _on_interact_word(
        self, client: blivedm.BLiveClient, message: web_models.InteractWordMessage
    ):
        # 确保房间已初始化
        self.ensure_room_initialized(client.room_id)
        
        # 判断当前房间是否处于开播状态
        if str(client.room_id) not in self.is_in_live_symbol_dict.keys():
            return
        if not self.is_in_live_symbol_dict[str(client.room_id)]:
            return
        
        seconds = message.timestamp
        dt = datetime.fromtimestamp(seconds).strftime("%Y-%m-%d %H:%M:%S")

        temp_interact_word = {}
        current_liveid = self.room_live_status.get(client.room_id, {}).get('live_id')

        if message.msg_type == 1:
            temp_interact_word["action"] = "进入房间"
            self.save_enter_room_info_to_db(client.room_id, current_liveid, message.timestamp, message.uid, message.username, message.face)

        if message.msg_type == 2:
            logger.info(f"[{client.room_id}] [{dt}] {message.username} 关注了主播")
            temp_interact_word["action"] = "关注了主播"
        elif message.msg_type == 3:
            logger.info(f"[{client.room_id}] [{dt}] {message.username} 分享了房间")
            temp_interact_word["action"] = "分享了房间"
        elif message.msg_type == 4:
            logger.info(f"[{client.room_id}] [{dt}] {message.username} 特别关注了主播")
            temp_interact_word["action"] = "特别关注了主播"
        elif message.msg_type == 5:
            logger.info(f"[{client.room_id}] [{dt}] {message.username} 与主播互粉了")
            temp_interact_word["action"] = "与主播互粉了"
        elif message.msg_type == 6:
            logger.info(f"[{client.room_id}] [{dt}] {message.username} 为主播点赞了")
            temp_interact_word["action"] = "为主播点赞了"

        # 记录活跃观众（参与互动的用户）
        # 初始化该房间的活跃观众集合（如果不存在）
        if client.room_id not in self.active_watcher_user_ids_dict:
            self.active_watcher_user_ids_dict[client.room_id] = set()
        
        # 将用户ID添加到活跃观众集合中
        self.active_watcher_user_ids_dict[client.room_id].add(message.uid)
        
        # 进入房间，这个不存入互动大表
        if message.msg_type != 1:
            params = {
                "room_id": client.room_id,
                "live_id": current_liveid,
                "user_id": message.uid,
                "user_name": message.username,
                "user_face": message.face,
                "msg_type": message.msg_type,
                "msg_text": temp_interact_word["action"],
                "timestamp": message.timestamp * 1000,
                "datetime": dt,
            }

            # 存入对象池
            self.interact_word_pool.append(params)
            self.interact_word_db_index += 1
            if self.interact_word_db_index >= self.interact_word_db_threshold:
                self.interact_word_commit_pool = copy.deepcopy(self.interact_word_pool)
                self.interact_word_pool = []

                logger.info(
                    "insert interact_word successfully,count = "
                    + str(self.interact_word_db_index)
                    + ", pool count = "
                    + str(len(self.interact_word_commit_pool))
                )
                self.interact_word_db_index = 0
                cursor.executemany(
                    insert_interact_word_table_sql, self.interact_word_commit_pool
                )
                connection.commit()

        # 需要保存进入房间人次分钟表
        # 取出当前房间号的缓存数据，分钟数和进入房间观众数
        if (
            client.room_id
            not in self.to_save_minute_enter_room_count_minute_dict.keys()
        ):
            self.to_save_minute_enter_room_count_minute_dict[client.room_id] = 0
        if client.room_id not in self.temp_enter_room_count_minute_dict.keys():
            self.temp_enter_room_count_minute_dict[client.room_id] = 0

        cur_minute = math.floor(seconds / 60)
        if (
            cur_minute
            != self.to_save_minute_enter_room_count_minute_dict[client.room_id]
        ):
            # # 执行sql存数据库（互动次数分钟表），存的是上一分钟的数据
            if self.to_save_minute_enter_room_count_minute_dict[client.room_id] != 0:
                self.save_enter_room_count_minute_to_db(client.room_id)
            # 更新记录分钟和缓存人数
            self.temp_enter_room_count_minute_dict[client.room_id] = 0
            self.to_save_minute_enter_room_count_minute_dict[client.room_id] = (
                cur_minute
            )
        else:
            # 这里只统计进入房间消息
            if message.msg_type == 1:
                # 在同一分钟内不断自增
                self.temp_enter_room_count_minute_dict[client.room_id] += 1

        # 需要保存互动次数分钟表
        # 取出当前房间号的缓存数据，分钟数和房间观众数
        if (
            client.room_id
            not in self.to_save_minute_interact_word_count_minute_dict.keys()
        ):
            self.to_save_minute_interact_word_count_minute_dict[client.room_id] = 0
        if client.room_id not in self.temp_interact_word_count_minute_dict.keys():
            self.temp_interact_word_count_minute_dict[client.room_id] = 0

        cur_minute = math.floor(seconds / 60)
        if (
            cur_minute
            != self.to_save_minute_interact_word_count_minute_dict[client.room_id]
        ):
            # # 执行sql存数据库（互动次数分钟表），存的是上一分钟的数据
            if self.to_save_minute_interact_word_count_minute_dict[client.room_id] != 0:
                self.save_interact_word_count_minute_to_db(client.room_id)
            # 更新记录分钟和缓存人数
            self.temp_interact_word_count_minute_dict[client.room_id] = 0
            self.to_save_minute_interact_word_count_minute_dict[client.room_id] = (
                cur_minute
            )
        else:
            # 进入房间不计入互动次数
            if message.msg_type != 1:
                # 在同一分钟内不断自增
                self.temp_interact_word_count_minute_dict[client.room_id] += 1

    def save_enter_room_info_to_db(self, room_id, live_id, timestamp, uid, uname, face):
        room_id_str = str(room_id)
        if room_id_str not in self.enter_room_info_pools:
            self.enter_room_info_pools[room_id_str] = []
        if room_id_str not in self.enter_room_info_commit_pools:
            self.enter_room_info_commit_pools[room_id_str] = []
        if room_id_str not in self.enter_room_info_db_indices:
            self.enter_room_info_db_indices[room_id_str] = 0
        
        enter_params = {
            "room_id": room_id_str,
            "live_id": str(live_id),
            "timestamp": timestamp,
            "uid": uid,
            "uname": uname,
            "face": face
        }
        self.enter_room_info_pools[room_id_str].append(enter_params)
        self.enter_room_info_db_indices[room_id_str] += 1

        if self.enter_room_info_db_indices[room_id_str] >= self.enter_room_info_db_threshold:
            self.enter_room_info_commit_pools[room_id_str] = copy.deepcopy(self.enter_room_info_pools[room_id_str])
            self.enter_room_info_pools[room_id_str] = []

            logger.info(
                f"insert enter_room_info to {get_enter_room_user_info_table_name(room_id_str)} successfully, count = "
                + str(self.enter_room_info_db_indices[room_id_str])
                + ", pool count = "
                + str(len(self.enter_room_info_commit_pools[room_id_str]))
            )
            self.enter_room_info_db_indices[room_id_str] = 0
            
            insert_sql = get_insert_enter_room_user_info_table_sql(room_id_str)
            cursor.executemany(insert_sql, self.enter_room_info_commit_pools[room_id_str])
            connection.commit()

    def save_enter_room_count_minute_to_db(self, room_id):
        # 判断当前房间是否处于开播状态
        if str(room_id) not in self.is_in_live_symbol_dict.keys():
            return
        if not self.is_in_live_symbol_dict[str(room_id)]:
            return
        
        # 需要算出待存的秒级时间戳
        to_save_second = self.to_save_minute_enter_room_count_minute_dict[room_id] * 60
        to_save_datetime = datetime.fromtimestamp(to_save_second).strftime(
            "%Y-%m-%d %H:%M:%S"
        )

        current_liveid = self.room_live_status.get(room_id, {}).get('live_id')

        params = {
            "room_id": room_id,
            "live_id": current_liveid,
            "count": self.temp_enter_room_count_minute_dict[room_id],
            "timestamp": to_save_second,
            "datetime": to_save_datetime,
        }

        cursor.execute(insert_enter_room_count_minute_table_sql, params)
        connection.commit()
        logger.info(
            f"[{room_id}] [{to_save_datetime}] 存入DB，进入房间人次：{self.temp_enter_room_count_minute_dict[room_id]}"
        )

    def save_interact_word_count_minute_to_db(self, room_id):
        # 判断当前房间是否处于开播状态
        if str(room_id) not in self.is_in_live_symbol_dict.keys():
            return
        if not self.is_in_live_symbol_dict[str(room_id)]:
            return
        
        # 需要算出待存的秒级时间戳
        to_save_second = (
            self.to_save_minute_interact_word_count_minute_dict[room_id] * 60
        )
        to_save_datetime = datetime.fromtimestamp(to_save_second).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        current_liveid = self.room_live_status.get(room_id, {}).get('live_id')

        params = {
            "room_id": room_id,
            "live_id": current_liveid,
            "count": self.temp_interact_word_count_minute_dict[room_id],
            "timestamp": to_save_second,
            "datetime": to_save_datetime,
        }

        cursor.execute(insert_interact_word_count_minute_table_sql, params)
        connection.commit()
        logger.info(
            f"[{room_id}] [{to_save_datetime}] 存入DB，互动次数：{self.temp_interact_word_count_minute_dict[room_id]}"
        )
        
    def save_active_watcher_count_minute_to_db(self, room_id):
        # 判断当前房间是否处于开播状态
        if str(room_id) not in self.is_in_live_symbol_dict.keys():
            return
        if not self.is_in_live_symbol_dict[str(room_id)]:
            return
        
        # 需要算出待存的秒级时间戳
        to_save_second = (
            self.to_save_minute_active_watcher_count_minute_dict[room_id] * 60
        )
        to_save_datetime = datetime.fromtimestamp(to_save_second).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        current_liveid = self.room_live_status.get(room_id, {}).get('live_id')
        
        # 获取活跃观众数量（参与过互动的不同用户的数量）
        active_watcher_count = 0
        if room_id in self.active_watcher_user_ids_dict:
            active_watcher_count = len(self.active_watcher_user_ids_dict[room_id])

        params = {
            "room_id": room_id,
            "live_id": current_liveid,
            "count": active_watcher_count,
            "timestamp": to_save_second,
            "datetime": to_save_datetime,
        }

        cursor.execute(insert_active_watcher_count_minute_table_sql, params)
        connection.commit()
        logger.info(
            f"[{room_id}] [{to_save_datetime}] 存入DB，活跃观众数量：{active_watcher_count}"
        )

    def _on_watch_change(
        self, client: blivedm.BLiveClient, message: web_models.WatchChangeMessage
    ):
        # 确保房间已初始化
        self.ensure_room_initialized(client.room_id)
        
        cur_timestamp = int(round(time.time()))  # 单位：秒
        cur_dt = datetime.fromtimestamp(cur_timestamp).strftime("%Y-%m-%d %H:%M:%S")

        watch_count = message.num

        # 最新的watch_count覆盖到临时字典里
        if client.room_id not in self.watch_change_dict:
            self.watch_change_dict[client.room_id] = 0
        self.watch_change_dict[client.room_id] = watch_count

        logger.info(f"[{client.room_id}] [{cur_dt}] {watch_count}人看过")

    def _on_like_info_update(
        self, client: blivedm.BLiveClient, message: web_models.LikeInfoUpdateMessage
    ):
        # 确保房间已初始化
        self.ensure_room_initialized(client.room_id)
        
        cur_timestamp = int(round(time.time()))  # 单位：秒
        cur_dt = datetime.fromtimestamp(cur_timestamp).strftime("%Y-%m-%d %H:%M:%S")

        like_count = message.click_count

        if client.room_id not in self.like_info_update_dict:
            self.like_info_update_dict[client.room_id] = 0
        self.like_info_update_dict[client.room_id] = like_count

        logger.info(f"[{client.room_id}] [{cur_dt}] {like_count}点赞")

    def _on_start_live(
        self, client: blivedm.BLiveClient, message: web_models.StartLiveMessage
    ):
        # 确保房间已初始化
        self.ensure_room_initialized(client.room_id)
        
        cur_timestamp = time.time()  # 单位：秒
        cur_dt = datetime.fromtimestamp(cur_timestamp).strftime("%Y-%m-%d %H:%M:%S")
        logger.info(
            f"[{client.room_id}] [{cur_dt}] {message.room_id}开始直播，live_id = {message.live_key}"
        )

        # 如果字典没有这个房间id的键
        if str(message.room_id) not in self.is_in_live_symbol_dict.keys():
            self.is_in_live_symbol_dict[str(message.room_id)] = True
        # 设置正在直播的标志位
        self.is_in_live_symbol_dict[str(message.room_id)] = True

    def _on_online_rank_count(
        self, client: blivedm.BLiveClient, message: web_models.OnlineRankCountMessage
    ):
        # 确保房间已初始化
        self.ensure_room_initialized(client.room_id)
        
        cur_timestamp = int(round(time.time()))  # 单位：秒
        cur_dt = datetime.fromtimestamp(cur_timestamp).strftime("%Y-%m-%d %H:%M:%S")

        # 取出当前房间号的缓存数据，分钟数和房间观众数
        if (
            client.room_id
            not in self.to_save_minute_online_rank_count_minute_dict.keys()
        ):
            self.to_save_minute_online_rank_count_minute_dict[client.room_id] = 0
        if client.room_id not in self.temp_online_rank_count_minute_dict.keys():
            self.temp_online_rank_count_minute_dict[client.room_id] = 0

        cur_minute = math.floor(cur_timestamp / 60)
        if (
            cur_minute
            != self.to_save_minute_online_rank_count_minute_dict[client.room_id]
        ):
            # 这里是一定会下发的，所以把【【【营收】】】也放在这里存。执行sql存数据库（付费榜人数分钟表），存的是上一分钟的数据
            if self.to_save_minute_online_rank_count_minute_dict[client.room_id] != 0:
                self.save_online_rank_count_minute_to_db(client.room_id)
                self.save_income_minute_to_db(client.room_id)
                self.save_danmu_count_minute_to_db(client.room_id)
                
                # 保存活跃观众数量
                if client.room_id not in self.to_save_minute_active_watcher_count_minute_dict:
                    self.to_save_minute_active_watcher_count_minute_dict[client.room_id] = 0
                self.to_save_minute_active_watcher_count_minute_dict[client.room_id] = cur_minute
                self.save_active_watcher_count_minute_to_db(client.room_id)

                # 每分钟获取直播状态
                self.get_live_status(client.room_id, cur_minute * 60)

            # 更新记录分钟和缓存人数
            self.temp_online_rank_count_minute_dict[client.room_id] = message.count
            self.to_save_minute_online_rank_count_minute_dict[client.room_id] = (
                cur_minute
            )
            # 更新营收记录分钟和缓存营收
            self.temp_income_minute_dict[client.room_id] = 0
            self.to_save_minute_income_minute_dict[client.room_id] = cur_minute
            # 更新弹幕数记录分钟和缓存分钟弹幕数
            self.temp_danmu_count_minute_dict[client.room_id] = 0
            self.to_save_minute_danmu_count_minute_dict[client.room_id] = cur_minute
        else:
            # 不断覆盖
            self.temp_online_rank_count_minute_dict[client.room_id] = message.count

        count = message.count
        logger.info(f"[{client.room_id}] [{cur_dt}] 高能榜人数：{count}")

    def save_online_rank_count_minute_to_db(self, room_id):
        # 判断当前房间是否处于开播状态
        if str(room_id) not in self.is_in_live_symbol_dict.keys():
            return
        if not self.is_in_live_symbol_dict[str(room_id)]:
            return
        
        # 需要算出待存的秒级时间戳
        to_save_second = self.to_save_minute_online_rank_count_minute_dict[room_id] * 60
        to_save_datetime = datetime.fromtimestamp(to_save_second).strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        current_liveid = self.room_live_status.get(room_id, {}).get('live_id')

        params = {
            "room_id": room_id,
            "live_id": current_liveid,
            "count": self.temp_online_rank_count_minute_dict[room_id],
            "timestamp": to_save_second,
            "datetime": to_save_datetime,
        }

        cursor.execute(insert_online_rank_count_minute_table_sql, params)
        connection.commit()
        logger.info(
            f"[{room_id}] [{to_save_datetime}] 存入DB，高能榜人数：{self.temp_online_rank_count_minute_dict[room_id]}"
        )

    def get_live_status(self, room_id, in_seconds=0):
        cur_timestamp = in_seconds
        # 如果没传入整分钟的时间戳，那就拿当前时间戳
        if cur_timestamp == 0:
            cur_timestamp = int(round(time.time()))  # 单位：秒
        dt = datetime.fromtimestamp(cur_timestamp).strftime("%Y-%m-%d %H:%M:%S")

        if room_id not in self.temp_live_status_minute_dict.keys():
            self.temp_live_status_minute_dict[room_id] = -1
            
        # 初始化房间直播状态字典
        self.ensure_room_initialized(room_id)

        data = None
        try:
            session = requests.Session()
            retries = Retry(
                total=3, backoff_factor=1, status_forcelist=[500, 502, 503, 504]
            )
            session.mount("https://", HTTPAdapter(max_retries=retries))

            response = session.get(
                "https://api.live.bilibili.com/room/v1/Room/get_info",
                params={"room_id": room_id},
                headers={
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
                    "Referer": f"https://live.bilibili.com/{room_id}",
                },
                timeout=5,
            )
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 0:
                data = result["data"]
                if data["live_status"] == 1:
                    start_time_str = data["live_time"]
                    dt_obj = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
                    logger.info(
                        f"[{room_id}] [{dt}] 直播状态：直播中, live_start_time = {start_time_str},"
                        f" timestamp = {int(dt_obj.timestamp())}, title = {data['title']}"
                    )
                elif data["live_status"] == 2:
                    logger.info(
                        f"[{room_id}] [{dt}] 直播状态：轮播中, title = {data['title']}"
                    )
                elif data["live_status"] == 0:
                    logger.info(f"[{room_id}] [{dt}] 直播状态：未开播")
            else:
                logger.info(f"API错误: {result.get('message')} (code:{result.get('code')})")
                return  

        except requests.exceptions.RequestException as e:
            logger.info(f"网络请求异常: {str(e)}")
            return 
        except KeyError as e:
            logger.info(f"响应数据格式异常，缺失字段: {str(e)}")
            return  

        if not data:
            return  

        # 确保temp_live_status_minute_dict已初始化
        if room_id not in self.temp_live_status_minute_dict:
            self.temp_live_status_minute_dict[room_id] = -1

        # 从0、2变为1是开始直播，从1变为0、2是结束直播
        live_action = "无"
        current_liveid = None

        if self.room_live_status[room_id]['live_id'] == None and data['live_status'] == 1:
            # TODO：考虑程序中断时，self.room_live_status[room_id]['live_id']继承并未下播的liveid
            self.room_live_status[room_id]['live_id'] = str(uuid.uuid4())

        # 检查直播状态变化并更新liveid
        if self.temp_live_status_minute_dict[room_id] == 0 or self.temp_live_status_minute_dict[room_id] == 2:
            if data['live_status'] == 1:
                live_action = '开始直播'
                self.room_live_status[room_id]['start_time'] = dt
                # 设置直播状态为True，允许保存数据
                self.is_in_live_symbol_dict[str(room_id)] = True
                self.room_live_status[room_id]['live_id'] = str(uuid.uuid4())
                logger.info(f"[{room_id}] [{dt}] 生成新的liveid: {self.room_live_status[room_id]['live_id']}")
        elif self.temp_live_status_minute_dict[room_id] == 1:
            if data['live_status'] == 0 or data['live_status'] == 2:
                live_action = '结束直播'
                # 设置直播状态为False，停止保存数据
                self.is_in_live_symbol_dict[str(room_id)] = False
        
        # 更新当前直播状态
        self.room_live_status[room_id]['live_status'] = data['live_status']
        previous_live_status = self.temp_live_status_minute_dict[room_id]
        self.temp_live_status_minute_dict[room_id] = data['live_status']
        
        # 如果当前状态是直播中，确保is_in_live_symbol_dict为True
        if data['live_status'] == 1:
            self.is_in_live_symbol_dict[str(room_id)] = True
        else:
            # 如果不是直播中，设置为False
            self.is_in_live_symbol_dict[str(room_id)] = False
        
        # 获取当前liveid
        current_liveid = self.room_live_status[room_id]['live_id']

        params = {
            'room_id': room_id,
            'live_id': current_liveid,
            'live_status': data['live_status'],
            'live_action': live_action,
            'title': data['title'],
            'cover': data['user_cover'],
            'start_time': data['live_time'] if data['live_time'] != "0000-00-00 00:00:00" else None,
            'parent_area': data['parent_area_name'],
            'area': data['area_name'],
            'timestamp': cur_timestamp,
            'datetime': dt,
        }

        cursor.execute(insert_live_status_minute_table_sql, params)
        connection.commit()
        logger.info(f"[{room_id}] [{dt}] 存入DB，直播状态：{data['live_status']}，直播动作：{live_action}")

        # 判断直播动作
        if (previous_live_status == 0 or previous_live_status == 2) and data["live_status"] == 1:
            live_action = "开始直播"
            # 更新开播时间等逻辑已在前面处理
        elif previous_live_status == 1 and (data["live_status"] == 0 or data["live_status"] == 2):
            live_action = "结束直播"
            # 确保 live_id 存在才执行后续操作
            if current_liveid:
                # 下播时将看过和点赞存入数据库
                self.save_watch_change_to_db(room_id, cur_timestamp, dt)
                self.save_like_info_update_to_db(room_id, cur_timestamp, dt)

                watch_change_count = 0
                if room_id in self.watch_change_dict.keys():
                    watch_change_count = self.watch_change_dict[room_id]
                like_info_update_count = 0
                if room_id in self.like_info_update_dict.keys():
                    like_info_update_count = self.like_info_update_dict[room_id]

                # 上面的结束直播的状态可能没结束
                # 需要根据当前结束直播的时间找到最近的开始直播的时间
                # 就是上一个“开始直播”的时间点
                start_live_time_str, temp_execution_time = query_live_start_time_by_end_time(room_id, dt)

                pay_count, total_income, pay_result, execution_time = \
                    query_pay_count_by_room_and_live_start_end_time(room_id, start_live_time_str, dt)
                
                _, danmu_count = query_danmu_by_room_and_datetime(room_id, start_live_time_str, dt)

                interaction_count = query_interact_word_count_by_room_and_datetime(room_id, start_live_time_str, dt)
                enter_room_count = query_total_enter_room_count_by_room_and_datetime(room_id, start_live_time_str, dt)
                max_online_rank = query_max_online_rank_count_by_room_and_datetime(room_id, start_live_time_str, dt)
                ave_online_rank = query_average_online_rank_count_by_room_and_datetime(room_id, start_live_time_str, dt)
                ave_enter_room= query_average_enter_room_count_by_room_and_datetime(room_id, start_live_time_str, dt)            

                # TODO: employment of IsFull here
                self.save_live_session_to_db(room_id, current_liveid, data['title'], data['user_cover'], data['parent_area_name'], data['area_name'], start_live_time_str, dt, pay_count, total_income, watch_change_count, like_info_update_count, danmu_count, interaction_count, max_online_rank, enter_room_count, ave_online_rank, ave_enter_room, cur_timestamp, dt)
                self.room_live_status[room_id]['live_id'] = None


    def save_watch_change_to_db(self, room_id, seconds, dt):
        if room_id not in self.watch_change_dict.keys():
            return
            
        current_liveid = self.room_live_status.get(room_id, {}).get('live_id')

        params = {
            "room_id": room_id,
            "live_id": current_liveid,
            "count": self.watch_change_dict[room_id],
            "timestamp": seconds,
            "datetime": dt,
        }

        cursor.execute(insert_watch_change_table_sql, params)
        connection.commit()
        logger.info(
            f"[{room_id}] [{dt}] 存入DB，当前场次 观看人次：{self.watch_change_dict[room_id]}"
        )

    def save_like_info_update_to_db(self, room_id, seconds, dt):
        if room_id not in self.like_info_update_dict.keys():
            return
            
        current_liveid = self.room_live_status.get(room_id, {}).get('live_id')

        params = {
            "room_id": room_id,
            "live_id": current_liveid,
            "count": self.like_info_update_dict[room_id],
            "timestamp": seconds,
            "datetime": dt,
        }

        cursor.execute(insert_like_info_update_table_sql, params)
        connection.commit()
        logger.info(
            f"[{room_id}] [{dt}] 存入DB，当前场次 点赞次数：{self.like_info_update_dict[room_id]}"
        )

    def save_live_session_to_db(
        self,
        room_id,
        live_id,
        title,
        cover,
        parent_area,
        area,
        start_time_str,
        end_time_str,
        pay_count,
        income,
        watch_change_count,
        like_info_update_count,
        danmu_count,
        interaction_count,
        max_online_rank,
        enter_room_count,
        ave_online_rank,
        ave_enter_room,
        seconds,
        dt,
    ):
        
        params = {
            "room_id": room_id,
            "live_id": None if live_id is None else live_id,
            "title": title,
            "cover": cover,
            "parent_area": parent_area,
            "area": area,
            "start_time_str": start_time_str,
            "end_time_str": end_time_str,
            "pay_count": pay_count,
            "income": income,
            "watch_change_count": watch_change_count,
            "like_info_update_count": like_info_update_count,
            "danmu_count": danmu_count,
            "interaction_count": interaction_count,
            "max_online_rank": max_online_rank,
            "enter_room_count": enter_room_count,
            "ave_online_rank": ave_online_rank,
            "ave_enter_room": ave_enter_room,
            "timestamp": seconds,
            "datetime": dt,
        }

        cursor.execute(insert_live_session_table_sql, params)
        connection.commit()
        logger.info(
            f"[{room_id}] [{dt}] 存入DB，当前场次 开始时间：{start_time_str}，结束时间：{end_time_str}，"
            f"付费次数：{pay_count}，总营收：{income}，观看人次：{watch_change_count}，点赞次数：{like_info_update_count}, 弹幕数：{danmu_count}，互动数：{interaction_count}，最高在线人数：{max_online_rank}, 平均在线人数：{ave_online_rank}"
        )


if __name__ == "__main__":
    asyncio.run(main())
