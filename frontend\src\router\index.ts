import { createRouter, createWebHistory } from 'vue-router';
import HomeView from '../views/HomeView.vue';
import LoginView from '../views/LoginView.vue'; // Import the LoginView
import PersonalSpace from '../views/PersonalSpace.vue';
import SignUpView from '../views/SignUpView.vue'; // Import the SignUpView

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/personal/:vtuber?',
      name: 'personalSpace',
      component: PersonalSpace,
    },
    {
      path: '/dynamics/:vtuber?',
      name: 'dynamics',
      component: () => import('../views/Dynamics.vue'),
    },
    {
      path: '/videos/:vtuber?',
      name: 'videos',
      component: () => import('../views/Videos.vue'),
    },
    {
      path: '/comments/:vtuber?',
      name: 'comments',
      component: () => import('../views/Comments.vue'),
    },
    {
      path: '/live-stream/:vtuber?',
      name: 'live-stream',
      component: () => import('../views/LivesStreamHost.vue'),
    },
    {
      path: '/comparison',
      name: 'comparison',
      component: () => import('../views/Comparison.vue'),
    },
    {
      path: '/user-analyze',
      name: 'userAnalyze',
      component: () => import('../views/UserAnalyze.vue'),
    },
    {
      path: '/creator-info',
      name: 'creatorInfo',
      component: () => import('../views/CreatorInfo.vue'),
      meta: {
        title: '创作者数据分析',
        description: '查看创作者的详细数据统计和分析报告'
      }
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView,
    },
    {
      path: '/signup',
      name: 'signup',
      component: SignUpView,
    },
    {
      path: '/announcement',
      name: 'announcement',
      component: () => import('../views/AnnouncementView.vue'),
    },
  ],
})

// router.beforeEach((to, from, next) => {
//   const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
//   const authRequiredRoutes = !['login', 'signup'].includes(to.name as string);

//   if (authRequiredRoutes && !isLoggedIn) {
//     next({ name: 'login' });
//   } else if (['login', 'signup'].includes(to.name as string) && isLoggedIn) {
//     next({ name: 'home' });
//   } else if (to.name === 'live-stream' && !to.params.vtuber && isLoggedIn) {
//     next({ name: 'live-stream', params: { vtuber: '星瞳' } });
//   } else {
//     next();
//   }
// })

export default router
