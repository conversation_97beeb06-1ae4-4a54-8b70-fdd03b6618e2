<template>
  <div class="space-y-6">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-3xl font-bold text-gray-800 dark:text-white">评论分析</h2>
      <div class="flex items-center">
        <span class="mr-2 text-gray-700 dark:text-gray-300">允许拖拽</span>
        <label class="switch">
          <input type="checkbox" v-model="isDraggable" />
          <span class="slider round"></span>
        </label>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-[1fr_3fr_2fr] gap-6">
      <!-- Left Column -->
      <div class="space-y-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
          <h3 class="text-xl font-bold mb-4 text-gray-800 dark:text-white">
            选选想看的时间范围吧
          </h3>
          <div class="space-y-4">
            <div>
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >起始日期</label
              >
              <input
                type="date"
                v-model="startDate"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >结束日期</label
              >
              <input
                type="date"
                v-model="endDate"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >评论数</label
              >
              <input
                type="number"
                v-model="topN"
                min="1"
                max="100"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
          </div>
          <button
            @click="handleClick"
            class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          >
            更新数据
          </button>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h3 class="text-xl font-bold mb-4 text-gray-800 dark:text-white">
            评论词云图
          </h3>
          <div
            v-if="isWordCloudLoading"
            class="flex justify-center items-center h-[400px] animate-pulse"
          >
            <div class="loading-icon text-4xl mb-4">⌛</div>
            <p class="text-gray-600 dark:text-gray-400 font-medium">
              词云正在生成中
            </p>
          </div>
          <div
            v-else-if="!wordCloudData"
            class="flex justify-center items-center h-[400px]"
          >
            <p class="text-gray-600 dark:text-gray-400">没有词云数据捏</p>
          </div>
          <div v-else class="flex justify-center">
            <img
              :src="wordCloudData"
              alt="Word Cloud"
              class="max-w-full max-h-[400px] object-contain"
            />
          </div>
        </div>
      </div>

      <!-- Middle Column -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-xl font-bold mb-4 text-gray-800 dark:text-white">
          热度前 {{ topN }} 评论
        </h3>

        <div v-if="!isDataLoading">
          <div
            v-if="comments.length === 0"
            class="text-center text-gray-600 dark:text-gray-400 font-medium"
          >
            <p>这个时间段还没收集评论呢，快去push一下！ORZ</p>
          </div>
          <VueDraggable
            v-model="comments"
            :item-key="(element: Comment) => `comment-${element.uid}`"
            class="space-y-4 overflow-y-auto pr-2"
            :disabled="!isDraggable"
            :key="'comments-' + (isDraggable ? 'draggable' : 'static')"
          >
            <template #item="{ element }">
              <div
                class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 shadow hover:shadow-md transition-shadow duration-300"
                :class="{
                  'cursor-move': isDraggable,
                  'cursor-default': !isDraggable,
                }"
              >
                <div class="flex items-center mb-2">
                  <div
                    class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-2"
                  >
                    <!-- <i class="fas fa-user"></i> -->
                    <img
                      :src="element.face"
                      alt="User Photo"
                      class="rounded-full w-full h-full object-cover"
                    />
                  </div>
                  <h4 class="font-semibold text-gray-800 dark:text-white">
                    {{ element.name }}
                  </h4>
                </div>
                <p class="text-gray-700 dark:text-gray-300">
                  {{ element.comment }}
                </p>
                <div
                  class="mt-2 text-sm text-gray-500 dark:text-gray-400 flex justify-between"
                >
                  <span class="flex items-center"
                    ><i class="fas fa-fire mr-1"></i>{{ element.heat }}</span
                  >
                  <a
                    :href="element.source === 'video' ? `https://www.bilibili.com/video/${element.from_id}` : `https://www.bilibili.com/opus/${element.from_id}`"
                    target="_blank"
                    class="flex items-center hover:text-blue-500"
                  >
                    <i class="fas fa-video mr-1"></i>{{ element.from_name }}
                  </a>
                </div>
                <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {{ formatDate(element.time) }}
                </div>
              </div>
            </template>
          </VueDraggable>
        </div>
        <div
          v-else
          class="flex flex-col items-center justify-center animate-pulse"
        >
          <div class="loading-icon text-4xl mb-4">⌛</div>
          <div class="text-gray-600 dark:text-gray-400 font-medium">
            表格构建中...
          </div>
        </div>
      </div>

      <!-- Right Column (New) -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold text-gray-800 dark:text-white">
            评论聚类
          </h3>
          <select
            v-model="selectedTimeRange"
            @change="fetchTopics"
            class="bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5"
          >
            <option :value="1">今天</option>
            <option :value="7">最近一周</option>
            <option :value="30">最近一月</option>
            <option :value="90">最近三月</option>
          </select>
        </div>
        <div
          v-if="topicsLoading"
          class="flex justify-center items-center py-8 animate-pulse"
        >
          <div class="loading-icon text-4xl mb-4">☕</div>
          <div class="text-gray-600 dark:text-gray-400 font-medium">
            话题生成大概五分钟，喝杯咖啡再来看看吧...
          </div>
        </div>
        <div
          v-else-if="topics.length === 0"
          class="flex justify-center items-center"
        >
          <p class="text-gray-600 dark:text-gray-400 font-medium">
            暂无话题数据
          </p>
        </div>
        <div v-else class="space-y-4 overflow-y-auto pr-2">
          <div
            v-for="topic in topics"
            :key="topic.rank"
            class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 shadow"
          >
            <h4 class="font-semibold text-gray-800 dark:text-white mb-2">
              {{ topic.topic }}
            </h4>
            <div class="flex items-center mb-2">
              <!-- <span class="text-sm text-gray-500 dark:text-gray-400 mr-4"
                >Rank: {{ topic.rank }}</span
              > -->
              <span class="text-sm text-gray-500 dark:text-gray-400"
                >Heat: {{ getHeatSymbols(topic.heat) }}</span
              >
            </div>
            <div class="mb-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >Keywords:</span
              >
              <div class="flex flex-wrap gap-2 mt-1">
                <span
                  v-for="keyword in topic.keywords"
                  :key="keyword"
                  class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300"
                >
                  {{ keyword }}
                </span>
              </div>
            </div>
            <div>
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
                >Comments:</span
              >
              <ul class="list-disc list-inside mt-1 space-y-1">
                <li
                  v-for="(comment, index) in topic.comments"
                  :key="index"
                  class="text-sm text-gray-600 dark:text-gray-400"
                >
                  {{ comment }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Ref } from 'vue'
import { inject, onMounted, ref, watch } from 'vue'
import VueDraggable from 'vuedraggable'
import { api } from '../api'
import dayjs from 'dayjs'

interface Comment {
  name: string
  uid: string
  face: string
  comment: string
  heat: string
  from_id: string
  from_name: string
  time: string
  source: string
}

interface Topic {
  topic: string
  rank: number
  heat: number
  keywords: string[]
  comments: string[]
}

const selectedVTuber = inject('selectedVTuber') as Ref<string>
const isDarkMode = inject('isDarkMode') as Ref<boolean>
const topicsLoading = ref(true)
const error = ref('')
const comments = ref<Comment[]>([])
const wordCloudData = ref('')
const topN = ref(20)
const isDraggable = ref(false)
const topics = ref<Topic[]>([])
const selectedTimeRange = ref(30)

const startDate = ref(dayjs().subtract(30, 'day').format('YYYY-MM-DD'))
const endDate = ref(dayjs().format('YYYY-MM-DD'))

const getHeatSymbols = (heat: number) => {
  heat = Math.min(heat, 5)
  return '🔥'.repeat(heat)
}

const isDataLoading = ref<boolean>(false)
const isWordCloudLoading = ref<boolean>(false)

const fetchData = async () => {
  isDataLoading.value = true
  try {
    error.value = ''
    const commentsData = await api.getTopNComments(
      selectedVTuber.value,
      startDate.value,
      endDate.value,
      Number(topN.value)
    )

    comments.value = commentsData
  } catch (err) {
    error.value = '服务器开小差了😴，请稍后再试'
    console.error('Error fetching data:', err)
  } finally {
    isDataLoading.value = false
  }
}

const fetchWordCloud = async () => {
  isWordCloudLoading.value = true
  try {
    error.value = ''
    const wordCloudResponse = await api.getCommentWordCloud(
      selectedVTuber.value,
      startDate.value,
      endDate.value
    )
    console.log(wordCloudResponse)
    wordCloudData.value = wordCloudResponse
  } catch (err) {
    error.value = '服务器开小差了😴，请稍后再试'
    console.error('Error fetching data:', err)
  } finally {
    isWordCloudLoading.value = false
  }
}

const handleClick = () => {
  fetchData()
  fetchWordCloud()
}

const fetchTopics = async () => {
  try {
    topicsLoading.value = true
    const topicsData = await api.getCommentTopics(
      selectedVTuber.value,
      selectedTimeRange.value
    )
    topics.value = topicsData
  } catch (err) {
    console.error('Error fetching topics:', err)
  } finally {
    topicsLoading.value = false
  }
}

onMounted(async () => {
  await fetchData()
  await fetchTopics()
  await fetchWordCloud()
})

watch(selectedVTuber, async () => {
  await fetchData()
  await fetchTopics()
  await fetchWordCloud()
})

watch(selectedTimeRange, fetchTopics)

const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleString()
}
</script>

<style scoped>
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: '';
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #2196f3;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}
</style>
