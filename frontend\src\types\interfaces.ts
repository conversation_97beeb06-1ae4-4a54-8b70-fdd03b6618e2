// --------------  For personalspace ---------------------

export interface RecentInfo {
  dynamic_content: [string, string][]
  video_content: [string, string, string, string][]
  live_content: string
  rise_videos: [string, string, string, string, string][]
}

export interface FanMedal {
  uid: string
  uname: string
  face: string
  rank: number
  level: number
}

export interface SensimentInfo {
  time: string
  sentiment: number
  comment: string[]
}

export interface SensimentData {
  love_ratio: number
  positive_ratio: number
  neutral_ratio: number
  critical_ratio: number
  negative_ratio: number
  info: SensimentInfo[]
}

export interface topLikeCommentor {
  name: string
  uid: string
  likeNum: number
  face: string
}

export interface topAppealCommentor {
  name: string
  uid: string
  appealNum: number
  face: string
}

export interface topReplyCommentor {
  name: string
  uid: string
  rcountSum: number
  face: string
}

export interface LivestreamData {
  date: string
  title: string
  playCount: number
  commentCount: number
}

// --------------  For Creator Info ---------------------
export interface CreatorOverviewStat {
  inc_coin: number          // 新增投币数
  inc_elec: number          // 新增充电数
  inc_fav: number           // 新增收藏数
  inc_like: number          // 新增点赞数
  inc_share: number         // 新增分享数
  incr_click: number        // 新增播放数
  incr_dm: number           // 新增弹幕数
  incr_fans: number         // 新增粉丝数
  incr_reply: number        // 新增评论数
  total_click: number       // 总计播放数
  total_coin: number        // 总计投币数
  total_dm: number          // 总计弹幕数
  total_elec: number        // 总计充电数
  total_fans: number        // 总计粉丝数
  total_fav: number         // 总计收藏数
  total_like: number        // 总计点赞数
  total_reply: number       // 总计评论数
  total_share: number       // 总计分享数
}

export interface CreatorAttentionAnalyze {
  [key: string]: any
}

export interface CreatorArchiveAnalyze {
  [key: string]: any
}

export interface CreatorVideoOverview {
  [key: string]: any
}

export interface CreatorFanGraph {
  [key: string]: any
}

export interface CreatorFanOverview {
  [key: string]: any
}

export interface CreatorVideoCompare {
  [key: string]: any
}

export interface CreatorVideoPandect {
  [key: string]: any
}

export interface CreatorVideoSurvey {
  [key: string]: any
}

export interface CreatorVideoSource {
  [key: string]: any
}

export interface CreatorVideoViewData {
  [key: string]: any
}

export interface ApiResponse<T> {
  data: T
  total?: number
}

export interface DateRangeParams {
  uid: string
  start_date: string
  end_date: string
  limit?: number
  offset?: number
}

export interface DateParams {
  uid: string
  date: string
  limit?: number
  offset?: number
}
