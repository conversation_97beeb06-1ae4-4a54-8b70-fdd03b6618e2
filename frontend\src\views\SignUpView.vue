<template>
  <div class="flex h-screen">
    <!-- Left Panel: Sign Up Form -->
    <div class="w-full lg:w-1/3 flex flex-col justify-center items-center bg-sky-100 p-8 sm:p-12">
      <div class="w-full max-w-md">
        <div class="text-left mb-10">
          <h1 class="text-6xl font-bold text-gray-800">注册</h1>
        </div>

        <form @submit.prevent="handleSignUp">
          <div class="mb-4">
            <label for="email" class="block text-lg font-medium text-gray-700 text-left ml-1">邮箱</label>
            <input
              type="email"
              id="email"
              v-model="email"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="<EMAIL>"
            />
          </div>

          <div class="mb-4">
            <label for="password" class="block text-lg font-medium text-gray-700 text-left ml-1">密码</label>
            <div class="relative">
              <input
                :type="showPassword ? 'text' : 'password'"
                id="password"
                v-model="password"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="••••••••"
              />
              <button
                type="button"
                @click="togglePasswordVisibility"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
              >
                <svg v-if="!showPassword" class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>
                <svg v-else class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7 1.274 4.057 5.064 7 9.542 7 .847 0 1.673.126 2.468.368M18.438 10.092A8.917 8.917 0 0121.542 12c-1.274 4.057-5.064 7-9.542 7a9.01 9.01 0 01-1.312-.11M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3l18 18"></path></svg>
              </button>
            </div>
          </div>

          <div class="mb-4">
            <label for="confirmPassword" class="block text-lg font-medium text-gray-700 text-left ml-1">确认密码</label>
            <input
              type="password"
              id="confirmPassword"
              v-model="confirmPassword"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="••••••••"
            />
          </div>

          <div class="mb-4">
            <label for="verificationCode" class="block text-lg font-medium text-gray-700 text-left ml-1">验证码</label>
            <div class="flex items-center mt-1">
              <input
                type="text"
                id="verificationCode"
                v-model="verificationCode"
                required
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="输入验证码"
              />
              <button
                type="button"
                @click="getVerificationCode"
                :disabled="isCodeButtonDisabled"
                class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {{ codeButtonText }}
              </button>
            </div>
          </div>

          <button
            type="submit"
            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mt-6"
          >
            注册
          </button>
        </form>

        <p class="mt-8 text-center text-sm text-gray-600">
          已有账号?
          <router-link to="/login" class="font-medium text-blue-600 hover:text-blue-500">登录</router-link>
        </p>

        <!-- add a pic for pics/icon.png -->
        <div class="mt-10 flex justify-center mb-6">
            <img
              src="/pics/icon.png"
              alt="App Icon"
              class="h-24 w-24 object-contain transition-all duration-300 hover:scale-110 hover:rotate-6"
            >
        </div>

        <p class="text-center text-xs text-gray-500">
          &copy; {{ new Date().getFullYear() }} CDD Tech. All rights reserved.
        </p>
      </div>
    </div>

    <!-- Right Panel: Background Image Wall -->
    <div class="hidden lg:block lg:w-4/5 bg-cover bg-center relative overflow-hidden" style="background-image: url('/pics/bg_v1_43.jpeg');">
      <!-- Image with animation -->
      <div class="absolute inset-0 bg-cover bg-center animate-zoom-in-out" style="background-image: url('/pics/bg_v1_43.jpeg');"></div>
      <!-- Overlay to make the image less prominent -->
      <div class="absolute inset-0 bg-black opacity-30"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '../api';

const email = ref('');
const password = ref('');
const confirmPassword = ref('');
const verificationCode = ref('');
const showPassword = ref(false);
const router = useRouter();

const countdown = ref(0);
const codeButtonText = computed(() => {
  return countdown.value > 0 ? `${countdown.value}s` : '获取验证码';
});
const isCodeButtonDisabled = computed(() => countdown.value > 0);

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

const getVerificationCode = async () => { // Make function async
  if (!email.value) {
    alert('请先输入您的邮箱地址。');
    return;
  }
  try {
    // Call the sendVerificationCode APIa
    const response = await api.sendVerificationCode({ // Capture response
      email: email.value,
      type: 'register', // Specify type for registration
    });
    console.log('Verification code response:', response); // Log response
    // Handle successful code sending
    alert(response.message || `验证码已发送至 ${email.value}。请检查您的邮箱。`); // Use response.message

    // Start countdown
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  } catch (error: any) {
    console.error('Error sending verification code:', error);
    if (error.response && error.response.data && error.response.data.detail) {
      alert(`发送验证码失败: ${error.response.data.detail}`);
    } else if (error.message) { // Add check for error.message
      alert(`发送验证码失败: ${error.message}`);
    }
    else {
      alert('发送验证码失败。请稍后重试。');
    }
  }
};

const handleSignUp = async () => { // Make function async
  if (password.value !== confirmPassword.value) {
    alert('两次输入的密码不一致。');
    return;
  }
  // Basic validation
  if (email.value && password.value && verificationCode.value) {
    try {
      // Call the registerUser API
      const response = await api.registerUser({
        email: email.value,
        password: password.value,
        code: verificationCode.value, // Pass verification code to backend
      });
      console.log('Sign up response:', response);
      // Handle successful registration
      alert(response.message || '注册成功！请登录。'); // Use response.message
      router.push('/login'); // Redirect to login page
    } catch (error: any) {
      // Handle sign up error
      console.error('Sign up error:', error);
      if (error.response && error.response.data && error.response.data.detail) {
        alert(`注册失败: ${error.response.data.detail}`);
      } else if (error.message) { // Add check for error.message
        alert(`注册失败: ${error.message}`);
      }
      else {
        alert('注册失败。请检查您填写的信息并重试。');
      }
    }
  } else {
    alert('请填写所有必填项。');
  }
};
</script>

<style scoped>
/* Add any additional styles if Tailwind is not sufficient or for very specific tweaks */
@keyframes zoom-in-out {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05); /* Slightly zoom in */
  }
}

.animate-zoom-in-out {
  animation: zoom-in-out 10s infinite ease-in-out; /* 10s duration, infinite loop, ease-in-out timing */
}
</style>
