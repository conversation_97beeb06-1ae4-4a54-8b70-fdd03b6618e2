<template>
  <div
    class="container mx-auto p-6 bg-gradient-to-b from-blue-50 to-white min-h-screen"
  >
    <h1
      class="text-3xl font-bold mb-8 text-center text-gray-800 animate-fade-in"
    >
      <div class="flex items-center justify-center">
        🎢 单人舰长流向分析
        <div class="relative group ml-2">
            <svg
              class="w-4 h-4 text-gray-500 cursor-pointer"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M18 10c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm-8-6a6 6 0 100 12A6 6 0 0010 4zm0 9a1 1 0 110-2 1 1 0 010 2zm0-4a1 1 0 01-1-1V7a1 1 0 112 0v1a1 1 0 01-1 1z"
                clip-rule="evenodd"
              ></path>
            </svg>
            <div
              class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap"
            >
              当前数据积累较少，舰长重叠程度不高,在某些日期下会出现无数据展示的情况
            </div>
        </div>
      </div>
    </h1>

    <div
      class="mb-8 bg-white p-6 rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl"
    >
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div>
          <label
            for="target"
            class="block text-sm font-medium text-gray-700 mb-2"
            >VUP:</label
          >
          <div class="relative">
            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
              <i class="fas fa-user text-gray-400"></i>
            </span>
            <el-select
              v-model="target"
              filterable
              clearable
              placeholder="请选择或输入VUP"
              class="w-full"
            >
              <el-option
                v-for="v in vtubers"
                :key="v[0]"
                :label="v[0]"
                :value="v[0]"
              />
            </el-select>
          </div>
        </div>
        <div>
          <label
            for="startTimeHead"
            class="block text-sm font-medium text-gray-700 mb-2"
            >起始时间头:</label
          >
          <div class="relative">
            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
              <i class="fas fa-calendar-alt text-gray-400"></i>
            </span>
            <input
              v-model="startTimeHead"
              id="startTimeHead"
              type="date"
              class="w-full border rounded-md py-2 pl-10 pr-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>
        </div>
        <div>
          <label
            for="startTimeTail"
            class="block text-sm font-medium text-gray-700 mb-2"
            >起始时间尾:</label
          >
          <div class="relative">
            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
              <i class="fas fa-calendar-alt text-gray-400"></i>
            </span>
            <input
              v-model="startTimeTail"
              id="startTimeTail"
              type="date"
              class="w-full border rounded-md py-2 pl-10 pr-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>
        </div>
        <div>
          <label
            for="endTimeHead"
            class="block text-sm font-medium text-gray-700 mb-2"
            >结束时间头:</label
          >
          <div class="relative">
            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
              <i class="fas fa-calendar-alt text-gray-400"></i>
            </span>
            <input
              v-model="endTimeHead"
              id="endTimeHead"
              type="date"
              class="w-full border rounded-md py-2 pl-10 pr-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>
        </div>
        <div>
          <label
            for="endTimeTail"
            class="block text-sm font-medium text-gray-700 mb-2"
            >结束时间尾:</label
          >
          <div class="relative">
            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
              <i class="fas fa-calendar-alt text-gray-400"></i>
            </span>
            <input
              v-model="endTimeTail"
              id="endTimeTail"
              type="date"
              class="w-full border rounded-md py-2 pl-10 pr-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>
        </div>
        <div>
          <label for="n" class="block text-sm font-medium text-gray-700 mb-2"
            >展示的VUP数量:</label
          >
          <div class="relative">
            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
              <i class="fas fa-users text-gray-400"></i>
            </span>
            <input
              v-model.number="n"
              id="n"
              type="number"
              class="w-full border rounded-md py-2 pl-10 pr-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              min="1"
            />
          </div>
        </div>
        <div class="flex items-end">
          <button
            @click="fetchData"
            class="w-full bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition duration-300 flex items-center justify-center font-semibold"
            :disabled="loading"
          >
            <i class="fas fa-chart-bar mr-2"></i>
            {{ loading ? '分析中...' : '分析！' }}
          </button>
        </div>
      </div>
    </div>

    <div
      v-if="error"
      class="mb-6 p-4 bg-red-100 text-red-700 rounded-md shadow animate-fade-in"
    >
      <i class="fas fa-exclamation-circle mr-2"></i>
      {{ error }}
    </div>

    <div
      v-if="loading"
      class="flex justify-center items-center"
    >
      <img src="/loading/004.gif" alt="Loading..." class="h-20 w-20" />
    </div>

    <div
      v-else-if="chartData.nodes.length > 0"
      ref="chartRef"
      class="w-full h-[1000px] mb-8 bg-white rounded-lg shadow-lg p-4 transition-all duration-300 hover:shadow-xl"
    ></div>

    <div
      v-if="inflowUsers.length > 0 || outflowUsers.length > 0"
      class="grid grid-cols-1 lg:grid-cols-2 gap-8"
    >
      <!-- Inflow Users Table -->
      <div
        v-if="inflowUsers.length > 0"
        class="bg-white rounded-lg shadow-lg p-6 transition-all duration-300 hover:shadow-xl"
      >
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold text-blue-800">
            <i class="fas fa-sign-in-alt mr-2"></i>流入舰长 (合计:
            {{ inflowUsers.length }})
          </h2>
          <button
            @click="downloadExcel('inflow')"
            class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 transition duration-300 flex items-center"
          >
            <i class="fas fa-file-excel mr-2"></i>
            Download
          </button>
        </div>
        <div class="overflow-x-auto" @scroll="handleScroll($event, 'inflow')">
          <table class="w-full border-collapse rounded-lg overflow-hidden">
            <thead class="bg-blue-100">
              <tr>
                <th class="border p-2 text-center">头像</th>
                <th class="border p-2 text-center">用户昵称</th>
                <th class="border p-2 text-center">用户UID</th>
                <th class="border p-2 text-center">来自主播</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="[vup, users] in groupedInflowUsers" :key="vup">
                <!-- Group Header -->
                <tr
                  class="bg-blue-50 cursor-pointer hover:bg-blue-100"
                  @click="toggleGroup(vup, 'inflow')"
                >
                  <td colspan="4" class="border p-2">
                    <div class="flex justify-between items-center">
                      <span class="font-semibold pl-4">{{ vup }}</span>
                      <span class="text-sm text-gray-600">
                        {{ users.length }} 位舰长
                        <i
                          :class="
                            expandedInflowGroups.has(vup)
                              ? 'fas fa-chevron-up'
                              : 'fas fa-chevron-down'
                          "
                          class="ml-2"
                        ></i>
                      </span>
                    </div>
                  </td>
                </tr>
                <!-- Group Items -->
                <template v-if="expandedInflowGroups.has(vup)">
                  <tr
                    v-for="user in users"
                    :key="user[0]"
                    class="hover:bg-blue-50 transition-colors"
                  >
                    <td class="border p-2 text-center flex justify-center">
                      <img
                        :src="user[2]"
                        :alt="user[1]"
                        class="w-6 h-6 rounded-full object-cover"
                      />
                    </td>
                    <td class="border p-2 text-center">
                      <a
                        :href="`https://space.bilibili.com/${user[0]}`"
                        target="_blank"
                        class="text-blue-500 hover:underline"
                      >
                        {{ user[1] }}
                      </a>
                    </td>
                    <td class="border p-2 text-center">{{ user[0] }}</td>
                    <td class="border p-2 text-center">{{ user[3] }}</td>
                  </tr>
                </template>
              </template>
            </tbody>
          </table>
        </div>
        <!-- Collapse Button -->
        <button
          v-if="showInflowCollapseButton"
          class="fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-full shadow-lg hover:bg-blue-600 transition duration-300"
          @click="expandedInflowGroups.clear()"
        >
          <i class="fas fa-compress-alt mr-2"></i>
          收起全部
        </button>
      </div>

      <!-- Outflow Users Table -->
      <div
        v-if="outflowUsers.length > 0"
        class="bg-white rounded-lg shadow-lg p-6 transition-all duration-300 hover:shadow-xl"
      >
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold text-red-800">
            <i class="fas fa-sign-out-alt mr-2"></i>流出舰长 (合计:
            {{ outflowUsers.length }})
          </h2>
          <button
            @click="downloadExcel('outflow')"
            class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 transition duration-300 flex items-center"
          >
            <i class="fas fa-file-excel mr-2"></i>
            Download
          </button>
        </div>
        <div class="overflow-x-auto" @scroll="handleScroll($event, 'outflow')">
          <table class="w-full border-collapse rounded-lg overflow-hidden">
            <thead class="bg-red-100">
              <tr>
                <th class="border p-2 text-center">头像</th>
                <th class="border p-2 text-center">用户昵称</th>
                <th class="border p-2 text-center">用户UID</th>
                <th class="border p-2 text-center">流向主播</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="[vup, users] in groupedOutflowUsers" :key="vup">
                <!-- Group Header -->
                <tr
                  class="bg-red-50 cursor-pointer hover:bg-red-100"
                  @click="toggleGroup(vup, 'outflow')"
                >
                  <td colspan="4" class="border p-2">
                    <div class="flex justify-between items-center">
                      <span class="font-semibold pl-4">{{ vup }}</span>
                      <span class="text-sm text-gray-600">
                        {{ users.length }} 位舰长
                        <i
                          :class="
                            expandedOutflowGroups.has(vup)
                              ? 'fas fa-chevron-up'
                              : 'fas fa-chevron-down'
                          "
                          class="ml-2"
                        ></i>
                      </span>
                    </div>
                  </td>
                </tr>
                <!-- Group Items -->
                <template v-if="expandedOutflowGroups.has(vup)" :key="user[0]">
                  <tr
                    v-for="user in users"
                    class="hover:bg-red-50 transition-colors"
                  >
                    <td class="border p-2 text-center flex justify-center">
                      <img
                        :src="user[2]"
                        :alt="user[1]"
                        class="w-6 h-6 rounded-full object-cover"
                      />
                    </td>
                    <td class="border p-2 text-center">
                      <a
                        :href="`https://space.bilibili.com/${user[0]}`"
                        target="_blank"
                        class="text-blue-400 hover:underline"
                      >
                        {{ user[1] }}
                      </a>
                    </td>
                    <td class="border p-2 text-center">{{ user[0] }}</td>
                    <td class="border p-2 text-center">{{ user[3] }}</td>
                  </tr>
                </template>
              </template>
            </tbody>
          </table>
        </div>
        <!-- Collapse Button -->
        <button
          v-if="showOutflowCollapseButton"
          class="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded-full shadow-lg hover:bg-red-600 transition duration-300"
          @click="expandedOutflowGroups.clear()"
        >
          <i class="fas fa-compress-alt mr-2"></i>
          收起全部
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import type { EChartsOption } from 'echarts';
import * as echarts from 'echarts';
import { computed, inject, nextTick, onMounted, reactive, ref, watch } from 'vue'; // 导入 inject
import * as XLSX from 'xlsx';
import { api } from '../api';

interface Data {
  in: { [key: string]: [number, string, string][] }
  out: { [key: string]: [number, string, string][] }
}

interface Node {
  id: string
  name: string
  category: string
  symbolSize: number
  value?: number
  itemStyle?: {
    color: string
  }
  label?: {
    show: boolean
    formatter: string
  }
  symbol?: string
}

interface Link {
  source: string
  target: string
}

interface ChartData {
  nodes: Node[]
  links: Link[]
}

const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null

const vtubers = inject<string[]>('vtubers', [])
const target = ref('星瞳')
const startTimeHead = ref(dayjs().subtract(20, 'day').format('YYYY-MM-DD')) // 二十天前
const startTimeTail = ref(dayjs().subtract(11, 'day').format('YYYY-MM-DD')) // 十一天前
const endTimeHead = ref(dayjs().subtract(10, 'day').format('YYYY-MM-DD')) // 十天前
const endTimeTail = ref(dayjs().format('YYYY-MM-DD')) // 当前日期
const n = ref(30)

const inflowUsers = ref<[number, string, string, string][]>([])
const outflowUsers = ref<[number, string, string, string][]>([])
const loading = ref(false)
const error = ref('')

const groupedInflowUsers = computed(() => {
  const groups = new Map<string, [number, string, string, string][]>()
  inflowUsers.value.forEach(user => {
    const vup = user[3]
    if (!groups.has(vup)) {
      groups.set(vup, [])
    }
    groups.get(vup)?.push(user)
  })
  return groups
})

const groupedOutflowUsers = computed(() => {
  const groups = new Map<string, [number, string, string, string][]>()
  outflowUsers.value.forEach(user => {
    const vup = user[3]
    if (!groups.has(vup)) {
      groups.set(vup, [])
    }
    groups.get(vup)?.push(user)
  })
  return groups
})

// Track expanded groups
const expandedInflowGroups = ref(new Set<string>())
const expandedOutflowGroups = ref(new Set<string>())

// Track scroll position for showing collapse buttons
const showInflowCollapseButton = ref(false)
const showOutflowCollapseButton = ref(false)

// Auto-expand groups with less than 10 items
const autoExpandGroups = (
  groupedUsers: Map<string, any[]>,
  expandedGroups: Set<string>
) => {
  // groupedUsers.forEach((users, vup) => {
  //   if (users.length < 10) {
  //     expandedGroups.add(vup)
  //   }
  // })
  if (groupedUsers.size === 1) {
    const entry = groupedUsers.entries().next().value
    if (entry) {
      const [vup, users] = entry
      if (users.length < 10) {
        expandedGroups.add(vup)
      }
    }
  }
}

// Handle scroll events
const handleScroll = (event: Event, type: 'inflow' | 'outflow') => {
  const target = event.target as HTMLElement
  const scrollTop = target.scrollTop
  const threshold = 100 // Show after scrolling 100px

  if (type === 'inflow') {
    showInflowCollapseButton.value = scrollTop > threshold
  } else {
    showOutflowCollapseButton.value = scrollTop > threshold
  }
}

// Toggle group expansion
const toggleGroup = (vup: string, type: 'inflow' | 'outflow') => {
  const expandedGroups =
    type === 'inflow' ? expandedInflowGroups : expandedOutflowGroups
  if (expandedGroups.value.has(vup)) {
    expandedGroups.value.delete(vup)
  } else {
    expandedGroups.value.add(vup)
  }
}

const chartData = reactive<ChartData>({
  nodes: [],
  links: [],
})

onMounted(() => {
  if (chartRef.value) {
    chart = echarts.init(chartRef.value)
    window.addEventListener('resize', () => chart?.resize())
  }
  // fetchData()
})

watch(
  chartData,
  () => {
    nextTick(() => {
      updateChart()
    })
  },
  { deep: true }
)

watch(
  [groupedInflowUsers, groupedOutflowUsers],
  () => {
    autoExpandGroups(groupedInflowUsers.value, expandedInflowGroups.value)
    autoExpandGroups(groupedOutflowUsers.value, expandedOutflowGroups.value)
  },
  { immediate: true }
)

const fetchData = async () => {
  loading.value = true
  error.value = ''
  try {
    const response = await api.getTargetPeriodFlowDahanghaiList(
      target.value, // 目标 VUP
      startTimeHead.value, // 起始时间头
      startTimeTail.value, // 起始时间尾
      endTimeHead.value, // 结束时间头
      endTimeTail.value, // 结束时间尾
      n.value // 展示的 VUP 数量
    )
    console.log('API Response:', response)
    processData(response)
  } catch (err) {
    console.error('Error fetching data:', err)
    error.value = '服务器开小差了😭，请联系管理员halyu'
  } finally {
    loading.value = false
  }
}

const processData = (data: Data) => {
  const nodesMap = new Map<string, Node>()
  chartData.links = []
  inflowUsers.value = []
  outflowUsers.value = []

  // 计算总流入和总流出
  const totalInflow = Object.values(data.in).reduce(
    (sum, members) => sum + members.length,
    0
  )
  const totalOutflow = Object.values(data.out).reduce(
    (sum, members) => sum + members.length,
    0
  )

  // 添加目标节点
  const targetNode: Node = {
    id: `target_${target.value}`,
    name: target.value,
    category: '目标主播',
    symbolSize: 60,
    itemStyle: {
      color: '#f67280',
    },
    label: {
      show: true,
      formatter: `${target.value}\n(+${totalInflow}-${totalOutflow})`,
    },
  }
  nodesMap.set(targetNode.id, targetNode)

  // 处理流入/流出数据
  const processGroup = (
    groups: { [key: string]: [number, string, string][] }, // 更改参数类型以匹配新的数据结构
    category: string,
    color: string
  ) => {
    Object.entries(groups).forEach(([groupName, members]) => {
      const groupNodeId = `${category}_${groupName}`
      if (!nodesMap.has(groupNodeId)) {
        const groupNode: Node = {
          id: groupNodeId,
          name: groupName,
          category: category,
          symbolSize: 50,
          itemStyle: {
            color: color,
          },
          label: {
            show: true,
            formatter: `${groupName}\n(${members.length})`,
          },
        }
        nodesMap.set(groupNodeId, groupNode)
      }

      // 创建目标节点与组节点之间的链接
      chartData.links.push({
        source: category === '流入' ? groupNodeId : targetNode.id,
        target: category === '流入' ? targetNode.id : groupNodeId,
      })

      members.forEach(member => {
        const [id, name, image] = member
        const nodeId = `${category}_${id}`
        if (!nodesMap.has(nodeId)) {
          const node: Node = {
            id: nodeId,
            name: name,
            category: category,
            symbolSize: 40,
            value: id,
            itemStyle: {
              color: color,
            },
            label: {
              show: true,
              formatter: name,
            },
            symbol: `image://${image}`,
          }
          nodesMap.set(nodeId, node)
        }

        // 创建组节点与成员节点之间的链接
        chartData.links.push({
          source: category === '流入' ? nodeId : groupNodeId,
          target: category === '流入' ? groupNodeId : nodeId,
        })

        if (category === '流入') {
          inflowUsers.value.push([id, name, image, groupName])
        } else {
          outflowUsers.value.push([id, name, image, groupName])
        }
      })
    })
  }

  processGroup(data.in, '流入', '#dcedc2') // 传入 data.in 对象
  processGroup(data.out, '流出', '#ffd3b5') // 传入 data.out 对象

  chartData.nodes = Array.from(nodesMap.values())
  console.log('Processed Chart Data:', chartData)
}

const updateChart = () => {
  if (!chartRef.value) {
    console.warn('Chart container has zero width or height1')
    return
  }

  const containerWidth = chartRef.value.clientWidth
  const containerHeight = chartRef.value.clientHeight

  if (containerWidth === 0 || containerHeight === 0) {
    console.warn('Chart container has zero width or height')
    return
  }

  if (!chart) {
    chart = echarts.init(chartRef.value)
  } else {
    chart.dispose()
    chart = echarts.init(chartRef.value)
  }
  // chart.hideLoading()
  const option: EChartsOption = {
    title: {
      text: '舰长流向分析',
      top: 'top',
      left: 'left',
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        if (params.dataType === 'node') {
          return `Name: ${params.data.name}<br/>Category: ${
            params.data.category
          }<br/>Value: ${params.data.value || 'N/A'}`
        }
        return ''
      },
    },
    legend: [
      {
        data: ['目标主播', '流入', '流出'],
        top: 'bottom',
        left: 'left',
      },
    ],
    animationDurationUpdate: 1500,
    animationEasingUpdate: 'quinticInOut',
    series: [
      {
        name: '舰长流向图',
        type: 'graph',
        layout: 'force',
        data: chartData.nodes,
        links: chartData.links,
        categories: [{ name: '目标主播' }, { name: '流入' }, { name: '流出' }],
        roam: true,
        label: {
          show: true,
          position: 'right',
          formatter: '{b}',
        },
        labelLayout: {
          hideOverlap: true,
        },
        scaleLimit: {
          min: 0.4,
          max: 2,
        },
        lineStyle: {
          color: 'source',
          curveness: 0.3,
          width: 2,
        },
        emphasis: {
          focus: 'adjacency',
          lineStyle: {
            width: 10,
          },
        },
        force: {
          repulsion: 200,
          edgeLength: 100,
          gravity: 0.1,
        },
        draggable: true,
        edgeSymbol: ['none', 'triangle'],
        edgeSymbolSize: [0, 8],
      },
    ],
  }

  chart.setOption(option)
}

const downloadExcel = (type: 'inflow' | 'outflow') => {
  const data = type === 'inflow' ? inflowUsers.value : outflowUsers.value
  const worksheet = XLSX.utils.json_to_sheet(
    data.map(user => ({
      用户UID: user[0],
      用户昵称: user[1],
      // 个人主页：'https://space.bilibili.com/' + user[0],
      [type === 'inflow' ? '来自主播' : '流向主播']: user[3],
    }))
  )
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, `${type} Users`)
  XLSX.writeFile(workbook, `${type}_users.xlsx`)
}
</script>

<style scoped>
.overflow-x-auto {
  max-height: 600px;
  overflow-y: auto;
}
</style>
