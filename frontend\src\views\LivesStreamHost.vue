<template>
  <div class="lives-stream-host">
    <div v-if="error" class="error">{{ error }}</div>
    <div v-else class="content grid grid-cols-1 lg:grid-cols-4 gap-4">
      <!-- Left Column (1/4) -->
      <div class="lg:col-span-1 space-y-4">
        <!-- Live Status Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
          <h2 class="text-xl font-semibold mb-2">直播状态</h2>
          <div class="text-center">
            <p
              v-if="liveStatus.if_live"
              class="text-lg font-semibold text-green-500 mb-2"
            >
              -- 🔴 正在营业中! --
            </p>
            <p
              v-else
              class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2"
            >
              -- ⚪ 下班状态中 --
            </p>
            <p class="text-sm mb-2">
              {{ liveStatus.live_title || 'No stream title available' }}
            </p>
            <img
              :src="liveStatus.live_cover"
              alt="Stream Cover"
              class="w-full h-auto mt-2 rounded"
              @error="handleImageError"
            />
            <p class="text-sm mt-2">
              直播间号: {{ liveStatus.live_roomid || 'N/A' }}
            </p>
            <a
              :href="liveStatus.live_url"
              target="_blank"
              class="text-blue-500 hover:underline mt-2 inline-block"
            >
              {{ liveStatus.if_live ? '现在去看！' : '还没开播，去小窝看看吧' }}
            </a>
          </div>
        </div>

        <!-- Data Overview Card -->
        <div
          class="overview-card bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-4 transform transition-all duration-300 hover:shadow-lg"
        >
          <h2
            class="text-2xl font-bold mb-6 text-center flex items-center justify-center text-gray-800 dark:text-gray-200"
          >
            <span
              class="stats-icon mr-3 transform transition-all duration-300 hover:rotate-12"
              >📊</span
            >
            数据总览
          </h2>
          <div
            v-if="channelData"
            class="grid grid-cols-1 md:grid-cols-1 gap-6"
          >
            <!-- Last Stream Stats -->
            <div
              class="stats-card bg-gradient-to-br from-sky-50 to-indigo-100 dark:from-gray-700 dark:to-gray-600 p-6 rounded-xl transform hover:scale-102 transition-all duration-300"
            >
              <h3
                class="font-semibold text-lg mb-4 text-sky-800 dark:text-sky-200 flex items-center"
              >
                <span class="recent-icon mr-2 animate-pulse">🎯</span>
                上次直播数据 ({{ channelData.lastLiveDate }})
              </h3>
              <div class="space-y-4">
                <div
                  class="stat-item flex justify-between items-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/40 backdrop-blur-sm transform transition-all duration-200 hover:translate-x-1"
                >
                  <span
                    class="text-gray-700 dark:text-gray-300 flex items-center font-medium"
                  >
                    <span class="stat-icon mr-2">💬</span>
                    弹幕数量
                  </span>
                  <span class="font-bold text-sky-600 dark:text-sky-300">
                    {{
                      formatNumber(channelData.lastLiveDanmakuCount || 0)
                    }}
                  </span>
                </div>
                <div
                  class="stat-item flex justify-between items-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/40 backdrop-blur-sm transform transition-all duration-200 hover:translate-x-1"
                >
                  <span
                    class="text-gray-700 dark:text-gray-300 flex items-center font-medium"
                  >
                    <span class="stat-icon mr-2">💴</span>
                    营收金额
                  </span>
                  <span class="font-bold text-sky-600 dark:text-sky-300">
                    {{
                      formatNumber(channelData.lastLiveIncome || 0)
                    }}
                  </span>
                </div>
                <div
                  class="stat-item flex justify-between items-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/40 backdrop-blur-sm transform transition-all duration-200 hover:translate-x-1"
                >
                  <span
                    class="text-gray-700 dark:text-gray-300 flex items-center font-medium"
                  >
                    <span class="stat-icon mr-2">🫣</span>
                    观看人数
                  </span>
                  <span class="font-bold text-sky-600 dark:text-sky-300">
                    {{
                      formatNumber(channelData.lastWatchCount || 0)
                    }}
                  </span>
                </div>
                <div
                  class="stat-item flex justify-between items-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/40 backdrop-blur-sm transform transition-all duration-200 hover:translate-x-1"
                >
                  <span
                    class="text-gray-700 dark:text-gray-300 flex items-center font-medium"
                  >
                    <span class="stat-icon mr-2">🐾</span>
                    进房人数
                  </span>
                  <span class="font-bold text-sky-600 dark:text-sky-300">
                    {{
                      formatNumber(channelData.lastEnterRoomCount || 0)
                    }}
                  </span>
                </div>
                <div
                  class="stat-item flex justify-between items-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/40 backdrop-blur-sm transform transition-all duration-200 hover:translate-x-1"
                >
                  <span
                    class="text-gray-700 dark:text-gray-300 flex items-center font-medium"
                  >
                    <span class="stat-icon mr-2">⭐</span>
                    平均同接
                  </span>
                  <span class="font-bold text-sky-600 dark:text-sky-300">
                    {{
                      formatNumber(channelData.lastAveOnlineRankCount || 0)
                    }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Total Stats -->
            <div
              class="stats-card bg-gradient-to-br from-rose-50 to-purple-100 dark:from-gray-700 dark:to-gray-600 p-6 rounded-xl transform hover:scale-102 transition-all duration-300"
            >
              <h3
                class="font-semibold text-lg mb-4 text-purple-800 dark:text-purple-200 flex items-center"
              >
                <span class="total-icon mr-2">📈</span>
                总体数据
              </h3>
              <div class="space-y-3">
                <div
                  class="stat-item flex justify-between items-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/40 backdrop-blur-sm transform transition-all duration-200 hover:translate-x-1"
                >
                  <span
                    class="text-gray-700 dark:text-gray-300 flex items-center font-medium"
                  >
                    <span class="stat-icon mr-2">🎬</span>
                    直播次数
                  </span>
                  <span class="font-bold text-purple-600 dark:text-purple-300">
                    {{ formatNumber(channelData.totalLiveCount || 0) }}
                  </span>
                </div>
                <div
                  class="stat-item flex justify-between items-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/40 backdrop-blur-sm transform transition-all duration-200 hover:translate-x-1"
                >
                  <span
                    class="text-gray-700 dark:text-gray-300 flex items-center font-medium"
                  >
                    <span class="stat-icon mr-2">⏱️</span>
                    总直播时长
                  </span>
                  <span class="font-bold text-purple-600 dark:text-purple-300">
                    {{
                      formatDuration(channelData.totalLiveSecond || 0)
                    }}
                  </span>
                </div>
                <div
                  class="stat-item flex justify-between items-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/40 backdrop-blur-sm transform transition-all duration-200 hover:translate-x-1"
                >
                  <span
                    class="text-gray-700 dark:text-gray-300 flex items-center font-medium"
                  >
                    <span class="stat-icon mr-2">💭</span>
                    总弹幕数
                  </span>
                  <span class="font-bold text-purple-600 dark:text-purple-300">
                    {{
                      formatNumber(channelData.totalDanmakuCount || 0)
                    }}
                  </span>
                </div>
                <div
                  class="stat-item flex justify-between items-center p-3 rounded-lg bg-white/60 dark:bg-gray-800/40 backdrop-blur-sm transform transition-all duration-200 hover:translate-x-1"
                >
                  <span
                    class="text-gray-700 dark:text-gray-300 flex items-center font-medium"
                  >
                    <span class="stat-icon mr-2">💎</span>
                    总营收额
                  </span>
                  <span class="font-bold text-amber-600 dark:text-amber-300">
                    ¥{{ formatNumber(channelData.totalIncome || 0) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div
            v-else
            class="flex flex-col items-center justify-center py-8 animate-pulse"
          >
            <div class="loading-icon text-4xl mb-4">⌛</div>
            <div class="text-gray-600 dark:text-gray-400 font-medium">
              加载数据中...
            </div>
          </div>
        </div>
      </div>

      <!-- Middle Column (2/4) -->
      <div class="lg:col-span-2">
        <!-- Stream History -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
          <h2 class="text-xl font-semibold mb-4 flex items-center">
            历史直播记录
            <div class="relative group ml-2">
              <svg
                class="w-4 h-4 text-gray-500 cursor-pointer"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  d="M18 10c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm-8-6a6 6 0 100 12A6 6 0 0010 4zm0 9a1 1 0 110-2 1 1 0 010 2zm0-4a1 1 0 01-1-1V7a1 1 0 112 0v1a1 1 0 01-1 1z"
                  clip-rule="evenodd"
                ></path>
              </svg>
              <div
                class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap"
              >
                点击卡片查看详细分时数据
              </div>
            </div>
          </h2>
          <div class="space-y-4">
            <div
              v-for="session in currentPageSessions"
              :key="session.liveId"
              class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200 cursor-pointer"
              :class="{'ring-2 ring-blue-500': selectedSessionId === session.liveId}"
              @click="handleSessionClick(session)"
            >
              <div class="flex flex-col md:flex-row gap-4">
                <div class="w-full md:w-1/3">
                  <img
                    :src="session.coverUrl"
                    :alt="session.title"
                    class="w-full h-40 object-cover rounded-lg"
                    @error="handleImageError"
                  />
                </div>
                <div class="w-full md:w-2/3">
                  <h3 class="text-lg font-semibold mb-2">{{ session.title }}</h3>
                  <div class="grid grid-cols-2 gap-2 text-sm pl-4 py-2">
                    <div class="flex items-center gap-1">
                      <span class="text-gray-600 dark:text-gray-400"
                        >开始时间:</span
                      >
                      <span>{{ formatStringDate(session.startDate) }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <span class="text-gray-600 dark:text-gray-400"
                        >时长:</span
                      >
                      <span>{{
                        calculateDurationFromString(session.startDate, session.stopDate)
                      }}</span>
                    </div>
                     <div class="flex items-center gap-1">
                      <span class="text-gray-600 dark:text-gray-400"
                        >分区:</span
                      >
                      <span>{{ session.parentArea }} / {{ session.area }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <span class="text-gray-600 dark:text-gray-400"
                        >弹幕数:</span
                      >
                      <span>{{ formatNumber(session.danmakusCount) }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <span class="text-gray-600 dark:text-gray-400"
                        >观看人数:</span
                      >
                      <span>{{ formatNumber(session.watchCount) }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <span class="text-gray-600 dark:text-gray-400"
                        >互动数:</span
                      >
                      <span>{{ formatNumber(session.interactionCount) }}</span>
                    </div>
                     <div class="flex items-center gap-1">
                      <span class="text-gray-600 dark:text-gray-400"
                        >点赞数:</span
                      >
                      <span>{{ formatNumber(session.likeCount) }}</span>
                    </div>
                     <div class="flex items-center gap-1">
                      <span class="text-gray-600 dark:text-gray-400"
                        >付费人数:</span
                      >
                      <span>{{ formatNumber(session.payCount) }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <span class="text-gray-600 dark:text-gray-400"
                        >高能榜峰值:</span
                      >
                      <span>{{ formatNumber(session.onlineRank) }}</span>
                    </div>
                    <div class="flex items-center gap-1">
                      <span class="text-gray-600 dark:text-gray-400"
                        >营收:</span
                      >
                      <span class="text-yellow-600 dark:text-yellow-400">
                        ¥{{ formatNumber(session.totalIncome) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <div class="mt-4 flex justify-between items-center">
            <div class="text-sm text-gray-600 dark:text-gray-400">
              显示 {{ startIndex + 1 }}-{{ endIndex }} 条，共
              {{ totalSessions }} 条
            </div>
            <div class="flex gap-2">
              <button
                @click="previousPage"
                :disabled="currentPage === 1"
                class="px-3 py-1 rounded bg-blue-500 text-white disabled:bg-gray-300"
              >
                上一页
              </button>
              <button
                @click="nextPage"
                :disabled="currentPage >= totalPages"
                class="px-3 py-1 rounded bg-blue-500 text-white disabled:bg-gray-300"
              >
                下一页
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column (1/4) -->
      <div class="lg:col-span-1">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
          <h3
            class="text-xl font-bold text-center text-gray-800 dark:text-gray-200 mb-6"
          >
            {{ chartDisplayTitle }}
          </h3>
          <div v-if="isDataIncomplete" class="text-center py-8">
            <div class="text-red-500 dark:text-red-400 text-lg font-semibold mb-4">
              ⚠️ 本场服务器断开，直播获取不完整
            </div>
            <div class="text-gray-600 dark:text-gray-400 text-sm">
              该直播场次的分时数据不完整，无法显示详细图表
            </div>
          </div>
          <div v-else-if="!sessionMinuteData || isLoadingDetails" class="text-center text-gray-500">
             <span v-if="isLoadingDetails">加载详细数据中...</span>
             <span v-else>请点击左侧直播记录查看详细数据</span>
          </div>
          <div v-else class="charts-container">
            <div
              v-for="(_, index) in chartTitles"
              :key="index"
              class="chart-wrapper"
            >
              <canvas
                :ref="el => { if (el) chartRefs[index] = el as HTMLCanvasElement }"
              ></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Chart, ChartConfiguration } from 'chart.js/auto';
import type { Ref } from 'vue';
import { computed, inject, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'; // Import nextTick
import { useRoute } from 'vue-router';
import { api } from '../api';

// --- TYPES ---
interface LiveSession {
  liveId: string
  isFinish: boolean
  isFull: boolean
  parentArea: string
  area: string
  coverUrl: string
  danmakusCount: number
  startDate: string
  stopDate: string
  title: string
  totalIncome: number
  watchCount: number
  likeCount: number
  payCount: number
  interactionCount: number
  onlineRank: number
}

interface LiveSessionsResponse {
  lastLiveDate: string
  lastLiveDanmakuCount: number
  lastLiveIncome: number
  lastWatchCount: number
  lastAveEnterRoomCount: number
  lastEnterRoomCount: number
  lastAveOnlineRankCount: number
  totalDanmakuCount: number
  totalIncome: number
  totalLiveCount: number
  totalLiveSecond: number
  sessions: LiveSession[]
}

interface SessionMinuteData {
  liveId: string
  isFinish: boolean
  isFull: boolean
  parentArea: string
  area: string
  coverUrl: string
  danmakusCount: number
  startDate: string
  stopDate: string
  title: string
  totalIncome: number
  watchCount: number
  likeCount: number
  payCount: number
  interactionCount: number
  onlineRank: number
  danmuCountList: number[]
  activeWatcherList: number[]
  onlineRankCountList: number[]
  incomeCountList: number[]
  interactCountList: number[]
  enterCountList: number[]
  timestamp: string[]
}
// --- END TYPES ---

const route = useRoute()
const chartRefs = ref<(HTMLCanvasElement | null)[]>([])
const chartTitles = ref<string[]>([
  '弹幕数',
  '参与互动观众',
  '高能榜人数',
  '营收',
  '互动次数',
  '进入房间观众',
])
const selectedSessionId = ref<string | null>(null) // 用于跟踪当前选中的会话
const charts = ref<(Chart | null)[]>([]) // Allow null values in the array
const liveStatus = ref({
  if_live: false,
  live_url: '',
  live_title: '',
  live_cover: '',
  live_roomid: '',
})
const channelData = ref<LiveSessionsResponse | null>(null)
const sessions = ref<LiveSession[]>([])
const sessionMinuteData = ref<SessionMinuteData | null>(null)
const chartDisplayTitle = ref<string>('直播分时数据')
const isLoadingDetails = ref(false)
const isDataIncomplete = ref(false)

const selectedVTuber = inject('selectedVTuber') as Ref<string>
const error = ref('')

// Pagination
const currentPage = ref(1)
const itemsPerPage = 10
const totalSessions = computed(() => sessions.value.length)
const totalPages = computed(() => Math.ceil(totalSessions.value / itemsPerPage))
const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage)
const endIndex = computed(() =>
  Math.min(startIndex.value + itemsPerPage, totalSessions.value)
)
const currentPageSessions = computed(() =>
  sessions.value.slice(startIndex.value, endIndex.value)
)

function destroyAllCharts() {
  if (Array.isArray(charts.value)) {
      charts.value.forEach((chart, index) => {
          // Check if chart is not null before destroying
          if (chart && typeof chart.destroy === 'function') {
              try {
                  chart.destroy();
                  // 获取对应的 Canvas 元素并清理其 2D 上下文
                  const canvas = chartRefs.value[index];
                  if (canvas) {
                      const ctx = canvas.getContext('2d');
                      if (ctx) {
                          // 清除画布内容
                          ctx.clearRect(0, 0, canvas.width, canvas.height);
                      }
                  }
              } catch (e) {
                  console.error(`Error destroying chart at index ${index}:`, e);
              }
          }
      });
  }
  charts.value = []; // Reset the array completely
  chartRefs.value = []; // Also reset the refs array
}

onBeforeUnmount(() => {
  destroyAllCharts()
})

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

// --- UTILITY FUNCTIONS ---
const formatStringDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return dateString.replace(' ', '\n')
}

const calculateDurationFromString = (startStr: string, endStr: string) => {
  if (!startStr || !endStr) return 'N/A'
  try {
    const start = new Date(startStr.replace(/-/g, '/')).getTime()
    const end = new Date(endStr.replace(/-/g, '/')).getTime()
    if (isNaN(start) || isNaN(end) || end < start) return 'N/A'
    const duration = (end - start) / (1000 * 60 * 60)
    return duration.toFixed(1) + ' 小时'
  } catch (e) {
    console.error("Error calculating duration:", e)
    return 'N/A'
  }
}

const formatNumber = (num: number | null | undefined) => {
  if (num === undefined || num === null) return '0'
  return num.toLocaleString('zh-CN')
}

const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours} 小时 ${minutes} 分钟`
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/error.png'
}

function getRandomColor() {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
}
// --- END UTILITY FUNCTIONS ---


// --- DATA FETCHING LOGIC ---
async function fetchLiveStatusData() {
  try {
    const liveStatusData = await api.getLiveInfo(selectedVTuber.value)
    console.log(liveStatusData)
    liveStatus.value = {
      if_live: liveStatusData[3] == 1,
      live_url: "https://live.bilibili.com/" + liveStatusData[1] ,
      live_title: liveStatusData[5],
      live_cover: liveStatusData[6],
      live_roomid: liveStatusData[1],
    }
  } catch (err) {
    console.error('Error fetching live status data:', err)
  }
}

async function fetchLiveSessions() {
  isLoadingDetails.value = false; // Reset loading state
  isDataIncomplete.value = false; // Reset data incomplete state
  sessionMinuteData.value = null // Clear detailed data
  destroyAllCharts() // Clear charts
  chartDisplayTitle.value = '直播分时数据' // Reset chart title
  error.value = '' // Clear previous errors

  try {
    const response = await api.getLiveSessions(selectedVTuber.value)
    channelData.value = response
    sessions.value = response.sessions || []
    sessions.value.sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
    currentPage.value = 1
  } catch (err) {
    console.error('Error fetching live sessions:', err)
    error.value = '获取直播场次列表失败，请稍后重试。'
    channelData.value = null
    sessions.value = []
  }
}

async function handleSessionClick(session: LiveSession) {
  if (isLoadingDetails.value) {
    console.log('Already loading session details, please wait.')
    return
  }

  // 重置数据不完整状态
  isDataIncomplete.value = false

  // 更新选中的会话ID
  selectedSessionId.value = session.liveId

  if (sessionMinuteData.value?.liveId === session.liveId) {
    console.log('Data for this session is already loaded.');
    return;
  }

  if (!session.isFull) {
    isDataIncomplete.value = true
    sessionMinuteData.value = null
    destroyAllCharts()
    chartDisplayTitle.value = '直播分时数据 (数据不完整)'
    return;
  }

  // 设置加载状态，清除旧数据和图表
  isLoadingDetails.value = true
  error.value = ''
  chartDisplayTitle.value = `加载 ${selectedVTuber.value} 于 ${formatStringDate(session.startDate)} 的直播数据中...`
  destroyAllCharts()
  sessionMinuteData.value = null

  try {
    const response = await api.getLiveOneSessionMinutes(session.liveId, 10)

    if (response && response.timestamp && response.timestamp.length > 0) {
      chartDisplayTitle.value = `${selectedVTuber.value} 于 ${formatStringDate(session.startDate)} 的直播数据`
      sessionMinuteData.value = response
    } else {
      chartDisplayTitle.value = '直播分时数据 (加载成功但无数据)'
      error.value = `成功获取直播 ${session.liveId} 的数据，但内容为空或无效。`
      sessionMinuteData.value = null
    }
  } catch (e) {
    console.error("Error in handleSessionClick during fetch:", e)
    chartDisplayTitle.value = '直播分时数据 (加载出错)'
    error.value = `获取直播 ${session.liveId} 的详细数据失败。`
    sessionMinuteData.value = null
  } finally {
    isLoadingDetails.value = false
  }
}
// --- END DATA FETCHING LOGIC ---


// --- CHART LOGIC ---
// Renamed updateCharts to renderCharts and it now accepts data as argument
async function renderCharts(minuteData: SessionMinuteData): Promise<void> { // <-- Added async and Promise<void>
  if (!minuteData || !minuteData.timestamp || minuteData.timestamp.length === 0) {
    console.log('No valid session minute data available to display charts.')
    return
  }

  try {
    const labels = minuteData.timestamp.map(ts => {
      try {
        return new Date(ts.replace(/-/g, '/')).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      } catch (e) {
        console.error('Error formatting timestamp:', e);
        return 'Invalid time';
      }
    });

    const datasets = [
      minuteData.danmuCountList || [],
      minuteData.activeWatcherList || [],
      minuteData.onlineRankCountList || [],
      minuteData.incomeCountList || [],
      minuteData.interactCountList || [],
      minuteData.enterCountList || [],
    ].map(arr => Array.isArray(arr) ? arr : []);

    if (chartRefs.value.length !== datasets.length) {
      chartRefs.value = Array(datasets.length).fill(null);
    }

    await nextTick();

    if (!sessionMinuteData.value || sessionMinuteData.value.liveId !== minuteData.liveId) {
      console.log('Session data changed before chart rendering could complete, aborting.');
      return;
    }

    datasets.forEach((data, idx) => {
      if (!sessionMinuteData.value || sessionMinuteData.value.liveId !== minuteData.liveId) {
          console.log(`Chart rendering for index ${idx} aborted due to data change.`);
          return;
      }

      const canvas = chartRefs.value[idx];
      if (!canvas) {
        console.warn(`Chart canvas element at index ${idx} (${chartTitles.value[idx]}) is not available in the DOM.`);
        return;
      }

        if (data.length > 0) {
          try {
            const config: ChartConfiguration = {
              type: 'line',
              data: {
                labels: labels,
                datasets: [{
                  label: chartTitles.value[idx],
                  data: data,
                  borderColor: getRandomColor(),
                  fill: false,
                  tension: 0.1
                }]
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                  intersect: false,
                  mode: 'index',
                },
                plugins: {
                  legend: {
                    display: false,
                  },
                  tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    displayColors: false,
                    callbacks: {
                      label: function(context) {
                        return `${chartTitles.value[idx]}: ${formatNumber(context.parsed.y)}`;
                      },
                      title: function(context) {
                        const dataIndex = context[0]?.dataIndex;
                        return dataIndex !== undefined && minuteData.timestamp[dataIndex]
                          ? minuteData.timestamp[dataIndex]
                          : '';
                      }
                    }
                  },
                  title: {
                    display: true,
                    text: chartTitles.value[idx],
                    color: document.documentElement.classList.contains('dark') ? '#e5e7eb' : '#374151'
                  }
                },
                scales: {
                  y: {
                    beginAtZero: true,
                    ticks: {
                      color: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280'
                    },
                    grid: {
                      color: document.documentElement.classList.contains('dark') ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
                    }
                  },
                  x: {
                    ticks: {
                      color: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                      maxRotation: 0,
                      autoSkip: true,
                      maxTicksLimit: 10
                    },
                    grid: {
                      display: false
                    }
                  }
                }
              }
            };

            const ctx = canvas.getContext('2d');
            if (ctx) {
              const chart = new Chart(ctx, config);
              charts.value[idx] = chart as any;
            } else {
              console.warn(`Could not get 2D context for chart canvas at index ${idx} (${chartTitles.value[idx]})`);
            }
          } catch (e) {
            console.error(`Error creating chart ${idx} (${chartTitles.value[idx]}):`, e);
          }
        } else {
          console.warn(`Data not available or empty for chart index ${idx} (${chartTitles.value[idx]})`);
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = '16px Arial';
            ctx.fillStyle = document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280';
            ctx.textAlign = 'center';
            ctx.fillText('无数据', canvas.width / 2, canvas.height / 2);
          }
        }
      });

  } catch (e) {
    console.error('Error in renderCharts:', e);
  }
}
// --- END CHART LOGIC ---


// --- LIFECYCLE & WATCHERS ---

watch(
  () => selectedVTuber.value,
  async (newVtuber, oldVtuber) => {
    if (newVtuber && newVtuber !== oldVtuber) {
      console.log('Selected VTuber changed to:', newVtuber)
      await fetchLiveStatusData()
      await fetchLiveSessions()
    }
  },
  { immediate: true }
)

// *** NEW Watcher for sessionMinuteData ***
watch(sessionMinuteData, async (newData, oldData) => {

  if (newData === oldData) return;

  if (newData === oldData && newData !== null) return;
  destroyAllCharts();

  if (newData && newData.timestamp && newData.timestamp.length > 0) {
    console.log('sessionMinuteData changed with valid data, rendering charts for liveId:', newData.liveId);
    await nextTick();
    renderCharts(newData);
  } else {
    console.log('sessionMinuteData is now null or invalid, charts destroyed. Old data liveId:', oldData?.liveId);
  }
}, { deep: false });


onMounted(async () => {
})
// --- END LIFECYCLE & WATCHERS ---

</script>

<style scoped>
/* Styles are copied from LivesStream.vue, minor adjustments might be needed */
.lives-stream-host {
  padding: 20px;
}

.error {
  color: red;
  background-color: #ffebee;
  border: 1px solid red;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr; /* Display charts vertically */
  gap: 20px;
}

.chart-wrapper {
  background-color: var(--dp-secondary-color); /* Use theme color */
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 250px; /* Give charts a fixed height */
  position: relative; /* Needed for chart.js responsiveness */
}

/* Ensure canvas fills the wrapper */
.chart-wrapper canvas {
    /* max-height: 100%; */ /* Let Chart.js manage height based on container */
    /* max-width: 100%; */
    display: block; /* Prevent extra space below canvas */
    width: 100%;   /* Explicitly set width */
    height: 100%;  /* Explicitly set height */
}


.overview-card, .stats-card {
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

/* Add styles for dark mode compatibility if needed */
.dark .chart-wrapper {
   background-color: var(--dp-secondary-color); /* Use theme color */
}
.dark .error {
  color: #f44336;
  background-color: #3e2727;
  border-color: #f44336;
}


@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.transform {
  transition-property: transform;
}

</style>
