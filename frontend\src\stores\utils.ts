const emojis = [
  '😊',
  '😂',
  '😍',
  '🥰',
  '😎',
  '🤩',
  '😇',
  '😜',
  '🤗',
  '😋',
  '🔥',
  '😶‍🌫️',
  '👏',
  '🥳',
  '🙂‍↔️',
  '😎',
]

export const getRandomEmoji = () => {
  const randomIndex = Math.floor(Math.random() * emojis.length)
  return emojis[randomIndex]
}

export const getRecentDays = (days: string) => {
  switch (days) {
    case 'today':
      return 1
    case 'week':
      return 7
    case 'month':
      return 30
    case '3months':
      return 90
    default:
      return 30
  }
}

