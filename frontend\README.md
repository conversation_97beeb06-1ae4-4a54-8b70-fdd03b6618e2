# VupBI Frontend

VupBI 是一个 VTuber（虚拟主播）数据分析平台的前端应用。本项目提供了一个 Web 界面，用于展示和分析 VTuber 的各种数据指标，包括动态分析、视频数据、评论分析、直播数据等。

## 🚀 项目简介

VupBI Frontend 是基于 Vue 3 生态系统构建的单页面应用程序，为用户提供直观、交互式的数据可视化体验。平台支持多个 VTuber 的数据分析，包括：

- **个人空间**：VTuber 个人数据概览
- **动态分析**：社交媒体动态数据统计
- **视频分析**：视频发布和表现数据
- **评论分析**：用户评论情感和趋势分析
- **直播数据**：直播间数据和观众分析
- **对比分析**：多个 VTuber 之间的数据对比
- **用户分析**：观众行为和偏好分析

## 🛠️ 技术栈

### 核心框架
- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Vite** - 下一代前端构建工具

### UI 组件库
- **Element Plus** - 基于 Vue 3 的组件库
- **Tailwind CSS** - 实用优先的 CSS 框架

### 状态管理与路由
- **Vue Router 4** - Vue.js 官方路由管理器
- **Pinia** - Vue 的状态管理库
- **Vuex** - 集中式状态管理

### 数据可视化
- **ECharts** - 强大的数据可视化库
- **Chart.js** - 简单灵活的图表库

### 工具库
- **Axios** - HTTP 客户端
- **Day.js** - 轻量级日期处理库
- **DOMPurify** - DOM 净化库
- **Marked** - Markdown 解析器
- **XLSX** - Excel 文件处理

## 📋 系统要求

- **Node.js** >= 16.0.0
- **npm** >= 8.0.0 或 **yarn** >= 1.22.0

## 🔧 安装和设置

### 1. 克隆项目

```bash
git clone <repository-url>
cd vupbi/frontend
```

### 2. 安装依赖

```bash
npm install
```

或使用 yarn：

```bash
yarn install
```

### 3. 环境配置

项目使用 Vite 的环境变量系统。需要创建 `.env` 文件：

```bash
# API 基础地址
VITE_API_BASE_URL=http://************:9022

# 其他环境变量...
```

## 🚀 运行项目

### 开发环境

启动开发服务器（默认端口 80）：

```bash
npm run dev
```

开发服务器将在 `http://localhost` 启动，支持热重载。

### 生产构建

构建生产版本：

```bash
npm run build
```

构建文件将输出到 `dist/` 目录。

### 预览生产构建

预览生产构建：

```bash
npm run preview
```

## 📁 项目结构

```
frontend/
├── public/                 # 静态资源
│   ├── cdd.ico            # 网站图标
│   ├── loading/           # 加载动画资源
│   ├── pics/              # 图片资源
│   ├── vtubers/           # VTuber 头像等
│   └── wordcloud/         # 词云相关资源
├── src/
│   ├── assets/            # 项目资源文件
│   │   ├── base.css       # 基础样式
│   │   ├── main.css       # 主样式文件
│   │   └── logo.svg       # Logo 文件
│   ├── router/            # 路由配置
│   │   └── index.ts       # 路由定义
│   ├── stores/            # 状态管理
│   │   ├── counter.ts     # 计数器状态
│   │   └── utils.ts       # 工具状态
│   ├── types/             # TypeScript 类型定义
│   │   ├── interfaces.ts  # 接口定义
│   │   └── vue-wordcloud.d.ts # 词云组件类型
│   ├── views/             # 页面组件
│   │   ├── HomeView.vue           # 首页
│   │   ├── PersonalSpace.vue      # 个人空间
│   │   ├── Dynamics.vue           # 动态分析
│   │   ├── Videos.vue             # 视频分析
│   │   ├── Comments.vue           # 评论分析
│   │   ├── LivesStreamHost.vue    # 直播数据
│   │   ├── Comparison.vue         # 对比分析
│   │   ├── UserAnalyze.vue        # 用户分析
│   │   ├── LoginView.vue          # 登录页面
│   │   ├── SignUpView.vue         # 注册页面
│   │   └── AnnouncementView.vue   # 公告页面
│   ├── App.vue            # 根组件
│   ├── main.ts            # 应用入口
│   └── api.ts             # API 接口
├── dist/                  # 构建输出目录
├── node_modules/          # 依赖包
├── package.json           # 项目配置
├── vite.config.ts         # Vite 配置
├── tsconfig.json          # TypeScript 配置
├── tailwind.config.js     # Tailwind CSS 配置
├── postcss.config.js      # PostCSS 配置
└── eslint.config.js       # ESLint 配置
```

## 📜 可用脚本

| 命令 | 描述 |
|------|------|
| `npm run dev` | 启动开发服务器 |
| `npm run build` | 构建生产版本 |
| `npm run preview` | 预览生产构建 |
| `npm run lint` | 运行 ESLint 代码检查 |

## 🔧 开发环境配置

### TypeScript 支持

项目使用 `vue-tsc` 进行类型检查，确保 `.vue` 文件的 TypeScript 支持。

## 🌐 部署

### 构建生产版本

```bash
npm run build
```

### 部署到静态服务器

构建完成后，将 `dist/` 目录的内容部署到任何静态文件服务器即可。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

## 🔗 相关链接

- [Vue 3 文档](https://vuejs.org/)
- [Vite 文档](https://vitejs.dev/)
- [Element Plus 文档](https://element-plus.org/)
- [TypeScript 文档](https://www.typescriptlang.org/)

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 查看 [Issues](../../issues) 页面
2. 创建新的 Issue 描述问题
3. 联系项目维护者

---

**注意**: 本项目是 VupBI 数据平台的前端部分，需要配合后端服务使用。请确保后端服务正常运行。
