import axios from 'axios'

const API_URL = import.meta.env.VITE_API_BASE_URL

const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

const handleApiError = (error: any) => {
  console.error('API Error:', error)
  if (error.response) {
    console.error('Response data:', error.response.data)
    console.error('Response status:', error.response.status)
    console.error('Response headers:', error.response.headers)
  } else if (error.request) {
    console.error('No response received:', error.request)
  } else {
    console.error('Error setting up request:', error.message)
  }
  throw error
}

export const api = {

  // --- Web User Authentication API Endpoints ---
  async registerUser(userData: any) {
    try {
      const response = await apiClient.post('/web/users/register', userData)
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async loginUser(credentials: any) {
    try {
      const response = await apiClient.post('/web/users/login', credentials)
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async updateUserPassword(updateData: any) {
    try {
      // Typically, this would require authentication (e.g., sending a token)
      // The backend at /web/users/password (PUT) should verify the user's current session/password
      const response = await apiClient.put('/web/users/password', updateData)
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  // This is for resetting password when user has forgotten it
  async resetPassword(resetData: any) {
    try {
      const response = await apiClient.put('/web/users/password', resetData)
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async deleteUser(deleteData: any) {
    try {
      const response = await apiClient.delete('/web/users/delete', { data: deleteData })
    } catch (error) {
      return handleApiError(error)
    }
  },

  // Send verification code
  async sendVerificationCode(codeData: { email: string; type: 'register' | 'reset_password' | 'change_email' }) {
    try {
      const response = await apiClient.post('/web/users/send_code', codeData)
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getBasicMid(vtuber: string) {
    try {
      const response = await apiClient.get('/basic/mid', { params: { vtuber } })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCurrentFollowers(vtuber: string) {
    try {
      const response = await apiClient.get('/basic/follower/current', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getAllFollowers(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/basic/follower/all', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getAllFollowersByGranularity(vtuber: string, recent: number, granularity: string) {
    try {
      const response = await apiClient.get('/basic/follower/all', {
        params: { vtuber, recent: recent, granularity: granularity },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCurrentDahanghai(vtuber: string) {
    try {
      const response = await apiClient.get('/basic/dahanghai/current', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getAllDahanghai(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/basic/dahanghai/all', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getAllDahanghaiByGranularity(vtuber: string, recent: number, granularity: string) {
    try {
      const response = await apiClient.get('/basic/dahanghai/all', {
        params: { vtuber, recent: recent, granularity: granularity },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCurrentStat(vtuber: string) {
    try {
      const response = await apiClient.get('/basic/stat/current', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getPeroidStat(vtuber: string, start: string, end: string) {
    try {
      const response = await apiClient.get('/basic/stat/peroid', {
        params: { vtuber, s: start, e: end},
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getAllStat(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/basic/stat/all', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getMedalRank(vtuber: string) {
    try {
      const response = await apiClient.get('/basic/medal_rank', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getBasicInfo(vtuber: string) {
    try {
      const response = await apiClient.get('/basic/info', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getLiveInfo(vtuber: string) {
    try {
      const response = await apiClient.get('/live/info', { params: { vtuber } })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getDynamicsList(vtuber: string) {
    try {
      const response = await apiClient.get('/dynamics/list', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getTopNDynamics(vtuber: string, start: string, end: string, n: number) {
    try {
      const response = await apiClient.get('/dynamics/top_n/', {
        params: { vtuber, s: start, e: end, n },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCurrentDynamics(vtuber: string) {
    try {
      const response = await apiClient.get('/dynamics/current', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getDahanghaiRate(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/basic/dahanghai/rate', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getFollowerRate(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/basic/follower/rate', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getVideoList(vtuber: string) {
    try {
      const response = await apiClient.get('/video/list', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getTopNVideos(vtuber: string, start: string, end: string, n: number, sortBy: string, sortOrder: string) {
    try {
      const response = await apiClient.get('/video/top_n/', {
        params: { vtuber, s: start, e: end, n, sort_by: sortBy, sort_order: sortOrder },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getVideoComments(vtuber: string) {
    try {
      const response = await apiClient.get('/comment/video', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getDynamicComments(vtuber: string) {
    try {
      const response = await apiClient.get('/comment/dynamic', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getTopNComments(vtuber: string, start: string, end: string, n: number) {
    try {
      const response = await apiClient.get('/comment/top_n', {
        params: { vtuber, s: start, e: end, n },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getTopNCommentUsers(
    vtuber: string,
    start: string,
    end: string,
    n: number
  ) {
    try {
      const response = await apiClient.get('/comment/top_n_user', {
        params: { vtuber, s: start, e: end, n },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCommentWordCloud(vtuber: string, start: string, end: string) {
    try {
      const response = await apiClient.get('/comment/wordcloud', {
        params: { vtuber, s: start, e: end },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getRecentRelationships(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/llm/relations', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getRecentSensiment(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/llm/sensiment', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getRecentSensimentBili(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/llm/sensiment/bili', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getRecentSensimentTieba(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/llm/sensiment/tieba', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCommentTopics(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/llm/topics', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getFollowerRiseNum(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/basic/follower/rise', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getDahanghaiRiseNum(vtuber: string, recent: number) {
    try {
      const response = await apiClient.get('/basic/dahanghai/rise', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getVideoConclusion(vtuber: string, bvid: string) {
    try {
      const response = await apiClient.get('/video/conclusion', {
        params: { vtuber, bvid: bvid },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getRencentVideoDayViews(vtuber: string, bvid: string, recent: number) {
    try {
      const response = await apiClient.get('/video/views/recent', {
        params: { vtuber, bvid: bvid, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCurrentVideoDayViews(vtuber: string, bvid: string) {
    try {
      const response = await apiClient.get('/video/views/current', {
        params: { vtuber, bvid: bvid },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getTargetVideoDayViews(vtuber: string, bvid: string, time: string) {
    try {
      const response = await apiClient.get('/video/views/target', {
        params: { vtuber, bvid: bvid, time: time },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getTopNVideoRiseByTime(vtuber: string, time: string, n: number) {
    try {
      const response = await apiClient.get('/video/views/top_n/day', {
        params: { vtuber, time: time, n: n },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getTopNVideoRiseByPeriod(
    vtuber: string,
    s: string,
    e: string,
    n: number
  ) {
    try {
      const response = await apiClient.get('/video/views/top_n/period', {
        params: { vtuber, s: s, e: e, n: n },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getDahanghaiWholeList(vtuber: string) {
    try {
      const response = await apiClient.get('/basic/dahanghai/list', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getGaonengWholeList(vtuber: string) {
    try {
      const response = await apiClient.get('/basic/gaoneng/list', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getRecentInfo(vtuber: string) {
    try {
      const response = await apiClient.get('/info/recent/dict', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getSummariseRiseReason(vtuber: string) {
    try {
      const response = await apiClient.get('/info/recent/sumary', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getAllliveStatus() {
    try {
      const response = await apiClient.get('/board/live')
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getAllDahanghaiAndFollowerStuff(recent: number) {
    try {
      const response = await apiClient.get('/board/stat', {
        params: { recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getAllTotalInfo() {
    try {
      const response = await apiClient.get('/board/info')
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getWholeFlowDahanghaiList(current: string, previous: string) {
    try {
      const response = await apiClient.get('/board/dahanghai/whole', {
        params: { c: current, p: previous },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getTargetFlowDahanghaiList(
    target: string,
    current: string,
    previous: string,
    n: number
  ) {
    try {
      const response = await apiClient.get('/board/dahanghai/target', {
        params: { target, c: current, p: previous, n },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getTargetPeriodFlowDahanghaiList(
    target: string,
    start1: string,
    start2: string,
    end1: string,
    end2: string,
    n: number
  ) {
    try {
      const response = await apiClient.get('/board/dahanghai/target_period', {
        params: { target, start_time_str1: start1, start_time_str2: start2, end_time_str1: end1, end_time_str2: end2, n },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getFollowerNewList(vtuber: string, recent: number = 7) {
    try {
      const response = await apiClient.get('/follower/new_list', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getFollowerReviewList(vtuber: string) {
    try {
      const response = await apiClient.get('/follower/review_list', {
        params: { vtuber },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getFollowerReviewRate(vtuber: string, recent: number = 7) {
    try {
      const response = await apiClient.get('/follower/review_rate', {
        params: { vtuber, recent: recent },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getLiveSessions(vtuber: string) {
    try {
      const response = await apiClient.get('/live/sessions', {
        params: { vtuber},
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getLiveOneSessionMinutes(liveId: string, inteval: number = 10) {
    try {
      const response = await apiClient.get('/live/one_session/minute', {
        params: { liveId, inteval: inteval },
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  // --- Creator Info API Endpoints ---

  // 创作者概览统计数据
  async getCreatorOverviewStatByDateRange(uid: string, startDate: string, endDate: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/overview/stat/by-date-range', {
        params: { uid, start_date: startDate, end_date: endDate, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCreatorOverviewStatByDate(uid: string, date: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/overview/stat/by-date', {
        params: { uid, date, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  // 创作者涨粉分析数据
  async getCreatorAttentionAnalyzeByDateRange(uid: string, startDate: string, endDate: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/attention/analyze/by-date-range', {
        params: { uid, start_date: startDate, end_date: endDate, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCreatorAttentionAnalyzeByDate(uid: string, date: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/attention/analyze/by-date', {
        params: { uid, date, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  // 创作者播放分析数据
  async getCreatorArchiveAnalyzeByDateRange(uid: string, startDate: string, endDate: string, period?: number, limit: number = 1000, offset: number = 0) {
    try {
      const params: any = { uid, start_date: startDate, end_date: endDate, limit, offset }
      if (period !== undefined) params.period = period

      const response = await apiClient.get('/creator/archive/analyze/by-date-range', { params })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCreatorArchiveAnalyzeByDate(uid: string, date: string, period?: number, limit: number = 1000, offset: number = 0) {
    try {
      const params: any = { uid, date, limit, offset }
      if (period !== undefined) params.period = period

      const response = await apiClient.get('/creator/archive/analyze/by-date', { params })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },



  // 创作者粉丝图表数据
  async getCreatorFanGraphByDateRange(uid: string, startDate: string, endDate: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/fan/graph/by-date-range', {
        params: { uid, start_date: startDate, end_date: endDate, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCreatorFanGraphByDate(uid: string, date: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/fan/graph/by-date', {
        params: { uid, date, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  // 创作者粉丝概览数据
  async getCreatorFanOverviewByDateRange(uid: string, startDate: string, endDate: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/fan/overview/by-date-range', {
        params: { uid, start_date: startDate, end_date: endDate, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCreatorFanOverviewByDate(uid: string, date: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/fan/overview/by-date', {
        params: { uid, date, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  // 创作者视频比较数据
  async getCreatorVideoCompareByDateRange(uid: string, startDate: string, endDate: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/video/compare/by-date-range', {
        params: { uid, start_date: startDate, end_date: endDate, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCreatorVideoCompareByDate(uid: string, date: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/video/compare/by-date', {
        params: { uid, date, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  // 创作者视频数据增量趋势
  async getCreatorVideoPandectByDateRange(uid: string, startDate: string, endDate: string, dataTypeColumn?: string, limit: number = 1000, offset: number = 0) {
    try {
      const params: any = { uid, start_date: startDate, end_date: endDate, limit, offset }
      if (dataTypeColumn) params.data_type_column = dataTypeColumn

      const response = await apiClient.get('/creator/video/pandect/by-date-range', { params })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCreatorVideoPandectByDate(uid: string, date: string, dataTypeColumn?: string, limit: number = 1000, offset: number = 0) {
    try {
      const params: any = { uid, date, limit, offset }
      if (dataTypeColumn) params.data_type_column = dataTypeColumn

      const response = await apiClient.get('/creator/video/pandect/by-date', { params })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  // 创作者稿件操作来源占比
  async getCreatorVideoSurveyByDateRange(uid: string, startDate: string, endDate: string, dataType?: number, limit: number = 1000, offset: number = 0) {
    try {
      const params: any = { uid, start_date: startDate, end_date: endDate, limit, offset }
      if (dataType !== undefined) params.data_type = dataType

      const response = await apiClient.get('/creator/video/survey/by-date-range', { params })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCreatorVideoSurveyByDate(uid: string, date: string, dataType?: number, limit: number = 1000, offset: number = 0) {
    try {
      const params: any = { uid, date, limit, offset }
      if (dataType !== undefined) params.data_type = dataType

      const response = await apiClient.get('/creator/video/survey/by-date', { params })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  // 创作者稿件播放来源占比
  async getCreatorVideoSourceByDateRange(uid: string, startDate: string, endDate: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/video/source/by-date-range', {
        params: { uid, start_date: startDate, end_date: endDate, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCreatorVideoSourceByDate(uid: string, date: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/video/source/by-date', {
        params: { uid, date, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  // 创作者播放分布情况（粉丝与路人）
  async getCreatorVideoViewDataByDateRange(uid: string, startDate: string, endDate: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/video/view_data/by-date-range', {
        params: { uid, start_date: startDate, end_date: endDate, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },

  async getCreatorVideoViewDataByDate(uid: string, date: string, limit: number = 1000, offset: number = 0) {
    try {
      const response = await apiClient.get('/creator/video/view_data/by-date', {
        params: { uid, date, limit, offset }
      })
      return response.data
    } catch (error) {
      return handleApiError(error)
    }
  },
}
