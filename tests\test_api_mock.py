"""
模拟数据库的 RESTful API 测试
避免数据库连接问题，专注测试 API 接口设计
"""

import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import TestClient
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.app import app

client = TestClient(app)

class TestNewRESTfulAPIs:
    """测试新的 RESTful API 接口设计"""
    
    @patch('backend.tools.query_web_user_info.register_web_user_info')
    def test_create_user_endpoint_design(self, mock_register):
        """测试用户创建接口的 RESTful 设计"""
        # 模拟成功注册
        mock_register.return_value = "user123"
        
        user_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "testpassword123",
            "phone": "1234567890"
        }
        
        response = client.post("/web/users", json=user_data)
        
        # 检查状态码 - 创建资源应该返回 201
        assert response.status_code == 201
        
        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 201
        assert response_data["message"] == "User created successfully"
        
        # 检查返回的用户数据结构
        user_info = response_data["data"]
        assert "id" in user_info
        assert "email" in user_info
        assert "username" in user_info
        assert "created_at" in user_info
        # 确保密码不在响应中
        assert "password" not in user_info
        assert "password_hash" not in user_info
    
    @patch('backend.tools.query_web_user_info.login_web_user_info')
    def test_create_auth_session_endpoint_design(self, mock_login):
        """测试认证会话创建接口的 RESTful 设计"""
        # 模拟成功登录
        mock_login.return_value = {
            "user_id": "user123",
            "username": "testuser",
            "email": "<EMAIL>"
        }
        
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = client.post("/web/auth/sessions", json=login_data)
        
        # 检查状态码
        assert response.status_code == 200
        
        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 200
        assert response_data["message"] == "Authentication successful"
        
        # 检查认证数据结构
        auth_data = response_data["data"]
        assert "access_token" in auth_data
        assert "token_type" in auth_data
        assert "user" in auth_data
        assert auth_data["token_type"] == "bearer"
    
    def test_vtuber_mid_endpoint_design(self):
        """测试 VTuber UID 获取接口的 RESTful 设计"""
        vtuber_name = "星瞳"
        
        # 测试新的 RESTful 接口
        response = client.get(f"/vtubers/{vtuber_name}/mid")
        
        # 检查状态码
        assert response.status_code in [200, 404, 500]  # 可能因为数据库问题返回500
        
        if response.status_code == 200:
            # 检查响应格式
            response_data = response.json()
            assert "code" in response_data
            assert "message" in response_data
            assert "data" in response_data
            
            # 检查数据结构
            vtuber_data = response_data["data"]
            assert "mid" in vtuber_data
            assert "vtuber_name" in vtuber_data
    
    def test_video_conclusion_endpoint_design(self):
        """测试视频结论接口的 RESTful 设计"""
        bvid = "BV11k4y1Y71i"
        
        # 测试新的 RESTful 接口
        response = client.get(f"/videos/{bvid}/conclusion")
        
        # 检查状态码
        assert response.status_code in [200, 404, 500]
        
        if response.status_code == 200:
            # 检查响应格式
            response_data = response.json()
            assert "code" in response_data
            assert "message" in response_data
            assert "data" in response_data
            
            # 检查数据结构
            conclusion_data = response_data["data"]
            assert "bvid" in conclusion_data
            assert "conclusion" in conclusion_data
            assert "generated_at" in conclusion_data
    
    def test_video_views_endpoint_design(self):
        """测试视频播放量接口的 RESTful 设计"""
        bvid = "BV11k4y1Y71i"
        
        # 测试新的 RESTful 接口
        response = client.get(f"/videos/{bvid}/views/current")
        
        # 检查状态码
        assert response.status_code in [200, 404, 500]
        
        if response.status_code == 200:
            # 检查响应格式
            response_data = response.json()
            assert "code" in response_data
            assert "message" in response_data
            assert "data" in response_data

class TestBackwardCompatibility:
    """测试向后兼容性"""
    
    def test_old_endpoints_still_exist(self):
        """测试旧接口仍然存在"""
        # 这些旧接口应该仍然可以访问（即使可能因为数据库问题失败）
        old_endpoints = [
            "/basic/mid",
            "/basic/follower/current", 
            "/basic/dahanghai/current",
            "/video/conclusion",
            "/web/users/register",
            "/web/users/login"
        ]
        
        for endpoint in old_endpoints:
            if endpoint in ["/web/users/register", "/web/users/login"]:
                # POST 接口
                response = client.post(endpoint, json={"test": "data"})
            elif endpoint == "/video/conclusion":
                # 需要参数的 GET 接口
                response = client.get(endpoint, params={"bvid": "test"})
            else:
                # 普通 GET 接口
                response = client.get(endpoint, params={"vtuber": "星瞳"})
            
            # 接口应该存在（不是 404），即使可能因为其他原因失败
            assert response.status_code != 404, f"Endpoint {endpoint} should exist for backward compatibility"

class TestResponseFormat:
    """测试响应格式标准化"""
    
    def test_success_response_format(self):
        """测试成功响应格式"""
        from backend.app import create_success_response
        
        response = create_success_response(
            data={"test": "data"},
            message="Test successful",
            code=200
        )
        
        assert "code" in response
        assert "message" in response
        assert "data" in response
        assert response["code"] == 200
        assert response["message"] == "Test successful"
        assert response["data"]["test"] == "data"
    
    def test_error_response_format(self):
        """测试错误响应格式"""
        from backend.app import create_error_response
        
        response = create_error_response(
            message="Test error",
            code=400,
            details=[{"field": "email", "message": "Invalid email"}]
        )
        
        assert "code" in response
        assert "message" in response
        assert response["code"] == 400
        assert response["message"] == "Test error"
        assert "details" in response
        assert len(response["details"]) == 1
    
    def test_paginated_response_format(self):
        """测试分页响应格式"""
        from backend.app import create_paginated_response
        
        test_data = [{"id": 1}, {"id": 2}, {"id": 3}]
        response = create_paginated_response(
            data=test_data,
            total=100,
            page=1,
            page_size=3
        )
        
        assert "data" in response
        assert "total" in response
        assert "page" in response
        assert "page_size" in response
        assert "has_next" in response
        assert response["total"] == 100
        assert response["has_next"] == True
        assert len(response["data"]) == 3

class TestRESTfulPrinciples:
    """测试 RESTful 设计原则"""
    
    def test_resource_oriented_urls(self):
        """测试资源导向的 URL 设计"""
        # 新的 URL 应该是资源导向的
        resource_urls = [
            "/web/users",  # 用户资源
            "/web/auth/sessions",  # 认证会话资源
            "/vtubers/星瞳/mid",  # VTuber 的 UID 资源
            "/vtubers/星瞳/followers/current",  # VTuber 的当前粉丝资源
            "/videos/BV123/conclusion",  # 视频的结论资源
            "/videos/BV123/views/current",  # 视频的当前播放量资源
        ]
        
        for url in resource_urls:
            # 检查 URL 是否符合资源导向设计
            assert not any(verb in url.lower() for verb in ['create', 'get', 'update', 'delete', 'register', 'login'])
            # 检查是否使用了复数形式（除了特定的单数资源）
            parts = url.split('/')
            for part in parts:
                if part in ['users', 'sessions', 'vtubers', 'videos', 'followers', 'views']:
                    # 这些应该是复数形式的资源
                    pass
    
    def test_http_methods_semantics(self):
        """测试 HTTP 方法语义"""
        # 测试不同 HTTP 方法的语义是否正确
        
        # POST 用于创建资源
        response = client.post("/web/users", json={"test": "data"})
        assert response.status_code != 405  # Method Not Allowed
        
        # GET 用于获取资源
        response = client.get("/vtubers/星瞳/mid")
        assert response.status_code != 405
        
        # PATCH 用于部分更新
        response = client.patch("/web/users/password", json={"test": "data"})
        assert response.status_code != 405
        
        # DELETE 用于删除资源
        response = client.request("DELETE", "/web/users", json={"test": "data"})
        assert response.status_code != 405

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
