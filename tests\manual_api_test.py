"""
手动 API 测试脚本
用于在本地环境中测试修改后的 RESTful API 接口
"""

import requests
import json
from datetime import datetime

# 配置
BASE_URL = "http://localhost:9022"

def test_api_endpoint(method, endpoint, data=None, params=None):
    """测试 API 接口的通用函数"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, params=params, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, timeout=10)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, timeout=10)
        elif method.upper() == "PATCH":
            response = requests.patch(url, json=data, timeout=10)
        elif method.upper() == "DELETE":
            response = requests.delete(url, json=data, timeout=10)
        else:
            return {"error": f"Unsupported method: {method}"}
        
        return {
            "status_code": response.status_code,
            "response": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
            "headers": dict(response.headers)
        }
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}
    except json.JSONDecodeError as e:
        return {"error": f"JSON decode error: {e}", "raw_response": response.text}

def test_new_user_apis():
    """测试新的用户认证 API"""
    print("\n=== 测试新的用户认证 API ===")
    
    # 测试用户创建
    print("\n1. 测试用户创建 POST /web/users")
    user_data = {
        "email": f"test_{datetime.now().timestamp()}@example.com",
        "username": "testuser",
        "password": "testpassword123",
        "phone": "1234567890"
    }
    result = test_api_endpoint("POST", "/web/users", data=user_data)
    print(f"状态码: {result.get('status_code')}")
    print(f"响应: {json.dumps(result.get('response'), indent=2, ensure_ascii=False)}")
    
    # 测试认证会话创建
    print("\n2. 测试认证会话创建 POST /web/auth/sessions")
    login_data = {
        "email": user_data["email"],
        "password": user_data["password"]
    }
    result = test_api_endpoint("POST", "/web/auth/sessions", data=login_data)
    print(f"状态码: {result.get('status_code')}")
    print(f"响应: {json.dumps(result.get('response'), indent=2, ensure_ascii=False)}")

def test_new_vtuber_apis():
    """测试新的 VTuber API"""
    print("\n=== 测试新的 VTuber API ===")
    
    vtuber_name = "星瞳"
    
    # 测试获取 VTuber UID
    print(f"\n1. 测试获取 VTuber UID GET /vtubers/{vtuber_name}/mid")
    result = test_api_endpoint("GET", f"/vtubers/{vtuber_name}/mid")
    print(f"状态码: {result.get('status_code')}")
    print(f"响应: {json.dumps(result.get('response'), indent=2, ensure_ascii=False)}")
    
    # 测试获取当前粉丝数
    print(f"\n2. 测试获取当前粉丝数 GET /vtubers/{vtuber_name}/followers/current")
    result = test_api_endpoint("GET", f"/vtubers/{vtuber_name}/followers/current")
    print(f"状态码: {result.get('status_code')}")
    print(f"响应: {json.dumps(result.get('response'), indent=2, ensure_ascii=False)}")
    
    # 测试获取粉丝历史
    print(f"\n3. 测试获取粉丝历史 GET /vtubers/{vtuber_name}/followers/history")
    params = {"recent": 30, "limit": 5, "offset": 0}
    result = test_api_endpoint("GET", f"/vtubers/{vtuber_name}/followers/history", params=params)
    print(f"状态码: {result.get('status_code')}")
    print(f"响应: {json.dumps(result.get('response'), indent=2, ensure_ascii=False)}")
    
    # 测试获取当前大航海数
    print(f"\n4. 测试获取当前大航海数 GET /vtubers/{vtuber_name}/dahanghai/current")
    result = test_api_endpoint("GET", f"/vtubers/{vtuber_name}/dahanghai/current")
    print(f"状态码: {result.get('status_code')}")
    print(f"响应: {json.dumps(result.get('response'), indent=2, ensure_ascii=False)}")
    
    # 测试获取时期统计
    print(f"\n5. 测试获取时期统计 GET /vtubers/{vtuber_name}/stats/period")
    params = {"start_date": "2024-01-01", "end_date": "2024-01-31"}
    result = test_api_endpoint("GET", f"/vtubers/{vtuber_name}/stats/period", params=params)
    print(f"状态码: {result.get('status_code')}")
    print(f"响应: {json.dumps(result.get('response'), indent=2, ensure_ascii=False)}")

def test_new_video_apis():
    """测试新的视频 API"""
    print("\n=== 测试新的视频 API ===")
    
    bvid = "BV11k4y1Y71i"
    
    # 测试获取视频结论
    print(f"\n1. 测试获取视频结论 GET /videos/{bvid}/conclusion")
    result = test_api_endpoint("GET", f"/videos/{bvid}/conclusion")
    print(f"状态码: {result.get('status_code')}")
    print(f"响应: {json.dumps(result.get('response'), indent=2, ensure_ascii=False)}")
    
    # 测试获取视频当前播放量
    print(f"\n2. 测试获取视频当前播放量 GET /videos/{bvid}/views/current")
    result = test_api_endpoint("GET", f"/videos/{bvid}/views/current")
    print(f"状态码: {result.get('status_code')}")
    print(f"响应: {json.dumps(result.get('response'), indent=2, ensure_ascii=False)}")

def test_new_dynamics_apis():
    """测试新的动态 API"""
    print("\n=== 测试新的动态 API ===")
    
    vtuber_name = "星瞳"
    
    # 测试获取动态
    print(f"\n1. 测试获取动态 GET /vtubers/{vtuber_name}/dynamics")
    params = {"limit": 5, "sort": "popularity", "start_date": "2024-01-01", "end_date": "2024-12-31"}
    result = test_api_endpoint("GET", f"/vtubers/{vtuber_name}/dynamics", params=params)
    print(f"状态码: {result.get('status_code')}")
    print(f"响应: {json.dumps(result.get('response'), indent=2, ensure_ascii=False)}")

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n=== 测试向后兼容性 ===")
    
    # 测试旧的接口是否仍然工作
    old_endpoints = [
        ("GET", "/basic/mid", {"vtuber": "星瞳"}),
        ("GET", "/basic/follower/current", {"vtuber": "星瞳"}),
        ("GET", "/basic/dahanghai/current", {"vtuber": "星瞳"}),
        ("GET", "/video/conclusion", {"bvid": "BV11k4y1Y71i"}),
    ]
    
    for method, endpoint, params in old_endpoints:
        print(f"\n测试旧接口: {method} {endpoint}")
        result = test_api_endpoint(method, endpoint, params=params)
        print(f"状态码: {result.get('status_code')}")
        if result.get('status_code') == 200:
            print("✅ 向后兼容性正常")
        else:
            print("❌ 向后兼容性问题")
            print(f"响应: {json.dumps(result.get('response'), indent=2, ensure_ascii=False)}")

def main():
    """主测试函数"""
    print("🚀 开始 RESTful API 手动测试")
    print(f"测试服务器: {BASE_URL}")
    
    # 检查服务器是否运行
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"✅ 服务器运行正常，状态码: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("请确保后端服务器在 http://localhost:9022 上运行")
        return
    
    # 运行测试
    test_new_user_apis()
    test_new_vtuber_apis()
    test_new_video_apis()
    test_new_dynamics_apis()
    test_backward_compatibility()
    
    print("\n✅ 手动测试完成！")

if __name__ == "__main__":
    main()
