<template>
  <div
    class="container mx-auto p-6 bg-gradient-to-b from-blue-50 to-white min-h-screen"
  >
    <!-- Header Section -->
    <h1
      class="text-3xl font-bold mb-8 text-center text-gray-800 animate-fade-in"
    >
      🤺 Vups数据对比
    </h1>

    <!-- Selection Controls -->
    <div
      class="mb-8 bg-white rounded-2xl shadow-lg p-6 transform transition-all duration-300 hover:shadow-xl"
    >
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <!-- VTuber Selection -->
        <div class="space-y-2 pl-8">
          <label class="text-gray-700 font-semibold block mb-2">选择 VUP</label>
          <el-select
            v-model="selectedVtubers"
            multiple
            filterable
            placeholder="选择要对比的VUP"
            class="w-full"
            :multiple-limit="10"
          >
            <el-option
              v-for="vtuber in vtubersList"
              :key="vtuber[0]"
              :label="vtuber[0]"
              :value="vtuber[0]"
            />
          </el-select>
        </div>

        <!-- Date Range Selection -->
        <div class="space-y-2 pl-8">
          <label class="text-gray-700 font-semibold block mb-2">时间范围</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            class="w-full"
            value-format="x"
          />
        </div>

        <div class="flex justify-end items-end mr-4">
          <el-button
            type="primary"
            @click="fetchData"
            :loading="loading"
            class="w-auto px-4 py-2 bg-orange-300 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          >
            <i class="el-icon-refresh mr-2"></i>开始分析
          </el-button>
        </div>
      </div>
    </div>

    <div
      v-if="loading"
      class="flex justify-center items-center"
    >
      <img src="/loading/001.gif" alt="Loading..." class="h-20 w-20" />
    </div>

    <!-- Comparison Results -->
    <div v-if="hasResults" class="space-y-8">
      <!-- Table Comparison -->
      <div
        class="bg-white rounded-2xl shadow-lg p-6 transition-all duration-300 hover:shadow-xl"
      >
        <div class="flex items-center mb-6">
          <i class="el-icon-data-analysis text-2xl text-blue-500 mr-3"></i>
          <h2 class="text-2xl font-bold text-gray-800">表格数据对比</h2>
        </div>
        <div class="overflow-x-auto">
          <el-table
            :data="tableData"
            border
            style="width: 100%"
            :header-cell-style="{
              background: '#f8fafc',
              color: '#1e293b',
              fontWeight: 'bold',
            }"
            :row-style="{ transition: 'background-color 0.3s ease' }"
            class="custom-table"
          >
            <el-table-column
              prop="name"
              label="VUP"
              fixed="left"
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="liveCount"
              label="直播场次"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="liveDuration"
              label="直播时长(小时)"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="avgDahanghai"
              label="平均大航海"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="avgIncome"
              label="场均营收"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="avgPay"
              label="场均付费次数"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="avgWatch"
              label="场均观看"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="avgLike"
              label="场均喜爱"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="avgDanmaku"
              label="场均弹幕"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="totalDanmaku"
              label="总弹幕"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="danmakuPerHour"
              label="每小时弹幕"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="avgInteraction"
              label="场均互动"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="maxOnline"
              label="最高在线"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="avgOnlineRank"
              label="场均在线"
              sortable
              header-align="center"
              align="center"
            />
            <el-table-column
              prop="avgEnterRoom"
              label="场均进房人数"
              sortable
              header-align="center"
              align="center"
            />

          </el-table>
        </div>
      </div>

      <!-- Radar Chart -->
      <div
        class="bg-white rounded-2xl shadow-lg p-6 transition-all duration-300 hover:shadow-xl"
      >
        <div
          class="flex flex-col md:flex-row justify-start items-start md:items-center mb-6 md:gap-4"
        >
          <div class="flex items-center mb-4 md:mb-0 md:flex-shrink-0">
            <i class="el-icon-pie-chart text-2xl text-purple-500 mr-3"></i>
            <h2 class="text-2xl font-bold text-gray-800">雷达图分析</h2>
          </div>
          <div class="space-y-2 md:space-y-0 md:w-fit">
            <el-checkbox-group
              v-model="selectedMetrics"
              class="flex flex-wrap gap-2"
            >
              <el-checkbox
                v-for="metric in metrics"
                :key="metric.key"
                :label="metric.key"
                class="metric-checkbox"
              >
                {{ metric.label }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div
          ref="radarChart"
          style="width: 100%; height: 600px"
          class="radar-chart"
        ></div>
      </div>

      <!-- Line Charts -->
      <div
        class="bg-white rounded-2xl shadow-lg p-6 transition-all duration-300 hover:shadow-xl"
      >
        <div
          class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6"
        >
          <div class="flex items-center mb-4 md:mb-0">
            <i class="el-icon-trend-charts text-2xl text-green-500 mr-3"></i>
            <h2 class="text-2xl font-bold text-gray-800">趋势分析</h2>
          </div>
          <el-radio-group
            v-model="timeGrouping"
            class="custom-radio-group bg-gray-100 p-2 rounded-lg"
          >
            <el-radio value="daily">按日</el-radio>
            <el-radio value="weekly">按周</el-radio>
          </el-radio-group>
        </div>

        <!-- <el-tabs type="border-card" class="custom-tabs">
          <el-tab-pane
            v-for="chart in lineCharts"
            :key="chart.key"
            :label="chart.label"
          >
            <div class="chart-container">
              <div :id="chart.key + 'Chart'" class="line-chart"></div>
            </div>
          </el-tab-pane>
        </el-tabs> -->

        <div v-for="chart in lineCharts" :key="chart.key" class="chart-item">
          <h3 class="text-xl font-bold pl-10 text-gray-600">
            {{ chart.label }}
          </h3>
          <div class="chart-container">
            <div :id="chart.key + 'Chart'" class="line-chart"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import * as echarts from 'echarts';
import { computed, inject, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { api } from '../api';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

interface LiveData {
  startDate: string
  stopDate: string
  totalIncome: number
  payCount: number
  watchCount: number
  likeCount: number
  danmakusCount: number
  interactionCount: number
  onlineRank: number
  aveOnlineRank: number
  aveEnterRoom: number
  enterRoom: number
}

interface VTuberData {
  name: string
  sessions: LiveData[]
  lastLiveDate: string
  lastLiveDanmakuCount: number
  lastLiveIncome: number
  lastWatchCount: number
  lastAveOnlineRankCount: number
  totalDanmakuCount: number
  totalIncome: number
  totalLiveCount: number
  totalLiveSecond: number
  periodStats: [
    number, // id
    string, // uid
    string, // name
    number, // timestamp
    string, // datetime
    number, // follower_num
    number, // dahanghai_num
    number, // video_total_num
    number, // article_total_num
    number, // likes_total_num
    number // elec_num
  ][]
}

interface TableDataItem {
  name: string
  liveCount: number
  liveDuration: string
  avgIncome: string
  avgPay: string
  avgWatch: string
  avgLike: string
  avgDanmaku: string
  totalDanmaku: number
  danmakuPerHour: string
  avgInteraction: string
  maxOnline: number
  avgOnlineRank: string
  avgEnterRoom: string
  avgDahanghai: string
  enterRoomCount: number
  [key: string]: string | number
}

const vtubersList = inject<string[][]>('vtubers', [])

// State
const selectedVtubers = ref<string[]>(['嘉然', '乃琳', '星瞳', '恬豆', '東雪蓮'])
const dateRange = ref<[number, number]>([
  dayjs().subtract(30, 'day').valueOf(),
  dayjs().valueOf(),
])
const loading = ref(false)
const vtuberData = ref<Record<string, VTuberData>>({})

// Chart refs
const radarChart = ref<HTMLElement | null>(null)
const radarInstance = ref<echarts.ECharts | null>(null)
const chartInstances = ref<Record<string, echarts.ECharts>>({})

// UI controls
const timeGrouping = ref('daily')
const selectedMetrics = ref([
  'liveCount',
  'avgIncome',
  'avgWatch',
  'avgDanmaku',
  'danmakuPerHour',
  'avgInteraction',
])

// Metrics configuration
const metrics = [
  { key: 'liveCount', label: '直播场次' },
  { key: 'liveDuration', label: '直播时长' },
  { key: 'avgIncome', label: '场均营收' },
  { key: 'avgPay', label: '场均付费' },
  { key: 'avgWatch', label: '场均观看' },
  { key: 'avgLike', label: '场均喜爱' },
  { key: 'avgDanmaku', label: '场均弹幕' },
  { key: 'danmakuPerHour', label: '每小时弹幕' },
  { key: 'avgInteraction', label: '场均互动' },
  { key: 'maxOnline', label: '最高在线' },
  { key: 'avgOnlineRank', label: '场均在线' },
  { key: 'avgEnterRoom', label: '场均进房人数' },
  { key: 'avgDahanghai', label: '平均大航海' },
]

const lineCharts = [
  { key: 'duration', label: '直播时长' },
  { key: 'income', label: '营收' },
  { key: 'interaction', label: '平均高能/场' },
  { key: 'watch', label: '平均观众/场' },
  { key: 'danmaku', label: '弹幕数' },
  { key: 'danmakuPerHour', label: '弹幕/小时' },
  // { key: 'dahanghai', label: '大航海数量' },
  // { key: 'follower', label: '粉丝变化' },
]

// Computed
const hasResults = computed(() => Object.keys(vtuberData.value).length > 0)

const tableData = computed<TableDataItem[]>(() => {
  return selectedVtubers.value
    .map(vtuber => {
      const data = vtuberData.value[vtuber]
      if (!data) return null

      const lives = filterLivesByDateRange(data.sessions)
      const totalDuration = calculateTotalDuration(lives)
      const durationHours = totalDuration / 3600

      return {
        name: vtuber,
        liveCount: lives.length,
        liveDuration: durationHours.toFixed(1),
        avgIncome: (sumProperty(lives, 'totalIncome') / lives.length).toFixed(
          0
        ),
        avgPay: (sumProperty(lives, 'payCount') / lives.length).toFixed(0),
        avgWatch: (sumProperty(lives, 'watchCount') / lives.length).toFixed(0),
        avgLike: (sumProperty(lives, 'likeCount') / lives.length).toFixed(0),
        avgDanmaku: (
          sumProperty(lives, 'danmakusCount') / lives.length
        ).toFixed(0),
        totalDanmaku: sumProperty(lives, 'danmakusCount'),
        danmakuPerHour: (
          sumProperty(lives, 'danmakusCount') / durationHours
        ).toFixed(0),
        avgInteraction: (
          sumProperty(lives, 'interactionCount') / lives.length
        ).toFixed(0),
        maxOnline: Math.max(...lives.map(live => live.onlineRank || 0)),
        avgOnlineRank: (sumProperty(lives, 'aveOnlineRank') / lives.length).toFixed(0),
        avgEnterRoom: (sumProperty(lives, 'enterRoom') / lives.length).toFixed(0),
        avgDahanghai: (
          data.periodStats.length > 0
            ? data.periodStats.reduce((sum, stat) => sum + stat[6], 0) /
              data.periodStats.length
            : 0
        ).toFixed(0),
      }
    })
    .filter((item): item is TableDataItem => item !== null)
})

// Methods
const initCharts = async () => {
  await nextTick()

  // Initialize radar chart
  if (radarChart.value && !radarInstance.value) {
    radarInstance.value = echarts.init(radarChart.value)
  }

  // Initialize line charts
  lineCharts.forEach(chart => {
    const el = document.getElementById(chart.key + 'Chart')
    if (el && !chartInstances.value[chart.key]) {
      chartInstances.value[chart.key] = echarts.init(el)
    }
  })
}

const fetchData = async () => {
  loading.value = true
  try {
    const [start, end] = dateRange.value
    const startDate = dayjs(start).format('YYYY-MM-DD')
    const endDate = dayjs(end).format('YYYY-MM-DD')

    const promises = selectedVtubers.value.map(async vtuber => {
      const liveSessions = await api.getLiveSessions(vtuber)
      const periodStats = await api.getPeroidStat(vtuber, startDate, endDate)
      return { vtuber, liveSessions, periodStats }
    })

    const results = await Promise.all(promises)

    vtuberData.value = Object.fromEntries(
      results.map(({ vtuber, liveSessions, periodStats }) => [
        vtuber,
        {
          ...liveSessions,
          sessions: liveSessions.sessions,
          periodStats: periodStats,
        },
      ])
    )

    await initCharts()
    updateCharts()
  } catch (error) {
    console.error('Error fetching data:', error)
  } finally {
    loading.value = false
  }
}

const filterLivesByDateRange = (lives: LiveData[]) => {
  if (!dateRange.value) return lives
  const [start, end] = dateRange.value
  return lives.filter(live => {
    const startDateTimestamp = dayjs(live.startDate).valueOf()
    const stopDateTimestamp = dayjs(live.stopDate).valueOf()
    return startDateTimestamp >= start && stopDateTimestamp <= end
  })
}

const calculateTotalDuration = (lives: LiveData[]) => {
  return lives.reduce((total, live) => {
    const startDateTimestamp = dayjs(live.startDate).valueOf()
    const stopDateTimestamp = dayjs(live.stopDate).valueOf()
    return total + (stopDateTimestamp - startDateTimestamp) / 1000
  }, 0)
}

const sumProperty = (array: LiveData[], property: keyof LiveData) => {
  return array.reduce((sum, item) => sum + (Number(item[property]) || 0), 0)
}

const updateRadarChart = () => {
  if (!radarChart.value) return

  if (!radarInstance.value) {
    radarInstance.value = echarts.init(radarChart.value)
  }

  const indicator = selectedMetrics.value.map(key => {
    const metric = metrics.find(m => m.key === key)
    const max = calculateMaxValue(key)
    return { name: metric?.label || key, max }
  })

  const colors = ['#60a5fa', '#f87171', '#34d399', '#a78bfa', '#fbbf24']

  const series = selectedVtubers.value.map((vtuber, index) => {
    const data = tableData.value.find(item => item.name === vtuber)
    return {
      value: selectedMetrics.value.map(key => Number(data?.[key] || 0)),
      name: vtuber,
      itemStyle: {
        color: colors[index % colors.length],
      },
      lineStyle: {
        width: 2,
      },
      areaStyle: {
        opacity: 0.1,
      },
    }
  })

  radarInstance.value.setOption({
    color: colors,
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#e2e8f0',
      textStyle: {
        color: '#1e293b',
      },
      extraCssText: 'box-shadow: 0 2px 4px rgba(0,0,0,0.1);',
    },
    legend: {
      data: selectedVtubers.value,
      orient: 'vertical',
      right: '5%',
      top: 'middle',
      textStyle: {
        color: '#64748b',
      },
      itemWidth: 15,
      itemHeight: 15,
      borderRadius: 3,
    },
    radar: {
      indicator,
      shape: 'circle',
      splitNumber: 5,
      axisName: {
        color: '#64748b',
        backgroundColor: '#f8fafc',
        borderRadius: 3,
        padding: [3, 5],
      },
      splitLine: {
        lineStyle: {
          color: '#e2e8f0',
        },
      },
      splitArea: {
        show: true,
        areaStyle: {
          color: ['#ffffff', '#f8fafc'],
        },
      },
      axisLine: {
        lineStyle: {
          color: '#e2e8f0',
        },
      },
    },
    series: [
      {
        type: 'radar',
        data: series,
        symbol: 'circle',
        symbolSize: 6,
        emphasis: {
          lineStyle: {
            width: 4,
          },
        },
      },
    ],
    animation: true,
    animationDuration: 300,
  })
}

const calculateMaxValue = (key: string) => {
  const values = tableData.value.map(item => Number(item[key] || 0))
  return Math.ceil(Math.max(...values) * 1.2)
}

const updateLineCharts = () => {
  lineCharts.forEach(chart => {
    const chartRef = chartInstances.value[chart.key]
    if (!chartRef) return

    const xAxis = getDateAxis()
    const allValues: number[] = []
    const colors = ['#60a5fa', '#f87171', '#34d399', '#a78bfa', '#fbbf24']

    const series = selectedVtubers.value.map((vtuber, index) => {
      const data = getChartData(vtuber, chart.key, xAxis)
      allValues.push(...data)
      return {
        name: vtuber,
        type: 'line',
        data,
        smooth: true,
        showSymbol: false,
        symbolSize: 8,
        emphasis: {
          focus: 'series',
          lineStyle: {
            width: 4,
          },
        },
        itemStyle: {
          color: colors[index % colors.length],
        },
        lineStyle: {
          width: 3,
        },
        areaStyle: {
          opacity: 0.1,
        },
      }
    })

    const axisConfig = getAxisConfig(
      allValues.filter(v => !isNaN(v) && v !== 0)
    )

    chartRef.setOption({
      color: colors,
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#e2e8f0',
        textStyle: {
          color: '#1e293b',
        },
        extraCssText: 'box-shadow: 0 2px 4px rgba(0,0,0,0.1);',
        formatter: (params: any[]) => {
          const date = params[0].axisValue
          let result = `${date}<br/>`
          params.forEach(param => {
            const value = formatNumber(param.value)
            let unit = ''
            switch (chart.key) {
              case 'income':
                unit = ' 元'
                break
              case 'danmakuPerHour':
                unit = ' 条/小时'
                break
              case 'danmaku':
                unit = ' 条'
                break
              case 'follower':
                unit = ' 人'
                break
              case 'dahanghai':
                unit = ' 人'
                break
            }
            result += `${param.marker}${param.seriesName}: ${value}${unit}<br/>`
          })
          return result
        },
      },
      legend: {
        data: selectedVtubers.value,
        type: 'scroll',
        bottom: 0,
        textStyle: {
          color: '#64748b',
        },
        itemWidth: 15,
        itemHeight: 15,
        borderRadius: 3,
        padding: [8, 16],
      },
      grid: {
        top: 40,
        left: '5%',
        right: '5%',
        bottom: 60,
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: xAxis.map(date => dayjs(date).format('YYYY-MM-DD')),
        axisLabel: {
          rotate: 45,
          hideOverlap: true,
          color: '#64748b',
        },
        axisLine: {
          lineStyle: {
            color: '#e2e8f0',
          },
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        min: axisConfig.min,
        max: axisConfig.max,
        interval: axisConfig.interval,
        axisLabel: {
          formatter: (value: number) => formatNumber(value),
          margin: 16,
          color: '#64748b',
        },
        splitLine: {
          lineStyle: {
            color: '#e2e8f0',
            type: 'dashed',
          },
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      series,
      animation: true,
      animationDuration: 300,
    })
  })
}

const getDateAxis = () => {
  if (!dateRange.value) return []
  const [start, end] = dateRange.value
  const dates = []
  let current = dayjs(start)
  const endDate = dayjs(end)

  while (current.isBefore(endDate) || current.isSame(endDate, 'day')) {
    dates.push(current.valueOf())
    current =
      timeGrouping.value === 'weekly'
        ? current.add(1, 'week')
        : current.add(1, 'day')
  }

  return dates
}

const getChartData = (vtuberName: string, metric: string, dates: number[]) => {
  const data = vtuberData.value[vtuberName]
  if (!data) return []

  return dates.map(date => {
    return calculateMetricValue(data, metric, date, timeGrouping.value);
  })
}

const filterLivesByDate = (
  lives: LiveData[],
  date: number,
  grouping: string
) => {
  const start = dayjs(date)
  const end =
    grouping === 'weekly' ? start.add(1, 'week') : start.add(1, 'day')

  return lives.filter(live => {
    const liveStart = dayjs(live.startDate)
    return liveStart.isAfter(start) && liveStart.isBefore(end)
  })
}

const calculateMetricValue = (
  data: VTuberData,
  metric: string,
  date: number,
  timeGrouping: string
) => {
  if (!data) return 0

  switch (metric) {
    case 'duration':
      const livesDuration = filterLivesByDate(data.sessions, date, timeGrouping)
      return calculateTotalDuration(livesDuration) / 3600
    case 'income':
      const livesIncome = filterLivesByDate(data.sessions, date, timeGrouping)
      return sumProperty(livesIncome, 'totalIncome')
    case 'interaction':
      const livesInteraction = filterLivesByDate(
        data.sessions,
        date,
        timeGrouping
      )
      return livesInteraction.length > 0
        ? sumProperty(livesInteraction, 'interactionCount') /
            livesInteraction.length
        : 0
    case 'watch':
      const livesWatch = filterLivesByDate(data.sessions, date, timeGrouping)
      return livesWatch.length > 0
        ? sumProperty(livesWatch, 'watchCount') / livesWatch.length
        : 0
    case 'danmaku':
      const livesDanmaku = filterLivesByDate(data.sessions, date, timeGrouping)
      return sumProperty(livesDanmaku, 'danmakusCount')
    case 'danmakuPerHour':
      const livesDanmakuPerHour = filterLivesByDate(
        data.sessions,
        date,
        timeGrouping
      )
      const durationDanmakuPerHour =
        calculateTotalDuration(livesDanmakuPerHour) / 3600
      return durationDanmakuPerHour > 0
        ? sumProperty(livesDanmakuPerHour, 'danmakusCount') /
            durationDanmakuPerHour
        : 0
    case 'follower':
      // TODO: Implement follower change calculation from extra.fansHistory
      return 0
    case 'dahanghai':
      // Calculate Dahanghai (Grand Voyage) count for the given period 
      const start = dayjs(date)
      const end =
        timeGrouping === 'weekly'
          ? start.add(1, 'week')
          : start.add(1, 'day')


      const filteredPeriodStats = data.periodStats.filter(stat => {

        const statDate = dayjs(stat[4]); // stat[4] is datetime
        const isWithinRange = statDate.isSameOrAfter(start, 'day') && statDate.isSameOrBefore(end, 'day');

        return isWithinRange;
      });
      const totalDahanghai = filteredPeriodStats.length > 0
        ? filteredPeriodStats.reduce((sum, stat) => sum + stat[6], 0)
        : 0;
      return totalDahanghai;
    default:
      return 0
  }
}

// Utility functions
const formatNumber = (value: number): string => {
  if (value >= *********) {
    return (value / *********).toFixed(1) + '亿'
  }
  if (value >= 10000) {
    return (value / 10000).toFixed(1) + 'w'
  }
  if (value >= 1000) {
    return (value / 1000).toFixed(1) + 'k'
  }
  if (value == 0) {
    return value.toFixed(0)
  }
  return value.toFixed(1)
}

const getAxisConfig = (values: number[]) => {
  if (values.length === 0 || values.every(v => v === 0)) {
    // If all values are 0 or no values, set a default range
    return { min: 0, max: 10, interval: 2 };
  }
  const max = Math.max(...values)
  const min = Math.min(...values)
  const range = max - min

  // Handle special cases where range is 0 but values are not 0
  if (range === 0 && max !== 0) {
    const buffer = max * 0.1 > 0 ? max * 0.1 : 1; // Ensure buffer is at least 1 if max is very small
    return {
      min: Math.max(0, min - buffer),
      max: max + buffer,
      interval: (max + buffer - Math.max(0, min - buffer)) / 5,
    }
  }

  // Calculate appropriate interval
  let interval = Math.pow(10, Math.floor(Math.log10(range)))

  // Adjust interval for better readability
  if (range / interval < 4) {
    interval = interval / 2
  } else if (range / interval > 8) {
    interval = interval * 2
  }

  // Ensure min is 0 for most metrics
  const adjustedMin = min < 0 ? min : 0
  const adjustedMax = Math.ceil(max / interval) * interval

  return {
    min: adjustedMin,
    max: adjustedMax,
    interval: interval,
  }
}

const updateCharts = () => {
  updateRadarChart()
  updateLineCharts()
}

// Lifecycle & Watch
onMounted(async () => {
  await initCharts()

  // Handle window resize
  window.addEventListener('resize', () => {
    if (radarInstance.value) {
      radarInstance.value.resize()
    }
    Object.values(chartInstances.value).forEach(chart => {
      chart.resize()
    })
  })
})

watch(selectedMetrics, () => {
  if (hasResults.value) {
    updateRadarChart()
  }
})

watch(timeGrouping, () => {
  if (hasResults.value) {
    updateLineCharts()
  }
})

// Clean up
onUnmounted(() => {
  // Remove resize listener
  window.removeEventListener('resize', () => {
    if (radarInstance.value) {
      radarInstance.value.resize()
    }
    Object.values(chartInstances.value).forEach(chart => {
      chart.resize()
    })
  })

  // Clean up chart instances
  if (radarInstance.value) {
    radarInstance.value.dispose()
  }

  Object.values(chartInstances.value).forEach(chart => {
    chart.dispose()
  })
})
</script>

<style scoped>
.el-table {
  width: 100%;
  margin-top: 1rem;
  border-radius: 0.5rem;
  overflow: hidden;
}

.custom-table :deep(td),
.custom-table :deep(th) {
  padding: 12px 8px;
}

.custom-table :deep(.el-table__row:hover) {
  background-color: #f1f5f9 !important;
}

.custom-tabs :deep(.el-tabs__header) {
  margin-bottom: 0;
  border-radius: 0.5rem 0.5rem 0 0;
  overflow: hidden;
}

.custom-tabs :deep(.el-tabs__nav) {
  border: none;
}

.custom-tabs :deep(.el-tabs__item) {
  padding: 12px 24px;
  transition: all 0.3s;
}

.custom-tabs :deep(.el-tabs__item.is-active) {
  background: #f8fafc;
  border-bottom: 2px solid #3b82f6;
}

.chart-container {
  position: relative;
  width: 100%;
  padding: 1.5rem;
  background: #ffffff;
  border-radius: 0 0 0.5rem 0.5rem;
}

.line-chart {
  width: 100%;
  height: 480px;
  min-height: 400px;
}

.metric-checkbox {
  margin-right: 1rem;
}

.radar-chart {
  border-radius: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .line-chart {
    height: 360px;
  }

  .metric-checkbox {
    margin-bottom: 0.5rem;
  }
}

:deep(.el-tabs__content) {
  overflow: visible;
}

:deep(.el-tab-pane) {
  min-height: 500px;
}

/* Custom transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #666;
}
</style>
