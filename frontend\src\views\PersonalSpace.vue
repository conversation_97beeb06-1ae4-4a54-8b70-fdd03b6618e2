<script setup lang="ts">
import dayjs from 'dayjs'
import type { Ref } from 'vue'
import { computed, inject, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { api } from '../api'
import { getRandomEmoji, getRecentDays } from '../stores/utils'
import { FanMedal, LivestreamData, RecentInfo, SensimentData, SensimentInfo, topAppealCommentor, topLikeCommentor, topReplyCommentor } from '../types/interfaces'

import * as echarts from 'echarts/core'

import { <PERSON><PERSON><PERSON>, LineChart, PieChart } from 'echarts/charts'
import {
  DatasetComponent,
  DataZoomComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  TooltipComponent,
  TransformComponent,
} from 'echarts/components'

import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DatasetComponent,
  TransformComponent,
  DataZoomComponent,
  LineChart,
  BarChart,
  PieChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
]);
type EChartsType = echarts.ECharts;
type EChartsCoreOption = echarts.EChartsCoreOption;

const route = useRoute()

const selectedVTuber = inject('selectedVTuber') as Ref<string>
const isDarkMode = inject('isDarkMode') as Ref<boolean>
const error = ref('')

const info = ref({
  name: '',
  face: '',
  sign: '',
  birthday: '',
  top_photo: '',
})

const recentInfo = ref<RecentInfo>({
  dynamic_content: [['', '']],
  live_content: '',
  video_content: [['', '', '', '']],
  rise_videos: [['', '', '', '', '']],
})

const options = [
  { value: 'today', text: '本日' },
  { value: 'week', text: '本周' },
  { value: 'month', text: '本月' },
  { value: '3months', text: '三月' },
]

const followerCount = ref(0)
const uid = ref('')
const dahanghai = ref(0)

const recentRelationships = ref<string[]>([])
const showAllRelationships = ref(false) // 控制最近联动板块的展开/折叠状态

const liveInfo = ref({
  if_live: false,
  live_url: '',
  live_title: '',
  live_cover: '',
  live_roomid: '',
})

const selectedSensimentRange = ref('3months')
const showSensimentWarning = ref(false)
const lowSensimentComments = ref<SensimentInfo>()

// ECharts refs
const followerChartRef = ref<HTMLElement | null>(null)
const dahanghaiChartRef = ref<HTMLElement | null>(null)
const videoPlayChartRef = ref<HTMLElement | null>(null)
const commentCountChartRef = ref<HTMLElement | null>(null)
const articleAllViewChartRef = ref<HTMLElement | null>(null)
const elecCountChartRef = ref<HTMLElement | null>(null)
const sensimentDistributionChartBiliRef = ref<HTMLElement | null>(null)
const sensimentDistributionChartTiebaRef = ref<HTMLElement | null>(null)
const sensimentTrendChartBiliRef = ref<HTMLElement | null>(null)
const sensimentTrendChartTiebaRef = ref<HTMLElement | null>(null)

// ECharts instances
const followerChart = ref<echarts.ECharts | null>(null)
const dahanghaiChart = ref<echarts.ECharts | null>(null)
const videoPlayChart = ref<echarts.ECharts | null>(null)
const commentCountChart = ref<echarts.ECharts | null>(null)
const articleAllViewChart = ref<echarts.ECharts | null>(null)
const elecCountChart = ref<echarts.ECharts | null>(null)
const sensimentDistributionChartBili = ref<echarts.ECharts | null>(null)
const sensimentDistributionChartTieba = ref<echarts.ECharts | null>(null)
const sensimentTrendChartBili = ref<echarts.ECharts | null>(null)
const sensimentTrendChartTieba = ref<echarts.ECharts | null>(null)

// ECharts options
const followerChartOption = ref<EChartsCoreOption>({})
const dahanghaiChartOption = ref<EChartsCoreOption>({})

// Chart granularity and recent data selection
const selectedFollowerChartType = ref('day'); // 默认按天
const selectedDahanghaiChartType = ref('day'); // 默认按天
const followerRecent = ref(3) // 默认3天数据
const dahanghaiRecent = ref(3) // 默认3天数据
// 粉丝数趋势图和大航海趋势图的切换选项
const chartTypeOptions = [
  { value: 'hour', text: '按小时' },
  { value: 'day', text: '按天' },
  { value: 'week', text: '按周' },
];

const recentActivities = ref<{ icon: string; text: string }[]>([])

const fanMedalRanking = ref<FanMedal[]>([])

const topLikeCommentors = ref<topLikeCommentor[]>([])
const topAppealCommentors = ref<topAppealCommentor[]>([])
const topReplyCommentors = ref<topReplyCommentor[]>([])

// 控制评论榜单的折叠状态
const showTopLikeCommentors = ref(false) // 最受爱戴
const showTopAppealCommentors = ref(false) // 最具匠人精神
const showTopReplyCommentors = ref(false) // 最能一呼百应

const dynamics = ref<any[]>([])

const followers = ref<[string, number][]>([])
const dahanghais = ref<[string, number][]>([])

const cStat = ref<any>([])
const aStat = ref<any[]>([])
const formattedCStat = computed(() => {
  const value = cStat.value.video_total_num
  return (value / 10000).toFixed(0) + 'w'
})

// 计算近一周视频平均播放量
const averageWeeklyVideoViews = computed(() => {
  if (!allVideoList.value || allVideoList.value.length === 0) {
    return '0';
  }

  const sevenDaysAgo = dayjs().subtract(7, 'day');
  const videosLastWeek = allVideoList.value.filter((video: any) => {
    const videoDate = dayjs(video[4]); // video[4] is the date
    return videoDate.isAfter(sevenDaysAgo);
  });

  if (videosLastWeek.length === 0) {
    return '0';
  }

  const totalViews = videosLastWeek.reduce((sum: number, video: any) => sum + video[5], 0); // video[5] is playCount
  return (totalViews / videosLastWeek.length).toFixed(0);
});

const recentVideos = ref<LivestreamData[]>([])

const allVideoList = ref<any[]>([]);
const wordCloudExists = ref(true)

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/error.png'
}

const handleWordCloudError = () => {
  wordCloudExists.value = false
}

// Helper function to format numbers for Y-axis
const formatNumber = (value: number): string => {
  if (value >= 100000000) {
    return (value / 100000000).toFixed(2) + '亿'
  }
  if (value >= 10000) {
    return (value / 10000).toFixed(2) + '万'
  }
  return value.toString()
}

// Initialize ECharts instance
const initChart = (chartRef: HTMLElement | null, chartInstance: any, options: EChartsCoreOption): EChartsType | null => {
  if (!chartRef) return null;
  if (chartInstance) {
    chartInstance.dispose(); // Dispose existing chart instance
  }
  const newChartInstance = echarts.init(chartRef, isDarkMode.value ? 'dark' : 'light');
  newChartInstance.setOption(options);
  return newChartInstance;
};

// Update ECharts instance
const updateChart = (chartInstance: EChartsType | null, options: EChartsCoreOption): void => {
  if (chartInstance) {
    chartInstance.setOption(options);
  }
};

interface ProcessedChartData {
  labels: string[];
  averagedData: { value: number; original: number }[];
}

const processChartData = (rawData: [string, number][], type: 'hour' | 'day' | 'week'): ProcessedChartData => {
  const aggregatedData: { [key: string]: { sum: number; count: number; originalValues: number[] } } = {};

  rawData.forEach(([timestampStr, value]) => {
    if (value === null || isNaN(value)) return;

    // Ensure timestampStr is in YYYYMMDDHH format
    const date = dayjs(timestampStr, 'YYYYMMDDHH');
    if (!date.isValid()) {
      console.error(`Invalid timestamp: ${timestampStr}`);
      return;
    }

    let key = '';
    if (type === 'hour') {
      key = date.format('YYYY-MM-DD HH:00');
    } else if (type === 'day') {
      key = date.format('YYYY-MM-DD');
    } else if (type === 'week') {
      key = date.startOf('week').format('YYYY-MM-DD');
    }

    if (!aggregatedData[key]) {
      aggregatedData[key] = { sum: 0, count: 0, originalValues: [] };
    }
    aggregatedData[key].sum += value;
    aggregatedData[key].count++;
    aggregatedData[key].originalValues.push(value);
  });

  const labels: string[] = [];
  const averagedData: { value: number; original: number }[] = [];

  const sortedKeys = Object.keys(aggregatedData).sort();

  sortedKeys.forEach(key => {
    labels.push(key);
    const avg = aggregatedData[key].sum / aggregatedData[key].count;
    const originalValue = aggregatedData[key].originalValues[aggregatedData[key].originalValues.length - 1];
    averagedData.push({ value: avg, original: originalValue });
  });

  return { labels, averagedData };
};

const updateFollowerChart = async () => {
  if (!followerChartRef.value) return;

  isLineLoading.value = true;
  try {
    const processedData = processChartData(followers.value, selectedFollowerChartType.value as 'hour' | 'day' | 'week');

    const labels = processedData.labels;
    const data = processedData.averagedData;

    const option: EChartsCoreOption = {
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const param = params[0];
          const value = param.value;
          let date = '';
          if (selectedFollowerChartType.value === 'hour') {
            date = dayjs(param.name).format('YYYY-MM-DD HH:mm');
          } else if (selectedFollowerChartType.value === 'day') {
            date = dayjs(param.name).format('YYYY-MM-DD');
          } else if (selectedFollowerChartType.value === 'week') {
            date = dayjs(param.name).format('YYYY-MM-DD [周]');
          }
          return `${date}<br/>${param.seriesName}: ${typeof value === 'number' ? Math.round(value).toLocaleString() : value}`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: labels.map(date => {
          if (selectedFollowerChartType.value === 'hour') {
            return dayjs(date).format('YY-MM-DD HH:mm');
          } else if (selectedFollowerChartType.value === 'day') {
            return dayjs(date).format('YY-MM-DD');
          } else if (selectedFollowerChartType.value === 'week') {
            return dayjs(date).format('YY-MM-DD');
          }
          return date;
        }),
        axisLabel: {
          color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
          interval: 'auto',
          rotate: 45,
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
          formatter: (value: number) => formatNumber(value),
        },
        splitLine: {
          lineStyle: {
            color: isDarkMode.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
          },
        },

      },
      series: [
        {
          name: '粉丝数',
          type: 'line',
          data: data.map(item => item.value),
          smooth: true,
          lineStyle: {
            color: '#4CAF50',
          },
          itemStyle: {
            color: '#4CAF50',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(76, 175, 80, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(76, 175, 80, 0)',
              },
            ]),
          },
        },
      ],
      dataZoom: [
        {
          type: 'inside',
          start: Math.max(0, 100 - (30 / labels.length) * 100), // 默认显示最近30天的数据
          end: 100,
        },
        {
          start: Math.max(0, 100 - (30 / labels.length) * 100), // 默认显示最近30天的数据
          end: 100,
        },
      ],
    };
    followerChartOption.value = option;
    followerChart.value = initChart(followerChartRef.value, followerChart.value, option);

    if (followerChart.value) {
      followerChart.value.on('datazoom', (params: any) => {
        const option = followerChart.value?.getOption();
        const dataZoomOption = (option?.dataZoom as any)?.[0];
        const start = dataZoomOption?.start ?? params.start ?? 0;
        const end = dataZoomOption?.end ?? params.end ?? 100;

        const fullData = processedData.averagedData;
        const startIndex = Math.floor((start / 100) * fullData.length);
        const endIndex = Math.ceil((end / 100) * fullData.length);
        const zoomedData = fullData.slice(startIndex, endIndex);

        if (zoomedData.length > 0) {
          const currentMaxValue = Math.max(...zoomedData.map(item => item.value));
          const currentMinValue = Math.min(...zoomedData.map(item => item.value));
          const newYAxisMax = Math.floor(currentMaxValue * 1.0005);
          const newYAxisMin = Math.floor(currentMinValue * 0.9995);

          followerChart.value?.setOption({
            yAxis: {
              min: newYAxisMin,
              max: newYAxisMax,
              axisLabel: {
                formatter: (value: number) => formatNumber(value)
              }
            },
          }, false);
        }
      });

      // 额外监听dataZoom事件的其他变体
      followerChart.value.on('dataZoom', (params: any) => {
        // 获取当前的缩放范围
        const option = followerChart.value?.getOption();
        const dataZoomOption = (option?.dataZoom as any)?.[0];
        const start = dataZoomOption?.start ?? params.start ?? 0;
        const end = dataZoomOption?.end ?? params.end ?? 100;

        const fullData = processedData.averagedData;
        const startIndex = Math.floor((start / 100) * fullData.length);
        const endIndex = Math.ceil((end / 100) * fullData.length);
        const zoomedData = fullData.slice(startIndex, endIndex);

        if (zoomedData.length > 0) {
          const currentMaxValue = Math.max(...zoomedData.map(item => item.value));
          const currentMinValue = Math.min(...zoomedData.map(item => item.value));
          const newYAxisMax = Math.floor(currentMaxValue * 1.0005);
          const newYAxisMin = Math.floor(currentMinValue * 0.9995);


          followerChart.value?.setOption({
            yAxis: {
              min: newYAxisMin,
              max: newYAxisMax,
              axisLabel: {
                formatter: (value: number) => formatNumber(value)
              }
            },
          }, false);
        }
      });

      const initialStart = Math.max(0, 100 - (30 / labels.length) * 100);
      const initialEnd = 100;
      const initialStartIndex = Math.floor((initialStart / 100) * processedData.averagedData.length);
      const initialEndIndex = Math.ceil((initialEnd / 100) * processedData.averagedData.length);
      const initialZoomedData = processedData.averagedData.slice(initialStartIndex, initialEndIndex);

      if (initialZoomedData.length > 0) {
        const initialMaxValue = Math.max(...initialZoomedData.map(item => item.value));
        const initialMinValue = Math.min(...initialZoomedData.map(item => item.value));
        const initialYAxisMax = Math.floor(initialMaxValue * 1.0005);
        const initialYAxisMin = Math.floor(initialMinValue * 0.9995);

        followerChart.value?.setOption({
          yAxis: {
            min: initialYAxisMin,
            max: initialYAxisMax,
            axisLabel: {
              formatter: (value: number) => formatNumber(value)
            }
          },
        });
      }
    }
  } catch (err) {
    console.error('Error fetching follower data:', err);
    error.value = '服务器开小差了😭，请联系管理员halyu';
  } finally {
    isLineLoading.value = false;
  }
};

const updateDahanghaiChart = async () => {
  if (!dahanghaiChartRef.value) return;

  isLineLoading.value = true;
  try {
    const processedData = processChartData(dahanghais.value, selectedDahanghaiChartType.value as 'hour' | 'day' | 'week');

    const labels = processedData.labels;
    const data = processedData.averagedData;
    const option: EChartsCoreOption = {
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const param = params[0];
          // 直接使用param.value，这是图表显示的平均值
          const value = param.value;
          let date = '';
          if (selectedDahanghaiChartType.value === 'hour') {
            date = dayjs(param.name).format('YYYY-MM-DD HH:mm');
          } else if (selectedDahanghaiChartType.value === 'day') {
            date = dayjs(param.name).format('YYYY-MM-DD');
          } else if (selectedDahanghaiChartType.value === 'week') {
            date = dayjs(param.name).format('YYYY-MM-DD [周]');
          }
          return `${date}<br/>${param.seriesName}: ${typeof value === 'number' ? Math.round(value).toLocaleString() : value}`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%', // Increase bottom space for dataZoom
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: labels.map(date => {
          if (selectedDahanghaiChartType.value === 'hour') {
            return dayjs(date).format('MM-DD HH:mm');
          } else if (selectedDahanghaiChartType.value === 'day') {
            return dayjs(date).format('MM-DD');
          } else if (selectedDahanghaiChartType.value === 'week') {
            // For week, display only MM-DD for brevity on the axis
            return dayjs(date).format('MM-DD');
          }
          return date;
        }),
        axisLabel: {
          color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
          interval: 'auto', // Auto adjust interval
          rotate: 45, // Rotate labels to prevent overlap
        },
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
          formatter: (value: number) => formatNumber(value), // Y轴仍然格式化
        },
        splitLine: {
          lineStyle: {
            color: isDarkMode.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
          },
        },

      },
      series: [
        {
          name: '大航海数',
          type: 'line',
          data: data.map(item => item.value),
          smooth: true,
          lineStyle: {
            color: '#FF5722', // 橙色
          },
          itemStyle: {
            color: '#FF5722',
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(255, 87, 34, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(255, 87, 34, 0)',
              },
            ]),
          },
        },
      ],
      dataZoom: [
        {
          type: 'inside',
          start: Math.max(0, 100 - (30 / labels.length) * 100), // 默认显示最近30天的数据
          end: 100,
        },
        {
          start: Math.max(0, 100 - (30 / labels.length) * 100), // 默认显示最近30天的数据
          end: 100,
        },
      ],
    };
    dahanghaiChartOption.value = option;
    dahanghaiChart.value = initChart(dahanghaiChartRef.value, dahanghaiChart.value, option);

    // Add datazoom event listener for dahanghai chart
    if (dahanghaiChart.value) { // Add null check
      // 监听datazoom事件，处理拖动和滚动缩放
      dahanghaiChart.value.on('datazoom', (params: any) => {
        // 获取当前的缩放范围
        const option = dahanghaiChart.value?.getOption();
        const dataZoomOption = (option?.dataZoom as any)?.[0];
        const start = dataZoomOption?.start ?? params.start ?? 0;
        const end = dataZoomOption?.end ?? params.end ?? 100;

        const fullData = processedData.averagedData;
        const startIndex = Math.floor((start / 100) * fullData.length);
        const endIndex = Math.ceil((end / 100) * fullData.length);
        const zoomedData = fullData.slice(startIndex, endIndex);

        if (zoomedData.length > 0) {
          const currentMaxValue = Math.max(...zoomedData.map(item => item.value));
          const currentMinValue = Math.min(...zoomedData.map(item => item.value));
          const newYAxisMax = Math.floor(currentMaxValue * 1.0005);
          const newYAxisMin = Math.floor(currentMinValue * 0.9995);

          dahanghaiChart.value?.setOption({
            yAxis: {
              min: newYAxisMin,
              max: newYAxisMax,
              axisLabel: {
                formatter: (value: number) => formatNumber(value)
              }
            },
          }, false);
        }
      });

      // 额外监听dataZoom事件的其他变体
      dahanghaiChart.value.on('dataZoom', (params: any) => {
        // 获取当前的缩放范围
        const option = dahanghaiChart.value?.getOption();
        const dataZoomOption = (option?.dataZoom as any)?.[0];
        const start = dataZoomOption?.start ?? params.start ?? 0;
        const end = dataZoomOption?.end ?? params.end ?? 100;

        const fullData = processedData.averagedData;
        const startIndex = Math.floor((start / 100) * fullData.length);
        const endIndex = Math.ceil((end / 100) * fullData.length);
        const zoomedData = fullData.slice(startIndex, endIndex);

        if (zoomedData.length > 0) {
          const currentMaxValue = Math.max(...zoomedData.map(item => item.value));
          const currentMinValue = Math.min(...zoomedData.map(item => item.value));
          const newYAxisMax = Math.floor(currentMaxValue * 1.0005);
          const newYAxisMin = Math.floor(currentMinValue * 0.9995);

          dahanghaiChart.value?.setOption({
            yAxis: {
              min: newYAxisMin,
              max: newYAxisMax,
              axisLabel: {
                formatter: (value: number) => formatNumber(value)
              }
            },
          }, false);
        }
      });
      const initialStart = Math.max(0, 100 - (30 / labels.length) * 100);
      const initialEnd = 100;
      const initialStartIndex = Math.floor((initialStart / 100) * processedData.averagedData.length);
      const initialEndIndex = Math.ceil((initialEnd / 100) * processedData.averagedData.length);
      const initialZoomedData = processedData.averagedData.slice(initialStartIndex, initialEndIndex);

      if (initialZoomedData.length > 0) {
        const initialMaxValue = Math.max(...initialZoomedData.map(item => item.value));
        const initialMinValue = Math.min(...initialZoomedData.map(item => item.value));
        const initialYAxisMax = Math.floor(initialMaxValue * 1.05);
        const initialYAxisMin = Math.floor(initialMinValue * 0.95);

        dahanghaiChart.value?.setOption({
          yAxis: {
            min: initialYAxisMin,
            max: initialYAxisMax,
          },
        });
      }
    }

  } catch (err) {
    console.error('Error fetching dahanghai data:', err);
    error.value = '服务器开小差了😭，请联系管理员halyu';
  } finally {
    isLineLoading.value = false;
  }
};

const createVideoPlayChart = () => {
  if (!videoPlayChartRef.value || recentVideos.value.length === 0) return

  const labels = recentVideos.value.map(item => item.date)
  const data = recentVideos.value.map(item => item.playCount)

  const maxValue = Math.max(...data, 1);
  const minValue = Math.min(...data, 0);
  const yAxisMax =  Math.floor(maxValue * 1.0005);
  const yAxisMin = Math.floor(minValue * 0.9995);

  const option: EChartsCoreOption = {
    // title: {
    //   text: '近期视频播放量',
    //   textStyle: {
    //     color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
    //   },
    // },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0]
        const index = param.dataIndex
        const videoTitle = recentVideos.value[index].title
        return `${videoTitle}<br/>${param.seriesName}: ${param.value}`
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisLabel: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
        rotate: 45,
      },
    },
    yAxis: {
      type: 'value',
      min: yAxisMin,
      max: yAxisMax,
      axisLabel: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
        formatter: (value: number) => formatNumber(value),
      },
      splitLine: {
        lineStyle: {
          color: isDarkMode.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
    series: [
      {
        name: '视频播放量',
        type: 'bar',
        data: data,
        itemStyle: {
          color: '#FFC107',
        },
      },
    ],
  }
  videoPlayChart.value = initChart(videoPlayChartRef.value, videoPlayChart.value, option)
}

const createCommentCountChart = () => {
  if (!commentCountChartRef.value || recentVideos.value.length === 0) return

  const labels = recentVideos.value.map(item => item.date)
  const data = recentVideos.value.map(item => item.commentCount)

  const maxValue = Math.max(...data, 1);
  const minValue = Math.min(...data, 0);
  const yAxisMax =  Math.floor(maxValue * 1.0005);
  const yAxisMin = Math.floor(minValue * 0.9995);

  const option: EChartsCoreOption = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0]
        const index = param.dataIndex
        const videoTitle = recentVideos.value[index].title
        return `${videoTitle}<br/>${param.seriesName}: ${param.value}`
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisLabel: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
        rotate: 45,
      },
    },
    yAxis: {
      type: 'value',
      min: yAxisMin,
      max: yAxisMax,
      axisLabel: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
        formatter: (value: number) => formatNumber(value),
      },
      splitLine: {
        lineStyle: {
          color: isDarkMode.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
    series: [
      {
        name: '视频评论数',
        type: 'bar',
        data: data,
        itemStyle: {
          color: '#F44336',
        },
      },
    ],
  }
  commentCountChart.value = initChart(commentCountChartRef.value, commentCountChart.value, option)
}

const createArticleAllViewChart = () => {
  if (!articleAllViewChartRef.value || Object.keys(aStat.value).length === 0) return

  const processedData = processChartData(
    aStat.value.map((item: any) => [item[0], parseInt(item[3])]), // 专栏总浏览量对应 item[3]
    'day'
  );

  const labels = processedData.labels;
  const data = processedData.averagedData;

  // Filter out invalid values (<= 0) when calculating minValue
  const validValues = data.map(item => item.value).filter(value => value > 0);
  const maxValue = validValues.length > 0 ? Math.max(...validValues) : 1;
  const minValue = validValues.length > 0 ? Math.min(...validValues) : 0;
  const yAxisMax = Math.floor(maxValue * 1.0005);
  const yAxisMin = Math.floor(minValue * 0.9995);

  const option: EChartsCoreOption = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        const value = param.value;
        const originalDate = labels[param.dataIndex];
        const date = dayjs(originalDate).format('YY-MM-DD');
        return `${date}<br/>${param.seriesName}: ${typeof value === 'number' ? Math.round(value).toLocaleString() : value}`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%', // Increase bottom space for dataZoom
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: labels.map(date => dayjs(date).format('YY-MM-DD')), // Format labels for display
      axisLabel: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
        interval: 'auto', // Auto adjust interval
        rotate: 45, // Rotate labels to prevent overlap
      },
    },
    yAxis: {
      type: 'value',
      min: yAxisMin > 0 ? yAxisMin : 0, // Ensure min is not negative
      max: yAxisMax,
      axisLabel: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
        formatter: (value: number) => formatNumber(value),
      },
      splitLine: {
        lineStyle: {
          color: isDarkMode.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
    series: [
      {
        name: '专栏总浏览量',
        type: 'line',
        data: data.map(item => item.value), // Use averaged data for the chart
        smooth: true,
        lineStyle: {
          color: '#2196F3',
        },
        itemStyle: {
          color: '#2196F3',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(33, 150, 243, 0.3)',
            },
            {
              offset: 1,
              color: 'rgba(33, 150, 243, 0)',
            },
          ]),
        },
      },
    ],
    dataZoom: [
        {
          type: 'inside',
          start: Math.max(0, 100 - (30 / labels.length) * 100), // 默认显示最近30天的数据
          end: 100,
        },
        {
          start: Math.max(0, 100 - (30 / labels.length) * 100), // 默认显示最近30天的数据
          end: 100,
        },
    ]
  }
  articleAllViewChart.value = initChart(articleAllViewChartRef.value, articleAllViewChart.value, option)
}

const createElecCountChart = () => {
  if (!elecCountChartRef.value || Object.keys(aStat.value).length === 0) return

  const processedData = processChartData(
    aStat.value.map((item: any) => [item[0], parseInt(item[4])]),
    'day'
  );

  const labels = processedData.labels;
  const data = processedData.averagedData;

  // Filter out invalid values (<= 0) when calculating minValue
  const validValues = data.map(item => item.value).filter(value => value > 0);
  const maxValue = validValues.length > 0 ? Math.max(...validValues) : 1;
  const minValue = validValues.length > 0 ? Math.min(...validValues) : 0;
  const yAxisMax = Math.floor(maxValue * 1.0005);
  const yAxisMin = Math.floor(minValue * 0.9995);

  const option: EChartsCoreOption = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        const value = param.value;
        const originalDate = labels[param.dataIndex];
        const date = dayjs(originalDate).format('YY-MM-DD');
        return `${date}<br/>${param.seriesName}: ${typeof value === 'number' ? Math.round(value).toLocaleString() : value}`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: labels.map(date => dayjs(date).format('YY-MM-DD')),
      axisLabel: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
        interval: 'auto',
        rotate: 45,
      },
    },
    yAxis: {
      type: 'value',
      min: yAxisMin > 0 ? yAxisMin : 0, // Ensure min is not negative
      max: yAxisMax,
      axisLabel: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
        formatter: (value: number) => value,
      },
      splitLine: {
        lineStyle: {
          color: isDarkMode.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
    series: [
      {
        name: '当月充电数',
        type: 'line',
        data: data.map(item => item.value),
        smooth: true,
        lineStyle: {
          color: '#9C27B0',
        },
        itemStyle: {
          color: '#9C27B0',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(156, 39, 176, 0.3)',
            },
            {
              offset: 1,
              color: 'rgba(156, 39, 176, 0)',
            },
          ]),
        },
      },
    ],
    dataZoom: [
      {
        type: 'inside',
        start: Math.max(0, 100 - (90 / labels.length) * 100),
        end: 100,
      },
      {
        start: Math.max(0, 100 - (90 / labels.length) * 100),
        end: 100,
      },
    ],
  };
  elecCountChart.value = initChart(elecCountChartRef.value, elecCountChart.value, option);
};

const createSensimentTrendChart = (
  chartRef: HTMLElement | null,
  data: SensimentData,
  label: string,
  chartInstanceRef: any
): void => {
  if (!chartRef || !data.info || data.info.length === 0) return;

  // Process data to calculate daily averages
  const dailyData: { [key: string]: { sum: number; count: number } } = {};

  // Group data by day and calculate sum and count
  data.info.forEach(item => {
    const date = dayjs(item.time, 'YYYYMMDDHHmmss').format('YYYY-MM-DD');
    if (!dailyData[date]) {
      dailyData[date] = { sum: 0, count: 0 };
    }
    dailyData[date].sum += item.sentiment;
    dailyData[date].count++;
  });

  // Calculate daily averages and create labels - ensure ascending date order
  const sortedDates = Object.keys(dailyData).sort((a, b) => a.localeCompare(b));
  const labels = sortedDates.map(date => dayjs(date).format('MM-DD'));
  const sensimentData = sortedDates.map(date => {
    const dayData = dailyData[date];
    return dayData.sum / dayData.count; // Calculate average for each day
  });

  // Handle empty array case
  const maxValue = sensimentData.length > 0 ? Math.max(...sensimentData) : 1;
  const minValue = sensimentData.length > 0 ? Math.min(...sensimentData) : 0;
  const yAxisMax = Math.floor(maxValue * 1.0005);
  const yAxisMin = Math.floor(minValue * 0.9995);

  const option: EChartsCoreOption = {
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>${param.seriesName}: ${param.value.toFixed(2)}`;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisLabel: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
        rotate: 45,
      },
    },
    yAxis: {
      type: 'value',
      min: yAxisMin,
      max: yAxisMax,
      axisLabel: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
        formatter: (value: number) => value.toFixed(1),
      },
      splitLine: {
        lineStyle: {
          color: isDarkMode.value ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        },
      },
    },
    series: [
      {
        name: '情绪指数',
        type: 'line',
        data: sensimentData,
        smooth: true,
        lineStyle: {
          color: '#7B1FA2',
        },
        itemStyle: {
          color: '#7B1FA2',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(123, 31, 162, 0.3)',
            },
            {
              offset: 1,
              color: 'rgba(123, 31, 162, 0)',
            },
          ]),
        },
      },
    ],
  };
  chartInstanceRef.value = initChart(chartRef, chartInstanceRef.value, option);
}
const createPieChart = (
  chartRef: HTMLElement | null,
  data: {
    love: number
    positive: number
    neutral: number
    critical: number
    negative: number
  },
  label: string,
  chartInstanceRef: any
): void => {
  if (!chartRef || !Object.values(data).some(val => val > 0)) {
    console.log('No chart ref or all values are 0, not creating chart')
    return
  }

  const option: EChartsCoreOption = {
    title: {
      text: label,
      left: 'center',
      textStyle: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: [
        '入脑评价',
        '正面评价',
        '中性评价',
        '尖锐评价',
        '负面评价',
      ],
      textStyle: {
        color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
      },
    },
    series: [
      {
        name: '舆情分布',
        type: 'pie',
        radius: '55%',
        center: ['50%', '60%'],
        data: [
          { value: data.love, name: '入脑评价' },
          { value: data.positive, name: '正面评价' },
          { value: data.neutral, name: '中性评价' },
          { value: data.critical, name: '尖锐评价' },
          { value: data.negative, name: '负面评价' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        itemStyle: {
          color: (params: any) => {
            const colors = [
              '#9966FF', // love
              '#4CAF50', // positive
              '#FFC107', // neutral
              '#2196F3', // critical
              '#F44336', // negative
            ]
            return colors[params.dataIndex]
          },
        },
        label: {
          color: isDarkMode.value ? '#e2e8f0' : '#1a202c',
        },
      },
    ],
  }
  chartInstanceRef.value = initChart(chartRef, chartInstanceRef.value, option);
}

const createSensimentCharts = () => {

  // Create Bili charts
  if (sensimentDistributionChartBiliRef.value && sensimentDataBili.value) {
    createPieChart(
      sensimentDistributionChartBiliRef.value,
      {
        love: sensimentDataBili.value.love_ratio,
        positive: sensimentDataBili.value.positive_ratio,
        neutral: sensimentDataBili.value.neutral_ratio,
        critical: sensimentDataBili.value.critical_ratio,
        negative: sensimentDataBili.value.negative_ratio,
      },
      'B站舆情分布',
      sensimentDistributionChartBili
    )
  }

  if (sensimentTrendChartBiliRef.value && sensimentDataBili.value) {
    createSensimentTrendChart(
      sensimentTrendChartBiliRef.value,
      sensimentDataBili.value,
      'B站趋势',
      sensimentTrendChartBili
    )
  }

  // Create Tieba charts
  if (sensimentDistributionChartTiebaRef.value && sensimentDataTieba.value) {
    createPieChart(
      sensimentDistributionChartTiebaRef.value,
      {
        love: sensimentDataTieba.value.love_ratio,
        positive: sensimentDataTieba.value.positive_ratio,
        neutral: sensimentDataTieba.value.neutral_ratio,
        critical: sensimentDataTieba.value.critical_ratio,
        negative: sensimentDataTieba.value.negative_ratio,
      },
      '贴吧舆情分布',
      sensimentDistributionChartTieba
    )
  }

  if (sensimentTrendChartTiebaRef.value && sensimentDataTieba.value) {
    createSensimentTrendChart(
      sensimentTrendChartTiebaRef.value,
      sensimentDataTieba.value,
      '贴吧趋势',
      sensimentTrendChartTieba
    )
  }
}

const createAllCharts = () => {
  nextTick(() => {
    updateFollowerChart()
    updateDahanghaiChart()
    createVideoPlayChart()
    createCommentCountChart()
    createArticleAllViewChart()
    createElecCountChart()
  })
}

const createSensiCharts = () => {
  nextTick(() => {
    createSensimentCharts()
  })
}

const selectedDate = ref(dayjs().subtract(1, 'day').format('YYYY-MM-DD'))

const videoCount = ref(10)
const topVideos = ref<any[]>([])

const followerRiseNum = ref(0)
const dahanghaiRiseNum = ref(0)
const summariseRiseReason = ref('')

const followerRiseColor = computed(() =>
  followerRiseNum.value > 0
    ? 'text-red-500'
    : followerRiseNum.value < 0
      ? 'text-green-500'
      : 'text-gray-500'
)
const dahanghaiRiseColor = computed(() =>
  dahanghaiRiseNum.value > 0
    ? 'text-red-500'
    : dahanghaiRiseNum.value < 0
      ? 'text-green-500'
      : 'text-gray-500'
)
const followerRiseArrow = computed(() =>
  followerRiseNum.value > 0 ? '↑' : followerRiseNum.value < 0 ? '↓' : '→'
)
const dahanghaiRiseArrow = computed(() =>
  dahanghaiRiseNum.value > 0 ? '↑' : dahanghaiRiseNum.value < 0 ? '↓' : '→'
)

function getRateColor(value: string) {
  return value.startsWith('-') ? 'text-green-500' : 'text-red-500'
}

function getRateArrow(value: string) {
  return value.startsWith('-') ? '↓' : '↑'
}

const monthsDRColor = computed(() => getRateColor(monthsDR.value))
const seasonsDRColor = computed(() => getRateColor(seasonsDR.value))
const yearDRColor = computed(() => getRateColor(yearDR.value))
const monthsFRColor = computed(() => getRateColor(monthsFR.value))
const seasonsFRColor = computed(() => getRateColor(seasonsFR.value))
const yearFRColor = computed(() => getRateColor(yearFR.value))

const followerRRColor = computed(() => getRateColor(followerRR.value))

const monthsDRArrow = computed(() => getRateArrow(monthsDR.value))
const seasonsDRArrow = computed(() => getRateArrow(seasonsDR.value))
const yearDRArrow = computed(() => getRateArrow(yearDR.value))
const monthsFRArrow = computed(() => getRateArrow(monthsFR.value))
const seasonsFRArrow = computed(() => getRateArrow(seasonsFR.value))
const yearFRArrow = computed(() => getRateArrow(yearFR.value))


// Date range state
const startDate = ref(dayjs().subtract(90, 'day').format('YYYY-MM-DD'))
const endDate = ref(dayjs().format('YYYY-MM-DD'))
const topN = ref(10)
const followerRR = ref('0')

const sensimentData = ref<SensimentData | null>(null)
const sensimentDataBili = ref<SensimentData | null>(null)
const sensimentDataTieba = ref<SensimentData | null>(null)

const isLoading = inject('isLoading') as Ref<boolean>
const isSensiLoading = ref<boolean>(false)
const isTopNVideoLoading = ref<boolean>(false)
const isRecentInfoLoading = ref<boolean>(false)
const isCriticalLoading = ref<boolean>(false)
const isLineLoading = ref<boolean>(false)
const isExtraLoading = ref<boolean>(false)
const isNonCriticalLoading = ref<boolean>(false)
const isSummaryLoading = ref<boolean>(false)
const isCommentorsLoading = ref<boolean>(false) // 新增评论榜单加载状态

const monthsDR = ref('0')
const seasonsDR = ref('0')
const yearDR = ref('0')
const monthsFR = ref('0')
const seasonsFR = ref('0')
const yearFR = ref('0')
const recent = ref(-1) // 默认设置为-1，获取全部数据

// Add these back to fix ts-plugin errors
const videoPlayHistory = ref<{ [key: string]: number }>({});
const commentCountHistory = ref<{ [key: string]: number }>({});

const formatDynamicContent = (content: string) => {
  if (!content) return ''

  // Format emoji to [星瞳·动态表情包_xxx] pattern
  const emojiFormatted = content.replace(/\[([^\]]+?)\]/g, (match, p1) => {
    return ``
  })

  // Convert URLs to clickable links
  const urlRegex = /(https?:\/\/[^\s]+)/g
  return emojiFormatted.replace(urlRegex, url => {
    return `<a href="${url}" target="_blank" rel="noopener noreferrer">${url}</a>`
  })
}


const fetchSensimentData = async () => {
  isSensiLoading.value = true
  try {
    const [data, bilidata, tiebadata] = await Promise.all([
      api.getRecentSensiment(selectedVTuber.value, getRecentDays(selectedSensimentRange.value)),
      api.getRecentSensimentBili(selectedVTuber.value, getRecentDays(selectedSensimentRange.value)),
      api.getRecentSensimentTieba(selectedVTuber.value, getRecentDays(selectedSensimentRange.value)),
    ])

    sensimentData.value = data
    sensimentDataBili.value = bilidata
    sensimentDataTieba.value = tiebadata

    const recentNegativeRatio =
      data.info
        .slice(-3)
        .reduce(
          (sum: number, item: SensimentInfo) => sum + (1 - item.sentiment),
          0
        ) / 3
    showSensimentWarning.value = recentNegativeRatio > 0.8
    if (showSensimentWarning.value) {
      lowSensimentComments.value = data.info
        .filter(
          (item: SensimentInfo) =>
            item.sentiment < 0.5 && item.comment.length > 10
        )
        .flatMap((item: SensimentInfo) => item.comment)
        .slice(0, 3)
    }

  } catch (err) {
    console.error('Error fetching sentiment data:', err)
  } finally {
    isSensiLoading.value = false
  }
}

const fetchTopNVideoRise = async () => {
  isTopNVideoLoading.value = true
  try {
    const data = await api.getTopNVideoRiseByTime(
      selectedVTuber.value,
      selectedDate.value,
      videoCount.value
    )
    topVideos.value = data
  } catch (err) {
    console.error('Error fetching top N video rise:', err)
    error.value =
      '服务器开小差了😭，请联系管理员halyu'
  } finally {
    isTopNVideoLoading.value = false
  }
}

const fetchRecentInfo = async () => {
  isRecentInfoLoading.value = true
  try {
    const data = await api.getRecentInfo(selectedVTuber.value)
    recentInfo.value = data

    const activities = []

    if (recentInfo.value.video_content.length > 0) {
      activities.push({
        icon: '🎥',
        text: `上传新视频："${recentInfo.value.video_content[1]}"`,
      })
    }

    if (recentInfo.value.live_content) {
      activities.push({
        icon: '🔴',
        text: `进行了直播: "${recentInfo.value.live_content}"`,
      })
    }

    if (recentInfo.value.dynamic_content.length > 0) {
      const dynamicText = recentInfo.value.dynamic_content[1]
      activities.push({
        icon: '💬',
        text: `更新了动态: "${dynamicText.length > 30
            ? dynamicText.slice(0, 30) + '...'
            : dynamicText
          }"`,
      })
    }

    if (recentInfo.value.rise_videos.length > 0) {
      activities.push({
        icon: '🏆',
        text: `日增长：视频“${recentInfo.value.rise_videos[0][2]}” 获得了${recentInfo.value.rise_videos[0][4]}的播放日增长`,
      })
    }

    recentActivities.value = activities
  } catch (err) {
    console.error('Error fetching recent info:', err)
    error.value =
      '服务器开小差了😭，请联系管理员halyu'
  } finally {
    isRecentInfoLoading.value = false
  }
}

const fetchRiseNumbers = async () => {
  try {
    const [followerRise, dahanghaiRise] = await Promise.all([
      api.getFollowerRiseNum(selectedVTuber.value, 1),
      api.getDahanghaiRiseNum(selectedVTuber.value, 1),
    ])
    followerRiseNum.value = followerRise
    dahanghaiRiseNum.value = dahanghaiRise
  } catch (err) {
    console.error('Error fetching rise numbers:', err)
    error.value =
      '服务器开小差了😭，请联系管理员halyu'
  }
}

const fetchCriticalData = async () => {
  isCriticalLoading.value = true
  try {
    const [
      basicInfo,
      midValue,
      currentFollowers,
      currentDahanghai,
      liveInfoData,
    ] = await Promise.all([
      api.getBasicInfo(selectedVTuber.value),
      api.getBasicMid(selectedVTuber.value),
      api.getCurrentFollowers(selectedVTuber.value),
      api.getCurrentDahanghai(selectedVTuber.value),
      api.getLiveInfo(selectedVTuber.value),
    ])

    info.value = basicInfo

    uid.value = midValue
    followerCount.value = currentFollowers
    dahanghai.value = currentDahanghai

    liveInfo.value = {
      if_live: liveInfoData[3] == 1,
      live_url: "https://live.bilibili.com/" + liveInfoData[1] ,
      live_title: liveInfoData[5],
      live_cover: liveInfoData[6],
      live_roomid: liveInfoData[1],
    }

    // Fetch rise numbers after critical data is loaded
    await fetchRiseNumbers()

    await fetchRateData()

    await fetchFollowerReviewRateData()
  } catch (err) {
    console.error('Error fetching critical data:', err)
    error.value =
      '服务器开小差了😭，请联系管理员halyu'
  } finally {
    isCriticalLoading.value = false
  }
}

const fetchFollowerReviewRateData = async () => {
  isCriticalLoading.value = true
  try {
    const followerReviewRate = await api.getFollowerReviewRate(
      selectedVTuber.value,
      7
    )

    followerRR.value = followerReviewRate
  } catch (err) {
    console.error('Error fetching critical data:', err)
    error.value =
      '服务器开小差了😭，请联系管理员halyu'
  } finally {
    isCriticalLoading.value = false
  }
}

const fetchRateData = async () => {
  isCriticalLoading.value = true
  try {
    const [
      monthsDahanghaiRate,
      seasonsDahanghaiRate,
      yearDahanghaiRate,
      monthsFollowerRate,
      seasonsFollowerRate,
      yearFollowerRate,
    ] = await Promise.all([
      api.getDahanghaiRate(selectedVTuber.value, 30),
      api.getDahanghaiRate(selectedVTuber.value, 90),
      api.getDahanghaiRate(selectedVTuber.value, 365),
      api.getFollowerRate(selectedVTuber.value, 30),
      api.getFollowerRate(selectedVTuber.value, 90),
      api.getFollowerRate(selectedVTuber.value, 365),
    ])

    monthsDR.value = monthsDahanghaiRate
    seasonsDR.value = seasonsDahanghaiRate
    yearDR.value = yearDahanghaiRate
    monthsFR.value = monthsFollowerRate
    seasonsFR.value = seasonsFollowerRate
    yearFR.value = yearFollowerRate
  } catch (err) {
    console.error('Error fetching critical data:', err)
    error.value =
      '服务器开小差了😭，请联系管理员halyu'
  } finally {
    isCriticalLoading.value = false
  }
}

const fetchLineData = async () => {
  isLineLoading.value = true
  let videoListResult: any[] = []; // 声明在外部
  let allStat: any[] = []; // 声明在外部
  try {
    // 获取粉丝数和大航海数据，默认获取最近一周的数据
    const [allFollowers, allDahanghai, fetchedVideoListResult, currentStat, fetchedAllStat] =
      await Promise.all([
        api.getAllFollowers(selectedVTuber.value, recent.value),
        api.getAllDahanghai(selectedVTuber.value, recent.value),
        api.getVideoList(selectedVTuber.value),
        api.getCurrentStat(selectedVTuber.value),
        api.getAllStat(selectedVTuber.value, recent.value),
      ])

    videoListResult = fetchedVideoListResult; // 赋值给外部变量
    allStat = fetchedAllStat; // 赋值给外部变量

    allVideoList.value = videoListResult;
    followers.value = allFollowers
    dahanghais.value = allDahanghai

    // Process video list
    videoPlayHistory.value = {}
    commentCountHistory.value = {}
    videoListResult.forEach((video: any) => { // 使用 videoListResult
      const date = new Date(video[4]).toISOString().split('T')[0]
      videoPlayHistory.value[date] =
        (videoPlayHistory.value[date] || 0) + video[5]
      commentCountHistory.value[date] =
        (commentCountHistory.value[date] || 0) + video[6]
    })

    cStat.value = currentStat
    aStat.value = allStat

    // Process recent livestreams
    const recentVideoList = videoListResult.slice(0, 10) // TODO: 使用 videoListResult

    recentVideos.value = recentVideoList.map((video: any) => ({
      date: new Date(video[4]).toISOString().split('T')[0],
      title: video[1],
      playCount: video[5],
      commentCount: video[6],
    }))

  } catch (err) {
    console.error('Error fetching non-critical data:', err)
    error.value =
      '服务器开小差了😭，请联系管理员halyu'
  } finally {
    isLineLoading.value = false;
    createAllCharts();
  }
}

const fetchTopCommentorsData = async () => {
  isCommentorsLoading.value = true // 设置加载状态
  try {
    const topCommentors = await api.getTopNCommentUsers(
      selectedVTuber.value,
      startDate.value,
      endDate.value,
      topN.value
    )
    topLikeCommentors.value = topCommentors[0]
    topAppealCommentors.value = topCommentors[1]
    topReplyCommentors.value = topCommentors[2]
  } catch (err) {
    console.error('Error fetching top commentors data:', err)
    error.value =
      '服务器开小差了😭，请联系管理员halyu'
  } finally {
    isCommentorsLoading.value = false // 恢复加载状态
  }
}

const fetchExtraData = async () => {
  isExtraLoading.value = true
  try {
    await fetchRecentInfo()
    const medalRank = await api.getMedalRank(selectedVTuber.value)
    // 移除此处对 topCommentors 的请求
    const currentDynamics = await api.getCurrentDynamics(selectedVTuber.value)
    const recentRelationsData = await api.getRecentRelationships(
      selectedVTuber.value,
      100
    )

    fanMedalRanking.value = medalRank.rank_list

    // topLikeCommentors.value = topCommentors[0]
    // topAppealCommentors.value = topCommentors[1]
    // topReplyCommentors.value = topCommentors[2]

    dynamics.value = currentDynamics

    recentRelationships.value = recentRelationsData
  } catch (err) {
    console.error('Error fetching non-critical data:', err)
    error.value =
      '服务器开小差了😭，请联系管理员halyu'
  } finally {
    isExtraLoading.value = false
  }
}

const fetchSummaryData = async () => {
  isSummaryLoading.value = true
  try {
    const summariseRiseReasonRes = await api.getSummariseRiseReason(
      selectedVTuber.value
    )

    summariseRiseReason.value = summariseRiseReasonRes
  } catch (err) {
    console.error('Error fetching rise numbers:', err)
    error.value =
      '服务器开小差了😭，请联系管理员halyu'
  } finally {
    isSummaryLoading.value = false
  }
}

const fetchData = async () => {
  isLoading.value = true
  error.value = ''

  try {
    await fetchCriticalData()
    await fetchLineData()
    await fetchExtraData()
  } finally {
    isLoading.value = false
  }
}

const formattedSummariseRiseReason = computed(() => {
  return summariseRiseReason.value.replace(/\n/g, '<br>')
})

const resizeCharts = () => {
  // 遍历所有图表实例并调用 resize 方法
  if (followerChart.value) followerChart.value.resize();
  if (dahanghaiChart.value) dahanghaiChart.value.resize();
  if (videoPlayChart.value) videoPlayChart.value.resize();
  if (commentCountChart.value) commentCountChart.value.resize();
  if (articleAllViewChart.value) articleAllViewChart.value.resize();
  if (elecCountChart.value) elecCountChart.value.resize();
  if (sensimentDistributionChartBili.value) sensimentDistributionChartBili.value.resize();
  if (sensimentDistributionChartTieba.value) sensimentDistributionChartTieba.value.resize();
  if (sensimentTrendChartBili.value) sensimentTrendChartBili.value.resize();
  if (sensimentTrendChartTieba.value) sensimentTrendChartTieba.value.resize();
};

onMounted(async () => {
  const vtuberParam = route.params.vtuber
  if (vtuberParam) {
    selectedVTuber.value = vtuberParam as string
  }
  await fetchData()
  await fetchTopNVideoRise()
  await fetchSensimentData()
  // 默认不加载评论榜单数据
  // await fetchTopCommentorsData()

  // 添加窗口大小变化监听器
  window.addEventListener('resize', resizeCharts);
})

// 在组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', resizeCharts);
});

watch(
  () => route.params.vtuber,
  newVtuber => {
    if (newVtuber) {
      selectedVTuber.value = newVtuber as string
      fetchData()
      fetchTopNVideoRise()
      fetchSensimentData()
      // 默认不加载评论榜单数据
      // fetchTopCommentorsData()
    }
  })

watch(selectedSensimentRange, fetchSensimentData)

watch(isDarkMode, () => {
  createAllCharts();
  createSensiCharts();
});

watch(
  [recentVideos, aStat], // 监听 recentVideos 和 aStat 的变化
  createAllCharts,
  { deep: true }
)

watch(
  selectedFollowerChartType,
  () => {
    updateFollowerChart();
  },
  { immediate: true }
);

watch(
  selectedDahanghaiChartType,
  () => {
    updateDahanghaiChart();
  },
  { immediate: true }
);

watch([sensimentDataBili, sensimentDataTieba], createSensiCharts, {
  deep: true,
})

watch(followerRiseNum, newVal => {
  if (newVal > 500) {
    fetchSummaryData()
  }
})
</script>

<template>
  <div class="personal-space">
    <div v-if="error" class="error">{{ error }}</div>
    <div v-else class="content grid grid-cols-1 lg:grid-cols-4 gap-4">
      <!-- Left column: Cover Photo, Statistics, Recent Activities, and Live Status -->
      <div class="lg:col-span-3 space-y-4">
        <div class="cover-photo bg-gradient-to-r from-pink-400/80 to-indigo-400/80" :style="{}">
          <div class="overlay"></div>
          <div class="content-wrapper">
            <div class="profile-header">
              <div class="profile-picture-wrapper">
                <img :src="info.face || ''" alt="Profile Picture" class="profile-picture" />
              </div>
              <div class="profile-info">
                <h1 class="text-2xl font-bold">
                  {{ info.name || 'Loading...' }}
                </h1>
                <p class="text-sm">{{ info.sign || 'Loading...' }}</p>
                <p class="text-sm">生日: {{ info.birthday || 'Loading...' }}</p>
                <p class="text-sm">UID: {{ uid || 'Loading...' }}</p>
                <a :href="'https://space.bilibili.com/' + uid" target="_blank"
                  class="text-blue-200 text-sm hover:underline">
                  {{ '前往个人空间' }}
                </a>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mt-4">
          <!-- Left column: Statistics, Recent Activities, and Live Status -->
          <div class="lg:col-span-1 space-y-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <h2 class="text-xl font-semibold mb-2 flex items-center">
                当前数据
                <div class="relative group ml-2">
                  <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="currentColor" viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                      d="M18 10c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm-8-6a6 6 0 100 12A6 6 0 0010 4zm0 9a1 1 0 110-2 1 1 0 010 2zm0-4a1 1 0 01-1-1V7a1 1 0 112 0v1a1 1 0 01-1 1z"
                      clip-rule="evenodd"></path>
                  </svg>
                  <div
                    class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                    首行为当天数据，次行为环比数据
                  </div>
                </div>
              </h2>
              <div class="grid grid-cols-2 gap-4">
                <div class="text-center">
                  <div class="flex flex-col items-center">
                    <div class="flex items-center">
                      <p class="text-2xl font-bold">
                        {{
                          followerCount
                            ? followerCount.toLocaleString()
                            : 'Loading...'
                        }}
                      </p>
                      <p :class="followerRiseColor" class="text-sm ml-2">
                        {{ followerRiseArrow }} {{ followerRiseNum }}
                      </p>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      粉丝数
                    </p>
                    <div class="flex items-center mt-1">
                      <p :class="monthsFRColor" class="text-sm font-semibold">
                        {{ monthsFRArrow }} {{ monthsFR }}
                      </p>
                      <p class="text-xs text-gray-500 dark:text-gray-400 ml-1">
                        与上月同比
                      </p>
                    </div>
                  </div>
                </div>
                <div class="text-center">
                  <div class="flex flex-col items-center">
                    <div class="flex items-center">
                      <p class="text-2xl font-bold">
                        {{
                          dahanghai ? dahanghai.toLocaleString() : 'Loading...'
                        }}
                      </p>
                      <p :class="dahanghaiRiseColor" class="text-sm ml-2">
                        {{ dahanghaiRiseArrow }} {{ dahanghaiRiseNum }}
                      </p>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      大航海数
                    </p>
                    <div class="flex items-center mt-1">
                      <p :class="monthsFRColor" class="text-sm font-semibold">
                        {{ monthsDRArrow }} {{ monthsDR }}
                      </p>
                      <p class="text-xs text-gray-500 dark:text-gray-400 ml-1">
                        与上月同比
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4 mt-2">
                <div class="text-center">
                  <div class="flex flex-col items-center">
                    <div class="flex items-center">
                      <p :class="seasonsFRColor" class="text-sm font-semibold ml-2">
                        {{ seasonsFRArrow }} {{ seasonsFR }} /
                      </p>
                      <p :class="yearFRColor" class="text-sm font-semibold ml-2">
                        {{ yearFRArrow }} {{ yearFR }}
                      </p>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      季度/年度环比
                    </p>
                  </div>
                </div>
                <div class="text-center">
                  <div class="flex flex-col items-center">
                    <div class="flex items-center">
                      <p :class="seasonsDRColor" class="text-sm font-semibold ml-2">
                        {{ seasonsDRArrow }} {{ seasonsDR }} /
                      </p>
                      <p :class="yearDRColor" class="text-sm font-semibold ml-2">
                        {{ yearDRArrow }} {{ yearDR }}
                      </p>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      季度/年度环比
                    </p>
                  </div>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-4 mt-2">
                <div class="text-center">
                  <div class="flex flex-col items-center">
                    <div class="flex items-center">
                      <p class="text-lg font-semibold ml-2">
                        {{ formattedCStat }}
                      </p>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      视频总播放量
                    </p>
                  </div>
                </div>
                <!-- 近一周视频平均播放量 -->
                <div class="text-center">
                  <div class="flex flex-col items-center">
                    <div class="flex items-center">
                      <p class="text-lg font-semibold ml-2">
                        {{ averageWeeklyVideoViews }}
                      </p>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                      近一周视频平均播放量
                    </p>
                  </div>
                </div>
                <div class="text-center">
                  <div class="flex flex-col items-center">
                    <div class="flex items-center">
                      <p :class="followerRRColor" class="text-lg font-semibold ml-2">
                        {{ followerRR }}
                      </p>
                    </div>
                    <div class="flex items-center">
                      <p class="text-sm text-gray-600 dark:text-gray-400">
                        新增直播粉丝比例
                      </p>
                      <div class="relative group ml-2 flex items-center">
                        <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="currentColor" viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg">
                          <path fill-rule="evenodd"
                            d="M18 10c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm-8-6a6 6 0 100 12A6 6 0 0010 4zm0 9a1 1 0 110-2 1 1 0 010 2zm0-4a1 1 0 01-1-1V7a1 1 0 112 0v1a1 1 0 01-1 1z"
                            clip-rule="evenodd"></path>
                        </svg>
                        <div
                          class="absolute top-1/2 left-full transform -translate-y-1/2 ml-2 p-2 bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                          具体指一周内的新粉观看直播的比例
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <h2 class="text-xl font-semibold mb-2">最近活动</h2>
              <div v-if="!isRecentInfoLoading">
                <ul class="space-y-2">
                  <li v-for="(activity, index) in recentActivities" :key="index" class="flex items-center">
                    <span class="mr-2 text-xl">{{ activity.icon }}</span>
                    <span>{{ activity.text }}</span>
                  </li>
                </ul>
              </div>
              <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                <div class="loading-icon text-4xl mb-4">⌛</div>
                <div class="text-gray-600 dark:text-gray-400 font-medium">
                  正在收集中...
                </div>
              </div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <h2 class="text-xl font-semibold mb-2">最近在说:</h2>
              <div v-if="!isNonCriticalLoading" class="text">
                <p class="text-md font-semibold text-gray-500 mb-2">
                  <!-- {{ dynamics[2] }} -->
                  <span v-html="formatDynamicContent(dynamics[2])"></span>
                </p>
              </div>
              <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                <div class="loading-icon text-4xl mb-4">⌛</div>
                <div class="text-gray-600 dark:text-gray-400 font-medium">
                  正在抄录中...
                </div>
              </div>
            </div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <h2 class="text-xl font-semibold mb-2">直播状态</h2>
              <div class="text-center">
                <p v-if="liveInfo.if_live" class="text-lg font-semibold text-green-500 mb-2">
                  --正在营业中!--
                </p>
                <p v-else class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">
                  --下班状态中--
                </p>
                <p class="text-sm mb-2">
                  {{ liveInfo.live_title || 'No stream title available' }}
                </p>
                <img :src="liveInfo.live_cover" alt="Stream Cover" class="w-full h-auto mt-2 rounded"
                  @error="handleImageError" />
                <p class="text-sm mt-2">
                  直播间号: {{ liveInfo.live_roomid || 'N/A' }}
                </p>
                <a :href="liveInfo.live_url" target="_blank" class="text-blue-500 hover:underline mt-2 inline-block">
                  {{
                    liveInfo.if_live ? '现在去看！' : '还没开播，去小窝看看吧'
                  }}
                </a>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <h2 class="text-xl font-semibold mb-4">日增长最多视频</h2>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">选择日期</label>
                  <input type="date" v-model="selectedDate"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">展示个数</label>
                  <input type="number" v-model="videoCount" min="1" max="100"
                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
                </div>
                <div class="flex items-end">
                  <button @click="fetchTopNVideoRise"
                    class="w-auto px-4 py-2 bg-orange-300 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                    更新数据
                  </button>
                </div>
              </div>
              <div v-if="!isTopNVideoLoading" class="overflow-x-auto">
                <table class="min-w-full table-auto">
                  <thead>
                    <tr class="bg-gray-100 dark:bg-gray-700">
                      <th class="px-4 py-2 text-center">视频名称</th>
                      <!-- <th class="px-4 py-2 text-center">发布日期</th> -->
                      <th class="px-4 py-2 text-center">总播放量</th>
                      <th class="px-4 py-2 text-center">当日增长</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(video, index) in topVideos" :key="index" class="border-b dark:border-gray-600">
                      <td class="px-4 py-2 text-center">
                        <a :href="`https://www.bilibili.com/video/${video.bvid}`" target="_blank"
                          class="text-blue-500 hover:underline">
                          {{ video.title }}
                        </a>
                      </td>
                      <!-- <td class="px-4 py-2 text-center">{{ video[1] }}</td> -->
                      <td class="px-4 py-2 text-center">{{ video.view_num }}</td>
                      <td class="px-4 py-2 text-center">{{ video.view_rise_num }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                <div class="loading-icon text-4xl mb-4">⌛</div>
                <div class="text-gray-600 dark:text-gray-400 font-medium">
                  表格构建中...
                </div>
              </div>
            </div>

          </div>

          <!-- Right column: Trend Graphs -->
          <div class="lg:col-span-2 space-y-4">
            <!-- 粉丝数趋势图 -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <div class="flex justify-between items-center mb-2">
                <h2 class="text-xl font-semibold">粉丝数趋势图</h2>
                <div class="horizontal-radio-group flex space-x-4">
                  <label v-for="option in chartTypeOptions" :key="option.value"
                    class="radio-label flex items-center space-x-2">
                    <input type="radio" :value="option.value" v-model="selectedFollowerChartType"
                      class="form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out" />
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ option.text }}</span>
                  </label>
                </div>
              </div>
              <div v-if="!isLineLoading">
                <div ref="followerChartRef" style="width: 100%; height: 350px;"></div>
              </div>
              <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                <div class="loading-icon text-4xl mb-4">⌛</div>
                <div class="text-gray-600 dark:text-gray-400 font-medium">
                  加载数据中...
                </div>
              </div>
            </div>

            <!-- 大航海趋势图 -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <div class="flex justify-between items-center mb-2">
                <h2 class="text-xl font-semibold">大航海趋势图</h2>
                <div class="horizontal-radio-group flex space-x-4">
                  <label v-for="option in chartTypeOptions" :key="option.value"
                    class="radio-label flex items-center space-x-2">
                    <input type="radio" :value="option.value" v-model="selectedDahanghaiChartType"
                      class="form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out" />
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ option.text }}</span>
                  </label>
                </div>
              </div>
              <div v-if="!isLineLoading">
                <div ref="dahanghaiChartRef" style="width: 100%; height: 350px;"></div>
              </div>
              <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                <div class="loading-icon text-4xl mb-4">⌛</div>
                <div class="text-gray-600 dark:text-gray-400 font-medium">
                  加载数据中...
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <h2 class="text-xl font-semibold mb-2">近期视频播放量</h2>
                <div v-if="!isLineLoading">
                  <div ref="videoPlayChartRef" style="width: 100%; height: 300px;"></div>
                </div>
                <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                  <div class="loading-icon text-4xl mb-4">⌛</div>
                  <div class="text-gray-600 dark:text-gray-400 font-medium">
                    加载数据中...
                  </div>
                </div>
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <h2 class="text-xl font-semibold mb-2">近期视频评论数</h2>
                <div v-if="!isLineLoading">
                  <div ref="commentCountChartRef" style="width: 100%; height: 300px;"></div>
                </div>
                <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                  <div class="loading-icon text-4xl mb-4">⌛</div>
                  <div class="text-gray-600 dark:text-gray-400 font-medium">
                    加载数据中...
                  </div>
                </div>
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <h2 class="text-xl font-semibold mb-2">专栏总浏览量</h2>
                <div v-if="!isLineLoading">
                  <div ref="articleAllViewChartRef" style="width: 100%; height: 300px;"></div>
                </div>
                <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                  <div class="loading-icon text-4xl mb-4">⌛</div>
                  <div class="text-gray-600 dark:text-gray-400 font-medium">
                    加载数据中...
                  </div>
                </div>
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <h2 class="text-xl font-semibold mb-2">当月充电数</h2>
                <div v-if="!isLineLoading">
                  <div ref="elecCountChartRef" style="width: 100%; height: 300px;"></div>
                </div>
                <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                  <div class="loading-icon text-4xl mb-4">⌛</div>
                  <div class="text-gray-600 dark:text-gray-400 font-medium">
                    加载数据中...
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <div class="flex justify-between items-center mb-2">
                <h2 class="text-xl font-semibold mb-2 flex items-center">
                  舆情预警
                  <div class="relative group ml-2">
                    <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="currentColor" viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd"
                        d="M18 10c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm-8-6a6 6 0 100 12A6 6 0 0010 4zm0 9a1 1 0 110-2 1 1 0 010 2zm0-4a1 1 0 01-1-1V7a1 1 0 112 0v1a1 1 0 01-1 1z"
                        clip-rule="evenodd"></path>
                    </svg>
                    <div
                      class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                      目前设置：近三日负面情绪比例超过50%时将会预警，并显示部分负面评价
                    </div>
                  </div>
                </h2>
                <div class="horizontal-radio-group flex space-x-4">
                  <label v-for="option in options" :key="option.value" class="radio-label flex items-center space-x-2">
                    <input type="radio" :value="option.value" v-model="selectedSensimentRange"
                      @change="fetchSensimentData"
                      class="form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out" />
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ option.text }}</span>
                  </label>
                </div>
              </div>
              <div v-if="showSensimentWarning" class="text-red-500">
                <p class="mb-4">注意：最近3天的负面情绪比例较高。样例如下：</p>
                <div class="inline-block bg-gray-100 border border-gray-300 rounded-lg px-4 py-2 relative">
                  <span class="block">{{ lowSensimentComments?.comment?.join(', ') }}</span>
                  <div
                    class="absolute top-0 left-0 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-gray-100 border border-gray-300 rounded-full">
                  </div>
                </div>
              </div>
              <p v-else class="text-green-500">
                目前舆情状况良好，大的还没来。
              </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <h2 class="text-xl font-semibold mb-2">B站舆情趋势➡️</h2>
                <div v-if="!isSensiLoading">
                  <div ref="sensimentTrendChartBiliRef" style="width: 100%; height: 300px;"></div>
                </div>
                <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                  <div class="loading-icon text-4xl mb-4">⌛</div>
                  <div class="text-gray-600 dark:text-gray-400 font-medium">
                    问卷收集中...
                  </div>
                </div>
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <div v-if="!isSensiLoading">
                  <div ref="sensimentDistributionChartBiliRef" style="width: 100%; height: 300px;"></div>
                </div>
                <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                  <div class="loading-icon text-4xl mb-4">🎰</div>
                  <div class="text-gray-600 dark:text-gray-400 font-medium">
                    问卷收集中...
                  </div>
                </div>
              </div>

              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <h2 class="text-xl font-semibold mb-2">贴吧舆情趋势➡️</h2>
                <div v-if="!isSensiLoading">
                  <div ref="sensimentTrendChartTiebaRef" style="width: 100%; height: 300px;"></div>
                </div>
                <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                  <div class="loading-icon text-4xl mb-4">⌛</div>
                  <div class="text-gray-600 dark:text-gray-400 font-medium">
                    问卷收集中...
                  </div>
                </div>
              </div>
              <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <div v-if="!isSensiLoading">
                  <div ref="sensimentDistributionChartTiebaRef" style="width: 100%; height: 300px;"></div>
                </div>
                <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
                  <div class="loading-icon text-4xl mb-4">🎰</div>
                  <div class="text-gray-600 dark:text-gray-400 font-medium">
                    问卷收集中...
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
              <div class="flex justify-between items-center mb-2">
                <h2 class="text-xl font-semibold mb-2 flex items-center">
                  粉丝变化归因
                  <div class="relative group ml-2">
                    <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="currentColor" viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd"
                        d="M18 10c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm-8-6a6 6 0 100 12A6 6 0 0010 4zm0 9a1 1 0 110-2 1 1 0 010 2zm0-4a1 1 0 01-1-1V7a1 1 0 112 0v1a1 1 0 01-1 1z"
                        clip-rule="evenodd"></path>
                    </svg>
                    <div
                      class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                      内容由大模型生成，通过近期活动与活跃话题等总结生成
                    </div>
                  </div>
                </h2>
                <div v-if="followerRiseNum">
                  <button @click="fetchSummaryData" class="bg-blue-500 text-white px-4 py-2 rounded">
                    获取归因数据
                  </button>
                </div>
              </div>
              <div v-if="followerRiseNum">
                <p v-if="summariseRiseReason" v-html="formattedSummariseRiseReason"></p>
                <div v-else>粉丝变化数量不足500，归因价值不高</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right column: Recent Relationships, Fan Medal Ranking -->
      <div class="lg:col-span-1 space-y-4">
        <!-- New section for Recent Relationships -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
          <h2 class="text-xl font-semibold mb-2 flex items-center">
            最近联动（100days）
            <div class="relative group ml-2">
              <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="currentColor" viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd"
                  d="M18 10c0 4.418-3.582 8-8 8s-8-3.582-8-8 3.582-8 8-8 8 3.582 8 8zm-8-6a6 6 0 100 12A6 6 0 0010 4zm0 9a1 1 0 110-2 1 1 0 010 2zm0-4a1 1 0 01-1-1V7a1 1 0 112 0v1a1 1 0 01-1 1z"
                  clip-rule="evenodd"></path>
              </svg>
              <div
                class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                内容由大模型生成，仅供参考
              </div>
            </div>
          </h2>
          <div v-if="!isExtraLoading">
            <ul class="space-y-2">
              <template v-for="(relationship, index) in recentRelationships" :key="index">
                <li v-if="showAllRelationships || index < 10" class="flex items-start">
                  <span class="mr-2 text-xl">{{ getRandomEmoji() }}</span>
                  <span>{{ relationship }}</span>
                </li>
              </template>
            </ul>
            <div v-if="recentRelationships.length > 10" class="text-center mt-4">
              <button @click="showAllRelationships = !showAllRelationships"
                class="text-blue-500 hover:underline focus:outline-none">
                {{ showAllRelationships ? '折叠' : '展开' }}
              </button>
            </div>
          </div>
          <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
            <div class="loading-icon text-4xl mb-4">🛫</div>
            <div class="text-gray-600 dark:text-gray-400 font-medium">
              正在召集朋友中...
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
          <h2 class="text-xl font-semibold mb-4">富哥们儿榜单</h2>
          <div v-if="!isExtraLoading" class="overflow-x-auto">
            <table class="min-w-full table-auto">
              <thead>
                <tr class="bg-gray-100 dark:bg-gray-700">
                  <th class="px-4 py-2 text-center">排名</th>
                  <th class="px-4 py-2 text-center">头像</th>
                  <th class="px-4 py-2 text-center">主页</th>
                  <th class="px-4 py-2 text-center">等级</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(fan, index) in fanMedalRanking" :key="fan.uid" class="border-b dark:border-gray-600">
                  <td class="px-4 py-2 text-center">{{ fan.rank }}</td>
                  <td class="px-4 py-2 text-center flex justify-center">
                      <img :src="fan.face" :alt="fan.uname" class="w-8 h-8 rounded-full" />
                  </td>
                  <td class="px-4 py-2 text-center">
                    <a :href="`https://space.bilibili.com/${fan.uid}`" target="_blank"
                      class="text-blue-500 hover:underline">
                      {{ fan.uname }}
                    </a>
                  </td>
                  <td class="px-4 py-2 text-center">{{ fan.level }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
            <div class="loading-icon text-4xl mb-4">🔥</div>
            <div class="text-gray-600 dark:text-gray-400 font-medium">
              富哥们赶来中...
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
          <h2 class="text-xl font-semibold mb-2">观众在说:</h2>
          <div class="text">
            <img v-if="wordCloudExists" :src="`/wordcloud/${selectedVTuber}.png`"
              :alt="`${selectedVTuber} Word Cloud`" class="w-full h-auto mt-2 rounded"
              @error="handleWordCloudError" />
            <p v-else class="text-md font-semibold text-gray-500 mb-2">
              没有生成的词云捏，快去评论分析生成一张吧
            </p>
          </div>
        </div>

        <div class="space-y-6">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">起始日期</label>
                <input type="date" v-model="startDate"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">结束日期</label>
                <input type="date" v-model="endDate"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">榜单人数</label>
                <input type="number" v-model="topN" min="1" max="100"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
              </div>
              <!-- <div class="flex items-end">
                <button @click="fetchData"
                  class="w-auto px-4 py-2 bg-orange-300 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                  更新数据
                </button>
              </div> -->
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">❤️ 最受爱戴(评论被赞最多)</h2>
            <button @click="
              showTopLikeCommentors = !showTopLikeCommentors;
            if (showTopLikeCommentors) fetchTopCommentorsData();
            " class="w-auto px-4 py-2 bg-orange-300 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
              {{ showTopLikeCommentors ? '折叠' : '更新数据' }}
            </button>
          </div>
          <div v-if="showTopLikeCommentors">
            <div v-if="!isCommentorsLoading" class="overflow-x-auto">
              <table class="min-w-full table-auto">
                <thead>
                  <tr class="bg-gray-100 dark:bg-gray-700">
                    <th class="px-4 py-2 text-center">排名</th>
                    <th class="px-4 py-2 text-center">头像</th>
                    <th class="px-4 py-2 text-center">主页</th>
                    <th class="px-4 py-2 text-center">评论被赞数</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(fan, index) in topLikeCommentors" :key="fan.uid" class="border-b dark:border-gray-600">
                    <td class="px-4 py-2 text-center">{{ index + 1 }}</td>
                    <td class="px-4 py-2 text-center flex justify-center">
                      <img :src="fan.face" :alt="fan.name" class="w-8 h-8 rounded-full" />
                    </td>
                    <td class="px-4 py-2 text-center">
                      <a :href="`https://space.bilibili.com/${fan.uid}`" target="_blank"
                        class="text-blue-500 hover:underline">
                        {{ fan.name }}
                      </a>
                    </td>
                    <td class="px-4 py-2 text-center">{{ fan.likeNum }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
              <div class="loading-icon text-4xl mb-4">🫣</div>
              <div class="text-gray-600 dark:text-gray-400 font-medium">
                榜单构建中...
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">🤖 最具匠人精神（评论次数最多）</h2>
            <button @click="
              showTopAppealCommentors = !showTopAppealCommentors;
            if (showTopAppealCommentors) fetchTopCommentorsData();
            " class="w-auto px-4 py-2 bg-orange-300 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
              {{ showTopAppealCommentors ? '折叠' : '更新数据' }}
            </button>
          </div>
          <div v-if="showTopAppealCommentors">
            <div v-if="!isCommentorsLoading" class="overflow-x-auto">
              <table class="min-w-full table-auto">
                <thead>
                  <tr class="bg-gray-100 dark:bg-gray-700">
                    <th class="px-4 py-2 text-center">排名</th>
                    <th class="px-4 py-2 text-center">头像</th>
                    <th class="px-4 py-2 text-center">主页</th>
                    <th class="px-4 py-2 text-center">评论总数</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(fan, index) in topAppealCommentors" :key="fan.uid" class="border-b dark:border-gray-600">
                    <td class="px-4 py-2 text-center">{{ index + 1 }}</td>
                    <td class="px-4 py-2 text-center flex justify-center">
                      <img :src="fan.face" :alt="fan.name" class="w-8 h-8 rounded-full" />
                    </td>
                    <td class="px-4 py-2 text-center">
                      <a :href="`https://space.bilibili.com/${fan.uid}`" target="_blank"
                        class="text-blue-500 hover:underline">
                        {{ fan.name }}
                      </a>
                    </td>
                    <td class="px-4 py-2 text-center">{{ fan.appealNum }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
              <div class="loading-icon text-4xl mb-4">🫨</div>
              <div class="text-gray-600 dark:text-gray-400 font-medium">
                榜单构建中...
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold">👀 最能一呼百应（评论被回最多）</h2>
            <button @click="
              showTopReplyCommentors = !showTopReplyCommentors;
            if (showTopReplyCommentors) fetchTopCommentorsData();
            " class="w-auto px-4 py-2 bg-orange-300 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
              {{ showTopReplyCommentors ? '折叠' : '更新数据' }}
            </button>
          </div>
          <div v-if="showTopReplyCommentors">
            <div v-if="!isCommentorsLoading" class="overflow-x-auto">
              <table class="min-w-full table-auto">
                <thead>
                  <tr class="bg-gray-100 dark:bg-gray-700">
                    <th class="px-4 py-2 text-center">排名</th>
                    <th class="px-4 py-2 text-center">头像</th>
                    <th class="px-4 py-2 text-center">姓名</th>
                    <th class="px-4 py-2 text-center">评论被回量</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(fan, index) in topReplyCommentors" :key="fan.uid" class="border-b dark:border-gray-600">
                    <td class="px-4 py-2 text-center">{{ index + 1 }}</td>
                    <td class="px-4 py-2 text-center flex justify-center">
                      <img :src="fan.face" :alt="fan.name" class="w-8 h-8 rounded-full" />
                    </td>
                    <td class="px-4 py-2 text-center">
                      <a :href="`https://space.bilibili.com/${fan.uid}`" target="_blank"
                        class="text-blue-500 hover:underline">
                        {{ fan.name }}
                      </a>
                    </td>
                    <td class="px-4 py-2 text-center">{{ fan.rcountSum }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="flex flex-col items-center justify-center py-8 animate-pulse">
              <div class="loading-icon text-4xl mb-4">😶‍🌫️</div>
              <div class="text-gray-600 dark:text-gray-400 font-medium">
                榜单构建中...
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.personal-space {
  /* max-width: 100rem; */
  /* margin-left: auto;
  margin-right: auto; */
  width: 100%;
  margin: 0;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.cover-photo {
  height: 14rem;
  background-size: cover;
  background-position: center;
  position: relative;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  overflow: hidden;
}

.overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.content-wrapper {
  position: relative;
  z-index: 10;
  padding: 1.5rem;
}

.profile-header {
  display: flex;
  align-items: flex-end;
}

.profile-picture-wrapper {
  position: relative;
}

.profile-picture {
  width: 8rem;
  height: 8rem;
  border-radius: 9999px;
  border: 4px solid white;
}

.profile-info {
  margin-left: 1.5rem;
  color: white;
}

/* New styles for the recent relationships section */
.recent-relationships {
  margin-bottom: 1rem;
}

.recent-relationships ul {
  list-style-type: none;
  padding: 0;
}

.recent-relationships li {
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.recent-relationships .icon {
  margin-right: 0.5rem;
  font-size: 1.2em;
}

li {
  display: flex;
  align-items: center;
}

.loading,
.error {
  text-align: center;
  padding-top: 2rem;
  padding-bottom: 2rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
