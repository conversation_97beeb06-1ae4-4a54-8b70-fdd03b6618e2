import json
import os
from contextlib import asynccontextmanager
from datetime import datetime
from functools import lru_cache
from typing import Optional, Generic, TypeVar, Any, List

from fastapi import Fast<PERSON>I, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel, EmailStr, Field

import backend.utils as U
from server.creator_info_server import CreatorInfoServer
from backend.tools.query_board_data import (
    collect_all_dahanghai_and_follower_rise_num_stuff,
    collect_all_live_status_stuff,
    collect_target_flow_with_target_vtuber,
    collect_target_flow_with_target_vtuber_by_period,
    collect_whole_flow_from_all_vtubers,
    collect_whole_info_from_all_vtubers,
)
from backend.tools.query_live_info import (
    query_live_info_with_room_id,
    query_minutes_live_info_with_live_id,
    query_now_live_info_by_room,
    query_whole_live_info_with_live_id,
)
from backend.tools.query_user_data import (
    calculate_dahanghai_rate_by_mid,
    calculate_follower_rate_by_mid,
    query_all_dynamics_comments_by_mid,
    query_all_video_comments_by_mid,
    query_all_video_list_by_mid,
    query_comment_wordcloud,
    query_current_dahanghai_change_num,
    query_current_dynamics,
    query_current_follower_change_num,
    query_current_stat_by_mid,
    query_current_video_day_views,
    query_dahanghai_list_by_uid_and_datetime,
    query_fans_medal_rank_by_datetime,
    query_followers_list,
    query_followers_review_list,
    query_followers_review_rate,
    query_latest_fans_medal_rank,
    query_now_user_dahanghai_num_by_mid,
    query_now_user_follower_num_by_mid,
    query_peroid_user_all_stat_by_uid_and_time,
    query_recent_comments_sentiment_value,
    query_recent_info,
    query_recent_relationships,
    query_rise_reason_from_ai_gen_table_by_date,
    query_single_video_rencent_day_data,
    query_single_video_target_day_data,
    query_tieba_summaries_from_ai_gen_table_by_date,
    query_tieba_threads,
    query_tieba_whole,
    query_top_n_comments,
    query_top_n_comments_user,
    query_top_n_dynamics,
    query_top_n_videos,
    query_top_n_view_rise_day_data_period,
    query_top_n_view_rise_target_day_data,
    query_user_dynamics_by_mid,
    query_user_info_by_mid,
    query_video_ai_conclusion_by_bvid,
    query_whole_dahanghai_num_by_mid_and_recent,
    query_whole_user_all_stat_by_uid_and_recent,
    query_whole_user_follower_num_by_mid_and_recent,
)
from backend.tools.query_creator_data import (
    # 时间查询函数
    query_overview_stat_by_date_range,
    query_overview_stat_by_date,
    query_attention_analyze_by_date_range,
    query_attention_analyze_by_date,
    query_archive_analyze_by_date_range,
    query_archive_analyze_by_date,
    query_fan_graph_by_date_range,
    query_fan_graph_by_date,
    query_fan_overview_by_date_range,
    query_fan_overview_by_date,
    query_video_compare_by_date_range,
    query_video_compare_by_date,
    query_video_pandect_by_date_range,
    query_video_pandect_by_date,
    query_video_survey_by_date_range,
    query_video_survey_by_date,
    query_video_source_by_date_range,
    query_video_source_by_date,
    query_video_view_data_by_date_range,
    query_video_view_data_by_date,
)
from backend.tools.query_web_user_info import (
    check_web_user_info_table,
    delete_web_user_info,
    get_web_user_by_email,
    login_web_user_info,
    modify_web_user_password,
    register_web_user_info,
)
from backend.utils.db_pool import initialize_pool, shutdown_pool
from const import PROJECT_ROOT
from logger import logger

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Generic type for response data
T = TypeVar('T')

class ApiResponse(BaseModel, Generic[T]):
    """Standard API response format"""
    code: int = Field(description="Response code")
    message: str = Field(description="Response message")
    data: Optional[T] = Field(default=None, description="Response data")

class ErrorDetail(BaseModel):
    """Error detail information"""
    field: Optional[str] = Field(default=None, description="Error field")
    message: str = Field(description="Error message")
    code: Optional[str] = Field(default=None, description="Error code")

class ApiError(BaseModel):
    """Standard API error response format"""
    code: int = Field(description="Error code")
    message: str = Field(description="Error message")
    details: Optional[List[ErrorDetail]] = Field(default=None, description="Error details")

class PaginatedResponse(BaseModel, Generic[T]):
    """Paginated response format"""
    data: List[T] = Field(description="Response data list")
    total: int = Field(description="Total count")
    page: int = Field(description="Current page")
    page_size: int = Field(description="Page size")
    has_next: bool = Field(description="Has next page")

class WebUserBase(BaseModel):
    email: EmailStr


class WebUserCreate(WebUserBase):
    username: str
    password: str
    phone: Optional[str] = None


class WebUserLogin(BaseModel):
    email: EmailStr
    password: str


class WebUserPasswordUpdate(WebUserBase):
    new_password: str


class WebUserDelete(WebUserBase):
    pass


class Token(BaseModel):
    access_token: str
    token_type: str


class UserInDB(WebUserBase):
    username: str
    password_hash: str
    phone: Optional[str] = None


def get_password_hash(password):  # Still used for registration in this file
    return pwd_context.hash(password)


def create_success_response(data: Any = None, message: str = "Success", code: int = 200) -> dict:
    """Create a standard success response"""
    return {
        "code": code,
        "message": message,
        "data": data
    }

def create_error_response(message: str, code: int = 400, details: Optional[List[dict]] = None) -> dict:
    """Create a standard error response"""
    response = {
        "code": code,
        "message": message
    }
    if details:
        response["details"] = details
    return response

def create_paginated_response(data: List[Any], total: int, page: int = 1, page_size: int = 20) -> dict:
    """Create a paginated response"""
    has_next = (page * page_size) < total
    return {
        "data": data,
        "total": total,
        "page": page,
        "page_size": page_size,
        "has_next": has_next
    }


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Application startup
    logger.info("Application startup: Initializing database pool.")
    await initialize_pool()
    logger.info("Database pool initialized.")

    logger.info(
        "Application startup: Checking and creating web_user_info table if not exists."
    )
    await (
        check_web_user_info_table()
    )  # This function will be updated later to use the async pool
    logger.info("Web_user_info table check complete.")

    # Initialize CreatorInfoServer
    logger.info("Application startup: Initializing CreatorInfoServer.")
    app.state.creator_info_server = CreatorInfoServer()
    await app.state.creator_info_server.initialize_async()
    logger.info("CreatorInfoServer initialized.")

    yield

    # Application shutdown
    logger.info("Application shutdown: Closing database pool.")
    await shutdown_pool()
    logger.info("Database pool closed.")


app = FastAPI(lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@lru_cache(maxsize=None)
def get_user_mid(char):
    char_zh = U.get_zh_role_name(char)
    users_file_path = f"{PROJECT_ROOT}/config/target.txt"
    with open(users_file_path, "r", encoding="utf-8") as file:
        lines = file.readlines()
    for line in lines:
        parts = line.strip().split(", ")
        if parts[1] == char_zh:
            return parts[0]
    raise Exception(f"Character {char_zh} not found in target.txt")


def get_user_room_id(char):
    char_zh = U.get_zh_role_name(char)
    if os.path.exists(f"{PROJECT_ROOT}/config/room.txt"):
        try:
            with open(f"{PROJECT_ROOT}/config/room.txt", "r", encoding="utf-8") as file:
                for line in file:
                    if char_zh in line:
                        liveid = line.split(",")[1].strip()
                        return liveid
        except IOError as e:
            logger.warning(f"Failed to read room.txt: {e}")


def get_creator_info_server():
    """获取 CreatorInfoServer 实例"""
    return app.state.creator_info_server


@app.get("/")
async def read_root():
    return {"It's a demo web for info collection."}


# --- Web User Authentication API Endpoints ---


# RESTful: POST /web/users (create user resource)
@app.post("/web/users", status_code=status.HTTP_201_CREATED)
async def create_user(user_in: WebUserCreate):
    """
    Create a new web user.
    - **email**: User's email (must be unique).
    - **username**: User's chosen username.
    - **password**: User's password.
    - **phone**: User's phone number (optional).
    """
    logger.info(f"Attempting to create user with email: {user_in.email}")
    password_hash = get_password_hash(user_in.password)
    try:
        user_id = await register_web_user_info(
            username=user_in.username,
            password_hash=password_hash,
            email=user_in.email,
            phone=user_in.phone,
        )
        logger.info(
            f"User {user_in.username} created successfully with ID: {user_id}"
        )

        user_data = {
            "id": user_id,
            "email": user_in.email,
            "username": user_in.username,
            "phone": user_in.phone,
            "created_at": datetime.now().isoformat()
        }

        return create_success_response(
            data=user_data,
            message="User created successfully",
            code=201
        )
    except ValueError as e:
        logger.error(f"User creation failed for email {user_in.email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(str(e), 400)
        )
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during user creation for email {user_in.email}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Internal server error during user creation", 500)
        )

# Backward compatibility: keep old endpoint
@app.post(
    "/web/users/register", response_model=UserInDB, status_code=status.HTTP_201_CREATED
)
async def register_user(user_in: WebUserCreate):
    """
    [DEPRECATED] Register a new web user. Use POST /web/users instead.
    - **email**: User's email (must be unique).
    - **username**: User's chosen username.
    - **password**: User's password.
    - **phone**: User's phone number (optional).
    """
    logger.warning("Using deprecated endpoint /web/users/register. Use POST /web/users instead.")
    logger.info(f"Attempting to register user with email: {user_in.email}")
    password_hash = get_password_hash(user_in.password)
    try:
        user_id = await register_web_user_info(
            username=user_in.username,
            password_hash=password_hash,
            email=user_in.email,
            phone=user_in.phone,
        )
        logger.info(
            f"User {user_in.username} registered successfully with ID: {user_id}"
        )
        return UserInDB(
            email=user_in.email,
            username=user_in.username,
            password_hash=password_hash,
            phone=user_in.phone,
        )
    except ValueError as e:
        logger.error(f"Registration failed for email {user_in.email}: {e}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during registration for email {user_in.email}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during registration.",
        )


# RESTful: POST /web/auth/sessions (create authentication session)
@app.post("/web/auth/sessions")
async def create_auth_session(form_data: WebUserLogin):
    """
    Create authentication session and return a token.
    - **email**: User's email.
    - **password**: User's password.
    """
    logger.info(f"Authentication attempt for email: {form_data.email}")
    user_data_dict = await login_web_user_info(
        email=form_data.email, password=form_data.password
    )

    if not user_data_dict:
        logger.warning(
            f"Authentication failed for email {form_data.email}: Incorrect email or password."
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=create_error_response("Incorrect email or password", 401)
        )

    user_id = user_data_dict.get("user_id")
    username = user_data_dict.get("username")

    # For simplicity, returning a dummy token. Real implementation would use JWT.
    access_token = f"fake-jwt-token-for-{username}-{user_id}"

    token_data = {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user_id,
            "username": username,
            "email": form_data.email
        }
    }

    logger.info(f"User {username} (ID: {user_id}) authenticated successfully.")
    return create_success_response(
        data=token_data,
        message="Authentication successful"
    )

# Backward compatibility: keep old endpoint
@app.post("/web/users/login", response_model=Token)
async def login_for_access_token(form_data: WebUserLogin):
    """
    [DEPRECATED] Authenticate web user and return a token. Use POST /web/auth/sessions instead.
    - **email**: User's email.
    - **password**: User's password.
    """
    logger.warning("Using deprecated endpoint /web/users/login. Use POST /web/auth/sessions instead.")
    logger.info(f"Login attempt for email: {form_data.email}")
    user_data_dict = await login_web_user_info(
        email=form_data.email, password=form_data.password
    )

    if not user_data_dict:
        logger.warning(
            f"Login failed for email {form_data.email}: Incorrect email or password."
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    user_id = user_data_dict.get("user_id")
    username = user_data_dict.get("username")

    access_token = f"fake-jwt-token-for-{username}-{user_id}"
    logger.info(f"User {username} (ID: {user_id}) logged in successfully.")
    return {"access_token": access_token, "token_type": "bearer"}


# RESTful: PATCH /web/users/{user_id}/password (partial update of user resource)
@app.patch("/web/users/password", status_code=status.HTTP_200_OK)
async def update_user_password(update_data: WebUserPasswordUpdate):
    """
    Update web user's password.
    - **email**: User's email.
    - **new_password**: User's new password.
    """
    logger.info(f"Attempting to update password for email: {update_data.email}")

    user_data_dict = await get_web_user_by_email(email=update_data.email)
    if not user_data_dict:
        logger.warning(
            f"Password update failed: User not found for email {update_data.email}"
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=create_error_response("User not found", 404)
        )

    new_hashed_password = get_password_hash(update_data.new_password)
    try:
        success = await modify_web_user_password(
            email=update_data.email,
            new_password_hash=new_hashed_password,
        )
        if success:
            logger.info(f"Password updated successfully for email: {update_data.email}")
            return create_success_response(
                message="Password updated successfully"
            )
        else:
            logger.error(
                f"Password update failed for email {update_data.email}, modify_web_user_password returned false."
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=create_error_response("Failed to update password", 500)
            )
    except ValueError as e:
        logger.error(f"Password update failed for email {update_data.email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=create_error_response(str(e), 404)
        )
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during password update for {update_data.email}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Internal server error during password update", 500)
        )

# Backward compatibility: keep old endpoint
@app.put("/web/users/password", status_code=status.HTTP_200_OK)
async def update_user_password_old(update_data: WebUserPasswordUpdate):
    """
    [DEPRECATED] Update web user's password. Use PATCH /web/users/password instead.
    - **email**: User's email.
    - **new_password**: User's new password.
    """
    logger.warning("Using deprecated endpoint PUT /web/users/password. Use PATCH /web/users/password instead.")
    try:
        return await update_user_password(update_data)
    except HTTPException as e:
        # For backward compatibility, convert the new format back to old format
        if e.status_code == 404:
            raise HTTPException(status_code=404, detail="User not found")
        elif e.status_code == 500:
            raise HTTPException(status_code=500, detail="Internal server error during password update")
        else:
            raise e


# RESTful: DELETE /web/users (delete user resource by email in body)
@app.delete("/web/users", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(user_del: WebUserDelete):
    """
    Delete a web user.
    - **email**: User's email.
    """
    logger.info(f"Attempting to delete user with email: {user_del.email}")

    try:
        success = await delete_web_user_info(email=user_del.email)
        if success:
            logger.info(f"User with email {user_del.email} deleted successfully.")
            # Return 204 No Content for successful deletion
            return None
        else:
            logger.warning(
                f"Deletion failed for email {user_del.email}: User not found or DB error."
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("User not found or could not be deleted", 404)
            )
    except ValueError as e:
        logger.error(f"Deletion failed for email {user_del.email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=create_error_response(str(e), 404)
        )
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during deletion for email {user_del.email}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Internal server error during deletion", 500)
        )

# Backward compatibility: keep old endpoint
@app.delete("/web/users/delete", status_code=status.HTTP_200_OK)
async def remove_user(user_del: WebUserDelete):
    """
    [DEPRECATED] Delete a web user. Use DELETE /web/users instead.
    - **email**: User's email.
    """
    logger.warning("Using deprecated endpoint DELETE /web/users/delete. Use DELETE /web/users instead.")
    logger.info(f"Attempting to delete user with email: {user_del.email}")

    try:
        success = await delete_web_user_info(email=user_del.email)
        if success:
            logger.info(f"User with email {user_del.email} deleted successfully.")
            return {"message": "User deleted successfully"}
        else:
            logger.warning(
                f"Deletion failed for email {user_del.email}: User not found or DB error."
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found or could not be deleted.",
            )
    except ValueError as e:
        logger.error(f"Deletion failed for email {user_del.email}: {e}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during deletion for email {user_del.email}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during deletion.",
        )


# RESTful: GET /vtubers/{name}/mid (get specific vtuber's mid)
@app.get("/vtubers/{vtuber_name}/mid")
async def get_vtuber_mid(vtuber_name: str):
    """
    Get VTuber's UID by name.

    Args:
        vtuber_name: VTuber name

    Returns:
        Standard API response with VTuber UID
    """
    try:
        mid = get_user_mid(vtuber_name)
        return create_success_response(
            data={"mid": mid, "vtuber_name": vtuber_name},
            message="VTuber UID retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get UID for VTuber {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=create_error_response(f"VTuber {vtuber_name} not found", 404)
        )

# Backward compatibility: keep old endpoint
@app.get("/basic/mid")
async def read_basic_mid(vtuber: Optional[str] = "星瞳"):
    """
    [DEPRECATED] Get VTuber UID. Use GET /vtubers/{name}/mid instead.
    """
    logger.warning("Using deprecated endpoint /basic/mid. Use GET /vtubers/{name}/mid instead.")
    try:
        return get_user_mid(vtuber)
    except Exception as e:
        return f"{e}"


# RESTful: GET /vtubers/{name}/followers/current (get current follower count)
@app.get("/vtubers/{vtuber_name}/followers/current")
async def get_vtuber_current_followers(vtuber_name: str):
    """
    Get VTuber's current follower count.

    Args:
        vtuber_name: VTuber name

    Returns:
        Standard API response with current follower count
    """
    try:
        mid = get_user_mid(vtuber_name)
        follower_count = await query_now_user_follower_num_by_mid(mid)
        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "current_followers": follower_count,
                "timestamp": datetime.now().isoformat()
            },
            message="Current follower count retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get current followers for VTuber {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve follower count", 500)
        )

# Backward compatibility: keep old endpoint
@app.get("/basic/follower/current")
async def read_basic_current_follower(vtuber: Optional[str] = "星瞳"):
    """
    [DEPRECATED] Get current follower count. Use GET /vtubers/{name}/followers/current instead.
    """
    logger.warning("Using deprecated endpoint /basic/follower/current. Use GET /vtubers/{name}/followers/current instead.")
    mid = get_user_mid(vtuber)
    info = await query_now_user_follower_num_by_mid(mid)
    return info


# RESTful: GET /vtubers/{name}/followers/history (get follower history)
@app.get("/vtubers/{vtuber_name}/followers/history")
async def get_vtuber_follower_history(vtuber_name: str, recent: int = -1, limit: int = 1000, offset: int = 0):
    """
    Get VTuber's follower count history.

    Args:
        vtuber_name: VTuber name
        recent: Recent time period (-1 for all)
        limit: Maximum number of records to return
        offset: Number of records to skip

    Returns:
        Standard API response with follower history data
    """
    try:
        mid = get_user_mid(vtuber_name)
        history_data = await query_whole_user_follower_num_by_mid_and_recent(mid, recent)

        # Apply pagination
        total = len(history_data) if history_data else 0
        paginated_data = history_data[offset:offset + limit] if history_data else []

        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "history": paginated_data,
                "pagination": {
                    "total": total,
                    "limit": limit,
                    "offset": offset,
                    "has_more": (offset + limit) < total
                }
            },
            message="Follower history retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get follower history for VTuber {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve follower history", 500)
        )

# Backward compatibility: keep old endpoint
@app.get("/basic/follower/all")
async def read_basic_all_follower(vtuber: Optional[str] = "星瞳", recent: int = -1):
    """
    [DEPRECATED] Get follower history. Use GET /vtubers/{name}/followers/history instead.
    """
    logger.warning("Using deprecated endpoint /basic/follower/all. Use GET /vtubers/{name}/followers/history instead.")
    mid = get_user_mid(vtuber)
    info = await query_whole_user_follower_num_by_mid_and_recent(mid, recent)
    return info


# RESTful: GET /vtubers/{name}/dahanghai/current (get current dahanghai count)
@app.get("/vtubers/{vtuber_name}/dahanghai/current")
async def get_vtuber_current_dahanghai(vtuber_name: str):
    """
    Get VTuber's current dahanghai (guard) count.

    Args:
        vtuber_name: VTuber name

    Returns:
        Standard API response with current dahanghai count
    """
    try:
        mid = get_user_mid(vtuber_name)
        dahanghai_count = await query_now_user_dahanghai_num_by_mid(mid)
        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "current_dahanghai": dahanghai_count,
                "timestamp": datetime.now().isoformat()
            },
            message="Current dahanghai count retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get current dahanghai for VTuber {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve dahanghai count", 500)
        )

# Backward compatibility: keep old endpoint
@app.get("/basic/dahanghai/current")
async def read_basic_current_dahanghai(vtuber: Optional[str] = "星瞳"):
    """
    [DEPRECATED] Get current dahanghai count. Use GET /vtubers/{name}/dahanghai/current instead.
    """
    logger.warning("Using deprecated endpoint /basic/dahanghai/current. Use GET /vtubers/{name}/dahanghai/current instead.")
    mid = get_user_mid(vtuber)
    info = await query_now_user_dahanghai_num_by_mid(mid)
    return info


# RESTful: GET /vtubers/{name}/dahanghai/history (get dahanghai history)
@app.get("/vtubers/{vtuber_name}/dahanghai/history")
async def get_vtuber_dahanghai_history(vtuber_name: str, recent: int = -1, limit: int = 1000, offset: int = 0):
    """
    Get VTuber's dahanghai (guard) count history.

    Args:
        vtuber_name: VTuber name
        recent: Recent time period (-1 for all)
        limit: Maximum number of records to return
        offset: Number of records to skip

    Returns:
        Standard API response with dahanghai history data
    """
    try:
        mid = get_user_mid(vtuber_name)
        history_data = await query_whole_dahanghai_num_by_mid_and_recent(mid, recent)

        # Apply pagination
        total = len(history_data) if history_data else 0
        paginated_data = history_data[offset:offset + limit] if history_data else []

        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "history": paginated_data,
                "pagination": {
                    "total": total,
                    "limit": limit,
                    "offset": offset,
                    "has_more": (offset + limit) < total
                }
            },
            message="Dahanghai history retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get dahanghai history for VTuber {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve dahanghai history", 500)
        )

# Backward compatibility: keep old endpoint
@app.get("/basic/dahanghai/all")
async def read_basic_all_dahanghai(vtuber: Optional[str] = "星瞳", recent: int = -1):
    """
    [DEPRECATED] Get dahanghai history. Use GET /vtubers/{name}/dahanghai/history instead.
    """
    logger.warning("Using deprecated endpoint /basic/dahanghai/all. Use GET /vtubers/{name}/dahanghai/history instead.")
    mid = get_user_mid(vtuber)
    info = await query_whole_dahanghai_num_by_mid_and_recent(mid, recent)
    return info


@app.get("/basic/dahanghai/rate")  # ok2
async def read_basic_dahanghai_rate(vtuber: Optional[str] = "星瞳", recent: int = 90):
    """
    获取大航海数量环比

    Param: vtuber主播名, recent最近时间(-1表示全部)

    Url: /basic/dahanghai/rate?vtuber=%E6%98%9F%E7%9E%B3&recent=3

    Type: int

    Example: "-0.7%"
    """
    mid = get_user_mid(vtuber)
    info = await calculate_dahanghai_rate_by_mid(mid, recent)
    return info


@app.get("/basic/follower/rate")  # ok2
async def read_basic_follower_rate(vtuber: Optional[str] = "星瞳", recent: int = 90):
    """
    获取粉丝数量环比

    Param: vtuber主播名, recent最近时间(-1表示全部)

    Url: /basic/follower/rate?vtuber=%E6%98%9F%E7%9E%B3&recent=3

    Type: int

    Example: "-0.7%"
    """
    mid = get_user_mid(vtuber)
    info = await calculate_follower_rate_by_mid(mid, recent)
    return info


@app.get("/basic/stat/current")  # ok2
async def read_basic_current_stat(vtuber: Optional[str] = "星瞳"):
    """
    获取当前基础数据

    Param: vtuber主播名

    Url: /basic/stat/current?vtuber=%E6%98%9F%E7%9E%B3

    Type: [uid, name, timestamp, datetime, follower_num, dahanghai_num, video_total_num, article_total_num, likes_total_num, elec_num]

    Example: [
                26,
                "401315430",
                "星瞳",
                1747710000,
                "2025-05-20T11:00:00",
                974497,
                269,
                99663571,
                13455,
                11801146,
                1460
            ]
    """
    mid = get_user_mid(vtuber)
    info = await query_current_stat_by_mid(mid)
    return info


# RESTful: GET /vtubers/{name}/stats/period (get stats by period)
@app.get("/vtubers/{vtuber_name}/stats/period")
async def get_vtuber_stats_by_period(vtuber_name: str, start_date: str, end_date: str):
    """
    Get VTuber's statistics for a specific period.

    Args:
        vtuber_name: VTuber name
        start_date: Start date (YYYY-MM-DD format)
        end_date: End date (YYYY-MM-DD format)

    Returns:
        Standard API response with period statistics
    """
    try:
        mid = get_user_mid(vtuber_name)
        stats_data = await query_peroid_user_all_stat_by_uid_and_time(mid, start_date, end_date)

        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "period": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "statistics": stats_data
            },
            message="Period statistics retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get period stats for VTuber {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve period statistics", 500)
        )

# Backward compatibility: keep old endpoint with typo
@app.get("/basic/stat/peroid")
async def read_basic_peroid_stat(s: str, e: str, vtuber: Optional[str] = "星瞳"):
    """
    [DEPRECATED] Get period stats (note: typo in 'peroid'). Use GET /vtubers/{name}/stats/period instead.
    """
    logger.warning("Using deprecated endpoint /basic/stat/peroid (with typo). Use GET /vtubers/{name}/stats/period instead.")
    mid = get_user_mid(vtuber)
    info = await query_peroid_user_all_stat_by_uid_and_time(mid, s, e)
    return info

# Correct spelling endpoint for backward compatibility
@app.get("/basic/stat/period")
async def read_basic_period_stat(s: str, e: str, vtuber: Optional[str] = "星瞳"):
    """
    [DEPRECATED] Get period stats. Use GET /vtubers/{name}/stats/period instead.
    """
    logger.warning("Using deprecated endpoint /basic/stat/period. Use GET /vtubers/{name}/stats/period instead.")
    mid = get_user_mid(vtuber)
    info = await query_peroid_user_all_stat_by_uid_and_time(mid, s, e)
    return info


@app.get("/basic/stat/all")  # ok2
async def read_basic_all_stat(vtuber: Optional[str] = "星瞳", recent: int = -1):
    """
    获取历史基础数据

    Param: vtuber主播名, recent最近时间(-1表示全部)

    Url: /basic/stat/current?vtuber=%E6%98%9F%E7%9E%B3

    Type: List[['time', 'videoTotalNum', 'articleTotalNum', 'likesTotalNum', 'elecNum']]

    Example: [
                [
                    "2025051415",
                    99391051,
                    13427,
                    11765417,
                    1456,
                    972639,
                    276
                ],
                [
                    "2025051502",
                    99391051,
                    13427,
                    11767636,
                    1456,
                    972566,
                    276
                ],
                ...
            ]
    """
    mid = get_user_mid(vtuber)
    info = await query_whole_user_all_stat_by_uid_and_recent(mid, recent)
    return info


@app.get("/basic/medal_rank")  # ok2
async def read_basic_medal_rank(vtuber: Optional[str] = "星瞳"):
    """
    获取粉丝勋章排名前十

    Returns: List[Dict]

    // Example:

    {
        "uid": "401315430",
        "name": "星瞳",
        "liveid": "22886883",
        "datetime": "2025-05-14T00:00:00",
        "rank_list": [
            {
            "uid": 486553605,
            "face": "https://i0.hdslb.com/bfs/face/9d9a8d9e3b8926ff1aa8adf9363e9a87c2171181.jpg",
            "rank": 1,
            "color": 7996451,
            "level": 34,
            "uname": "钉钩鱼骑不动",
            "isSelf": 0,
            "special": "",
            "target_id": 401315430,
            "is_lighted": 1,
            "medal_name": "瞳星结",
            "guard_level": 2,
            "medal_color_end": 15304379,
            "medal_color_start": 7996451,
            "medal_color_border": 16771156
            },
            ...
        ]
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_latest_fans_medal_rank(mid)
    return info


@app.get("/basic/medal_rank/target")  # ok2
async def read_basic_medal_rank_target(
    target_datetime: str, vtuber: Optional[str] = "星瞳"
):
    """
    获取粉丝勋章排名

    Returns: List[Dict]

    // Example:

    {
        "uid": "401315430",
        "name": "星瞳",
        "liveid": "22886883",
        "datetime": "2025-05-14T00:00:00",
        "rank_list": [
            {
            "uid": 486553605,
            "face": "https://i0.hdslb.com/bfs/face/9d9a8d9e3b8926ff1aa8adf9363e9a87c2171181.jpg",
            "rank": 1,
            "color": 7996451,
            "level": 34,
            "uname": "钉钩鱼骑不动",
            "isSelf": 0,
            "special": "",
            "target_id": 401315430,
            "is_lighted": 1,
            "medal_name": "瞳星结",
            "guard_level": 2,
            "medal_color_end": 15304379,
            "medal_color_start": 7996451,
            "medal_color_border": 16771156
            },
    """
    mid = get_user_mid(vtuber)
    info = await query_fans_medal_rank_by_datetime(mid, target_datetime)
    return info


@app.get("/basic/info")  # ok2
async def read_basic_info(vtuber: Optional[str] = "星瞳"):
    """
    获取主播信息：姓名, 头像, 签名, 生日, 主页图

    Returns: List

    [uid, name, face, sign, birthday, top_photo, cover, room, room_img]

    // Example:

    [
        "401315430",
        "星瞳_Official",
        "https://i0.hdslb.com/bfs/face/35ce8ca063124d4dda5c211039d0eb67eae3c797.jpg",
        "时尚虚拟偶像 炫舞系列虚拟代言人 合作联系***********************",
        "10-21",
        "http://i1.hdslb.com/bfs/space/cb1c3ef50e22b6096fde67febe863494caefebad.png",
        "22886883",
        "https://live.bilibili.com/22886883?broadcast_type=0&is_room_feed=1"
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_user_info_by_mid(mid)
    return info


@app.get("/live/info")  # ok2
async def read_live_info(vtuber: Optional[str] = "星瞳"):
    """
    获取直播信息：是否直播, 直播地址, 直播标题, 直播封面, 直播房间号

    Returns: List

    [if_live, live_url, live_title, live_cover, live_roomid]

    // Example:

    [
        127,
        "22886883",
        null,
        2,
        "无",
        "【3D】下一个更乖",
        "https://i0.hdslb.com/bfs/live/new_room_cover/8ab848423b88f25c7df6f02a72e1296e1d839032.jpg",
        null,
        "虚拟主播",
        "虚拟日常",
        1747113480,
        "2025-05-13T13:18:00"
    ]
    """
    room_id = get_user_room_id(vtuber)
    info = await query_now_live_info_by_room(room_id)
    return info


@app.get("/dynamics/list")  # ok2
async def read_dynamics_list(vtuber: Optional[str] = "星瞳"):
    """
    获取主播全部动态列表（风控问题，目前仅部分，更新中）

    Returns: List[List]

    [['主播', '发布时间', '动态内容', '跳转网址', '话题', 'ID', '转发数', '评论数', '点赞数', 'comment_id', 'comment_type'], ...]

    // Example:

    [
        [
            "星瞳_Official",
            "2025-05-14 17:36:07",
            "小星星们，下周的直播日程表有一点变动，520要3D和大家见面辣~！[星瞳·动态表情包_可爱猫猫]记得留意直播安排的变化哦！mua！ [百变星瞳_亲亲]",
            "https://www.bilibili.com/opus/1066770950389760019",
            null,
            "1066770950389760019",
            1,
            158,
            1197,
            "350767500",
            11,
            4.25
        ],
        ...
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_user_dynamics_by_mid(mid)
    return info


# RESTful: GET /vtubers/{name}/dynamics (get dynamics with query parameters)
@app.get("/vtubers/{vtuber_name}/dynamics")
async def get_vtuber_dynamics(
    vtuber_name: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 10,
    sort: str = "popularity",
    offset: int = 0
):
    """
    Get VTuber's dynamics with filtering and sorting options.

    Args:
        vtuber_name: VTuber name
        start_date: Start date filter (YYYY-MM-DD format)
        end_date: End date filter (YYYY-MM-DD format)
        limit: Maximum number of dynamics to return
        sort: Sort order ('popularity', 'time', etc.)
        offset: Number of records to skip

    Returns:
        Standard API response with dynamics data
    """
    try:
        mid = get_user_mid(vtuber_name)

        if sort == "popularity" and start_date and end_date:
            # Get top dynamics by popularity
            dynamics_data = await query_top_n_dynamics(mid, start_date, end_date, limit)
        else:
            # Get all dynamics (could be extended to support other sorting)
            dynamics_data = await query_user_dynamics_by_mid(mid)
            # Apply pagination
            dynamics_data = dynamics_data[offset:offset + limit] if dynamics_data else []

        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "filters": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "sort": sort
                },
                "dynamics": dynamics_data,
                "pagination": {
                    "limit": limit,
                    "offset": offset
                }
            },
            message="Dynamics retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get dynamics for VTuber {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve dynamics", 500)
        )

# Backward compatibility: keep old endpoint
@app.get("/dynamics/top_n/")
async def read_dynamics_top_n(s: str, e: str, n: int, vtuber: Optional[str] = "星瞳"):
    """
    [DEPRECATED] Get top N dynamics. Use GET /vtubers/{name}/dynamics?sort=popularity&limit=N instead.
    """
    logger.warning("Using deprecated endpoint /dynamics/top_n/. Use GET /vtubers/{name}/dynamics?sort=popularity&limit=N instead.")
    mid = get_user_mid(vtuber)
    info = await query_top_n_dynamics(mid, s, e, n)
    return info


@app.get("/dynamics/current")  # ok2
async def read_current_dynamics(vtuber: Optional[str] = "星瞳"):
    """
    获取主播当前动态列表

    Returns: List[List]

    [['主播', '发布时间', '动态内容', '跳转网址', '话题', 'ID', '转发数', '评论数', '点赞数', 'comment_id', 'comment_type'], ...]

    // Example:

    [
        "星瞳_Official",
        "2025-05-14 17:36:07",
        "小星星们，下周的直播日程表有一点变动，520要3D和大家见面辣~！[星瞳·动态表情包_可爱猫猫]记得留意直播安排的变化哦！mua！ [百变星瞳_亲亲]",
        "https://www.bilibili.com/opus/1066770950389760019",
        null,
        "1066770950389760019",
        1,
        158,
        1197,
        "350767500",
        11
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_current_dynamics(mid)
    return info


@app.get("/video/list")  # ok2
async def read_video_list(vtuber: Optional[str] = "星瞳"):
    """
    获取主播全部视频列表

    Returns: List[List]

    [['bvid', '视频名称', '描述', "封面", '发布时间', '播放数', '评论数', 'aid', '时长']]

    // Example:

    [
        [
            "BV1iSVqzDE2b",
            "一秒回到放假前！！！！",
            "表演/出镜：星瞳 剪辑：工具人 软件：基于UE5自研 动作：动作捕捉",
            "http://i1.hdslb.com/bfs/archive/1e8938df5b9b92f9d38b40c380bca63f1eaa69a9.jpg",
            "2025-05-05 18:00:00",
            37809,
            196,
            3710,
            1084,
            494,
            88,
            29,
            "114453983005561",
            "00:07",
            "",
            0,
            "{}",
            "",
            5.24
        ],
        ...
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_all_video_list_by_mid(mid)
    return info


@app.get("/video/top_n/")  # ok2
async def read_video_top_n(s: str, e: str, n: int, vtuber: Optional[str] = "星瞳"):
    """
    获取主播时间范围内热度最高的n个视频

    Enter: http://{host}:{port}/video/top_n/?vtuber=星瞳&s=2023-01-01&e=2024-12-31&n=10

    Returns: List[Dict]
    [{bvid: str, name: str, face: str, heat: int, pubtime: str}]

    // Example:

    [
        {
            "bvid": "BV1ocfaYBEo8",
            "name": "新中式舞台《寄明月》",
            "face": "http://i1.hdslb.com/bfs/archive/ffb163d328350bc22f9f3faebce28aa8fdc04324.jpg",
            "heat": 8.15,
            "pubtime": "2025-01-22",
            "honor_short": "全站排行榜最高第37名,热门收录",
            "honor_count": 2
        },
        ...
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_top_n_videos(mid, s, e, n)
    return info


@app.get("/comment/video")  # ok1 # TODO: a big branch, don't use this, try other way
async def read_comment_video(vtuber: Optional[str] = "星瞳"):
    """
    获取视频评论列表

    Returns: List[List]

    [['昵称', '评论', '本楼层数', '时间', '点赞数', 'mid', 'rpid']]

    // Example:

    [
        [
            "星瞳",
            "冲浪的阿昆达",
            "[星瞳·表情包_大头大眼]",
            "2025-05-19 22:24:28",
            0,
            "17951163",
            "262255884241",
            "https://i0.hdslb.com/bfs/face/1b3c206821230ecab84cc2efa4995ba30346839e.jpg",
            0,
            "261063571617",
            "114448211641820",
            0,
            0.1709
        ],
        ...
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_all_video_comments_by_mid(mid)
    return info


@app.get("/comment/dynamic")  # ok1 # TODO: a big branch, don't use this, try other way
async def read_comment_dynamic(vtuber: Optional[str] = "星瞳"):
    """
    获取动态评论列表，

    Returns: List[List]

    [['昵称', '评论', '本楼层数', '时间', '点赞数', 'mid', 'rpid']]

    // Example:

    [
        [
            "星闪双瞳",
            "瞳姐拍的时候自己都憋不住笑了[百变星瞳_大笑]",
            "14",
            "2024-10-18 04:21:06",
            "703",
            "37824774",
            "************"
        ],
        ...
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_all_dynamics_comments_by_mid(mid)
    return info


@app.get("/comment/top_n")  # ok2
async def read_comment_top_n(
    s: str, e: str, n: int, source: str = "video", vtuber: Optional[str] = "星瞳"
):
    """
    获取时间范围内热度最高的n个评论

    Enter: http://{host}:{port}/comment/top_n/?vtuber=星瞳&s=2023-01-01&e=2024-12-31&n=10

    Returns: List[Dict]

    [{'name': str, 'uid': str, 'comment': str, 'heat': str, 'from_id': str, 'from_name': str}]

    // Example:

    [
        {
            "name": "洛恩佐Lorenzo",
            "uid": 1097927383,
            "time": "2024-08-31 12:09:03",
            "comment": "太专业啦，希望以后还能在别的国产游戏里听到星瞳的配音[猴哥]",
            "heat": 3449,
            "from_id": "BV1JS421Q7rX",
            "from_name": "【黑神话:悟空】虚拟偶像给国产3A配音？超长角色配音纪实！"
        },
        ...
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_top_n_comments(mid, s, e, n, source)
    return info


@app.get("/comment/top_n_user")  # ok2
async def read_comment_top_n_user(
    s: str, e: str, n: int, source: str = "all", vtuber: Optional[str] = "星瞳"
):
    """
    获取时间范围内 点赞累计数前 n 的粉丝/出现次数前 n 的粉丝/回复数累计数前 n 的粉丝

    Enter: http://{host}:{port}/comment/top_n_user/?s=2023-01-01&e=2024-12-31&n=10

    Returns: 3 * List[Dict]

    [[{name: str, likeNum: str, uid: str}],
    [{name: str, appealNum: str, uid: str}],
    [{name: str, replyNum: str, uid: str}],]

    // Example:

    [
        [
            {
            "name": "雪糕cheese",
            "likeNum": 3841,
            "uid": "3493139945884106",
            "face": "https://i2.hdslb.com/bfs/face/311fd02cad5db2cc33a1926b79cc9c7dc385b673.jpg"
            },
            {
            "name": "一条宇宙咸",
            "likeNum": 3214,
            "uid": "385314951",
            "face": "https://i0.hdslb.com/bfs/face/ad95fabe16cde205b32d6f7fd88f142b33f92fe6.jpg"
            },
            ...,
        ]
    ]

    """
    mid = get_user_mid(vtuber)
    top_likes, top_comments, top_floors = await query_top_n_comments_user(
        mid, s, e, n, source
    )
    return [top_likes, top_comments, top_floors]


@app.get("/comment/wordcloud")  # ok2
async def read_comment_wordcloud(s: str, e: str, vtuber: Optional[str] = "星瞳"):
    """
    获取时间范围内 评论词云图

    Enter: http://{host}:{port}/comment/wordcloud/?vtuber=星瞳&s=2023-01-01&e=2024-12-31

    Returns: str

    // Example:

    "/wordcloud/星瞳.png"
    """
    mid = get_user_mid(vtuber)
    char_zh = U.get_zh_role_name(vtuber)
    path = await query_comment_wordcloud(mid, char_zh, s, e)
    return path


@app.get("/tieba/whole")  # ok2
async def read_whole_tieba(s: str, e: str, vtuber: Optional[str] = "星瞳"):
    """
    获取时间范围内 贴吧帖子+评论

    Enter: http://{host}:{port}/comment/wordcloud/?vtuber=星瞳&s=2023-01-01&e=2024-12-31

    Returns: ['fid', 'fname', 'tid', "user_name", 'create_time', 'last_time', 'title' , 'text', 'img', 'view_num', 'reply_num', 'agree', 'disagree', 'level_num', "pid", "floor"]

    // Example:

    [
        [
            "27776437",
            "星瞳official",
            "9015559022",
            "🍺打翻酱油瓶",
            "2024-05-13 23:01:28",
            "2024-05-13 23:01:28",
            "求助 请问谁有星瞳这个表情的图",
            "突然get到这个图的点 喜欢想用来当头像 有图的分享一下吧 谢谢大佬们",
            "https://tiebapic.baidu.com/forum/w%3D720%3Bq%3D60%3Bg%3D0/sign=de8d64f7173d26972ed30a5f65c0c3c6/5008401090ef76c6d98ea216db16fdfaae516782.jpg?tbpicau=2025-05-26-05_e3677288674f311e0e3fe82d6c3489eb",
            990,
            0,
            3,
            0,
            1,
            "150271963267",
            1
        ],
        ...
    ]

    """
    mid = get_user_mid(vtuber)
    info = await query_tieba_whole(mid, s, e)
    return info


@app.get("/tieba/thread")  # ok2
async def read_thread_tieba(s: str, e: str, vtuber: Optional[str] = "星瞳"):
    """
    获取时间范围内 贴吧帖子

    Enter: http://{host}:{port}/tieba/thread/?vtuber=星瞳&s=2023-01-01&e=2024-12-31

    Returns: ['fid', 'fname', 'tid', "user_name", 'create_time', 'last_time', 'title' , 'text', 'img', 'view_num', 'reply_num', 'share_num', 'agree', 'disagree']

    // Example:

    [
        [
            "27776437",
            "星瞳official",
            "9014779954",
            "只是只猹_",
            "2024-05-13 11:19:56",
            "2024-05-18 12:34:47",
            "已被主播眼熟",
            "已被主播眼熟\n望周知",
            "http://tiebapic.baidu.com/forum/w%3D720%3Bq%3D60%3Bg%3D0/sign=2d7efa30bedcd100cd9cfa2342b0362d/9dedb831e924b899bfb8290528061d950b7bf65a.jpg?tbpicau=2025-05-31-05_0a12b54beac32b5b3dfceca9b8deaf0b",
            3584,
            25,
            0,
            48,
            0
        ]
    ]

    """
    mid = get_user_mid(vtuber)
    info = await query_tieba_threads(mid, s, e)
    return info


@app.get("/llm/relations")  # ok2
async def read_recent_relationships(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    获取最近联动信息

    Enter: http://{host}:{port}/llm/relations?vtuber=星瞳&recent=30

    Returns: List[str]

    // Example:
    ["等一下，拯救者！中秋和扇宝一起拍'打歌视频'!嘎嘎~",
    "俏鸡传说原创专辑的第二首合唱曲《化身拯救者靠泡泡糖消灭黑暗怪兽》PV上线喽",
    "星瞳X扇宝的《倾城第一花》同名收藏集开启预约啦！",
    "宜宝诺宝周年庆啦，祝快乐！"]
    """
    mid = get_user_mid(vtuber)
    cur_datetime = datetime.now().strftime("%Y-%m-%d")
    info = await query_recent_relationships(mid, cur_datetime, recent)
    info = json.loads(info) if info else ["暂未生成联动信息"]
    return info


@app.get("/llm/sensiment")  # ok2
async def read_recent_sensiment(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    获取最近评论情感值

    Enter: http://{host}:{port}/llm/sensiment/?vtuber=星瞳&recent=30

    Returns: Dict

    // Example:
    {
        'love_ratio': 0.1,
        'positive_ratio': 0.2,
        'neutral_ratio': 0.3,
        'critical_ratio': 0.2,
        'negative_ratio': 0.2,
        'info': [
            {'time': '2023-01-01', 'comment': ['Great product!', 'Really liked it.'], 'sensiment': 0.85},
            {'time': '2023-01-02', 'comment': ['Not bad.', 'Could be better.'], 'sensiment': 0.55},
            {'time': '2023-01-03', 'comment': ['Excellent!'], 'sensiment': 1.0},
            ...
        ]
    }

    """
    mid = get_user_mid(vtuber)
    cur_time = datetime.now().strftime("%Y-%m-%d")
    info = await query_recent_comments_sentiment_value(
        mid, cur_time, recent, source="all"
    )

    return info


@app.get("/llm/sensiment/bili")  # ok2
async def read_recent_sensiment_bili(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    获取最近bilibili评论情感值

    Enter: http://{host}:{port}/llm/sensiment/bili?vtuber=星瞳&recent=30

    Returns: Dict

    // Example:
    {
        'love_ratio': 0.1,
        'positive_ratio': 0.2,
        'neutral_ratio': 0.3,
        'critical_ratio': 0.2,
        'negative_ratio': 0.2,
        'info': [
            {'time': '2023-01-01', 'comment': ['Great product!', 'Really liked it.'], 'sensiment': 0.85},
            {'time': '2023-01-02', 'comment': ['Not bad.', 'Could be better.'], 'sensiment': 0.55},
            {'time': '2023-01-03', 'comment': ['Excellent!'], 'sensiment': 1.0},
            ...
        ]
    }

    """
    mid = get_user_mid(vtuber)
    cur_time = datetime.now().strftime("%Y-%m-%d")
    info = await query_recent_comments_sentiment_value(
        mid, cur_time, recent, source="bili"
    )

    return info


@app.get("/llm/sensiment/tieba")  # ok2
async def read_recent_sensiment_tieba(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    获取最近贴吧评论情感值

    Enter: http://{host}:{port}/llm/sensiment/tieba/?vtuber=星瞳&recent=30

    Returns: Dict

    // Example:
    {
        'love_ratio': 0.1,
        'positive_ratio': 0.2,
        'neutral_ratio': 0.3,
        'critical_ratio': 0.2,
        'negative_ratio': 0.2,
        'info': [
            {'time': '2023-01-01', 'comment': ['Great product!', 'Really liked it.'], 'sensiment': 0.85},
            {'time': '2023-01-02', 'comment': ['Not bad.', 'Could be better.'], 'sensiment': 0.55},
            {'time': '2023-01-03', 'comment': ['Excellent!'], 'sensiment': 1.0},
            ...
        ]
    }

    """
    mid = get_user_mid(vtuber)
    cur_time = datetime.now().strftime("%Y-%m-%d")
    info = await query_recent_comments_sentiment_value(
        mid, cur_time, recent, source="tieba"
    )

    return info


@app.get("/llm/topics")  # ok2
async def read_comment_topics(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    获取最近评论话题

    Enter: http://{host}:{port}/llm/topics/?vtuber=星瞳&recent=30

    Returns: List[Dict]

    // Example:
    [
        {
            "topic": "虚拟主播星瞳'七杀梗'引发争议",
            "rank": 1,
            "heat": 5,
            "keywords": [
                "星瞳",
                "七杀梗",
                "背锅侠",
                "争议",
                "推卸责任"
            ],
            "comments": [
                "七杀梗已经让星瞳变成了传奇背锅侠，那些出事的主播完全是因为自身问题，可是只需要轻飘飘的一句'都是星瞳克死的'，那些主播身上的污点便会当即消除大半，其粉丝也会将矛头指向星瞳。",
                "现在的星瞳已经被描述成了一个人厌狗嫌的扫把星，这对主播的心理和事业都会造成巨大的破坏。",
                "尤其今天刷到那个，说是帮星瞳挡了几刀如何如何，言外之意就是之所以混到如今地步，错不在她，全怪星瞳。这完全是在推卸责任，实在令人作呕。"
            ]
        },
        ...
    ]
    """
    mid = get_user_mid(vtuber)
    cur_datetime = datetime.now().strftime("%Y-%m-%d")
    info = await query_tieba_summaries_from_ai_gen_table_by_date(
        mid, cur_datetime, recent
    )
    return info


@app.get("/basic/follower/rise")  # ok2
async def read_follower_arise_num(recent: int = 1, vtuber: Optional[str] = "星瞳"):
    """
    获取当前粉丝增量

    Enter: http://{host}:{port}/basic/followers/rise/?vtuber=星瞳

    Returns: int

    // Example: -20

    """
    mid = get_user_mid(vtuber)
    info = await query_current_follower_change_num(mid, recent)
    return info


@app.get("/basic/dahanghai/rise")  # ok2
async def read_dahanghai_arise_num(recent: int = 1, vtuber: Optional[str] = "星瞳"):
    """
    获取当前大航海增量

    Enter: http://{host}:{port}/basic/dahanghai/rise/?vtuber=星瞳

    Returns: int

    // Example: -20

    """
    mid = get_user_mid(vtuber)
    info = await query_current_dahanghai_change_num(mid, recent)
    return info


# RESTful: GET /videos/{bvid}/conclusion (get specific video's AI conclusion)
@app.get("/videos/{bvid}/conclusion")
async def get_video_conclusion(bvid: str):
    """
    Get AI-generated conclusion for a specific video.

    Args:
        bvid: Bilibili video ID (e.g., BV11k4y1Y71i)

    Returns:
        Standard API response with video conclusion
    """
    try:
        conclusion = await query_video_ai_conclusion_by_bvid(bvid)
        return create_success_response(
            data={
                "bvid": bvid,
                "conclusion": conclusion,
                "generated_at": datetime.now().isoformat()
            },
            message="Video conclusion retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get conclusion for video {bvid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video conclusion", 500)
        )

# Backward compatibility: keep old endpoint
@app.get("/video/conclusion")
async def read_video_ai_conclusion(bvid: str):
    """
    [DEPRECATED] Get video AI conclusion. Use GET /videos/{bvid}/conclusion instead.
    """
    logger.warning("Using deprecated endpoint /video/conclusion. Use GET /videos/{bvid}/conclusion instead.")
    info = await query_video_ai_conclusion_by_bvid(bvid)
    return info


# RESTful: GET /videos/{bvid}/views/current (get current day views for specific video)
@app.get("/videos/{bvid}/views/current")
async def get_video_current_views(bvid: str):
    """
    Get current day views and daily increase for a specific video.

    Args:
        bvid: Bilibili video ID (e.g., BV11k4y1Y71i)

    Returns:
        Standard API response with current day view data
    """
    try:
        view_data = await query_current_video_day_views(bvid)

        # Parse the returned data (assuming it's a list like ["BV1a3DqYZErW","2024-11-22","94865","0"])
        if view_data and len(view_data) >= 4:
            parsed_data = {
                "bvid": view_data[0],
                "date": view_data[1],
                "total_views": int(view_data[2]) if view_data[2].isdigit() else view_data[2],
                "daily_increase": int(view_data[3]) if view_data[3].isdigit() else view_data[3]
            }
        else:
            parsed_data = view_data

        return create_success_response(
            data=parsed_data,
            message="Current video views retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get current views for video {bvid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video views", 500)
        )

# Backward compatibility: keep old endpoint
@app.get("/video/views/current")
async def read_current_video_day_views(bvid: str):
    """
    [DEPRECATED] Get current video views. Use GET /videos/{bvid}/views/current instead.
    """
    logger.warning("Using deprecated endpoint /video/views/current. Use GET /videos/{bvid}/views/current instead.")
    info = await query_current_video_day_views(bvid)
    return info


@app.get("/video/views/recent")  # ok2
async def read_recent_video_day_views(bvid: str, recent: int):
    """
    获取单个视频最近指定rencent天数的播放量/日增列表

    Enter: http://{host}:{port}/video/views/recent/?bvid=BV11k4y1Y71i&recent=20&vtuber=星瞳

    Returns: List[List] or []

    // Example: [
      {
        "id": 63391,
        "uid": "401315430",
        "name": "星瞳",
        "bvid": "BV1a3DqYZErW",
        "title": "40秒梦幻旅行，跟我一起遨游天际！【iykyk】",
        "create_time": 1731146400,
        "datetime": "2025-05-29T00:00:00",
        "view_num": 100740,
        "view_rise_num": 4
      },
      ...
    ]

    """
    info = await query_single_video_rencent_day_data(bvid, recent)
    return info


@app.get("/video/views/target")  # ok2
async def read_target_video_day_views(bvid, time):
    """
    获取单个视频指定天的播放量/日增

    Enter: http://{host}:{port}/video/views/target/?bvid=BV11k4y1Y71i&vtuber=星瞳

    Returns: List

    // Example: ["BV1a3DqYZErW","2024-11-22","94865","0"]
    """
    info = await query_single_video_target_day_data(bvid, time)
    return info


@app.get("/video/views/top_n/day")  # ok2
async def read_top_n_video_day_by_time(
    time: str, n: int, vtuber: Optional[str] = "星瞳"
):
    """
    获取指定天前n个的视频播放量/日增

    Enter: http://{host}:{port}/video/views/top_n/day/?time=2024-11-22&n=20&vtuber=星瞳

    Returns: List

    // Example: [
        ["BV1a3DqYZErW","2024-11-21","94865","0"],
        ["BV1a3DqYZErW","2024-11-22","94865","0"],
        ...
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_top_n_view_rise_target_day_data(mid, time, n)
    return info


@app.get("/video/views/top_n/period")  # ok2
async def read_top_n_video_day_by_period(s, e, n, vtuber: Optional[str] = "星瞳"):
    """
    获取指定时间区间前n个的视频播放量/日增

    Enter: http://{host}:{port}/video/views/top_n/period/?s=2024-11-21&e=2024-11-21&n=20&vtuber=星瞳

    Returns: List

    // Example: [
        ["BV1a3DqYZErW","2024-11-21","94865","0"],
        ["BV1a3DqYZErW","2024-11-22","94865","0"],
        ...
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_top_n_view_rise_day_data_period(mid, s, e, n)
    return info


@app.get("/basic/dahanghai/list/target")  # ok2
async def read_target_dahanghai_whole_list(
    target_datetime, vtuber: Optional[str] = "星瞳"
):
    """
    获取指定时间下的舰长清单

    Enter: http://{host}:{port}/basic/dahanghai/list/?vtuber=星瞳

    Returns: List

    // Example:
    [
        {
            "up_uid": "401315430",
            "up_name": "星瞳",
            "time": "2025-05-14",
            "datetime": "2025-05-14T15:19:53.561790",
            "num": 276,
            "page": 1,
            "uid": 2168222,
            "ruid": 401315430,
            "rank": 1,
            "username": "bili_2976",
            "face": "https://i0.hdslb.com/bfs/face/member/noface.jpg",
            "guard_level": 1,
            "guard_sub_level": 0,
            "if_top3": true
        },
    ]
    """
    mid = get_user_mid(vtuber)
    info = await query_dahanghai_list_by_uid_and_datetime(mid, target_datetime)
    return info


@app.get("/info/recent/dict")  # ok2
async def read_recent_info(vtuber: Optional[str] = "星瞳"):
    """
    获取近期活动

    Enter: http://{host}:{port}/info/recent/dict/?vtuber=星瞳

    Returns: Dict

    // Example:
    {
    "time": "2025-05-28",
    "name": "星瞳_Official",
    "follower_change": -44,
    "dahanghai_change": -1,
    "video_content": [
        "2025-05-27 18:32:29",
        "打CALL教程❌体能测试✅",
        "14100",
        ""
    ],
    "dynamic_content": [
        "2025-05-27 18:32:29",
        "叮铃铃~在充满爱的起床铃中举起了双手~闪闪发光的小星星，伴随这声线~到我身边~~~​[星瞳·动态表情包_可爱猫猫]小星星们，2568，热烈招手不见不散！​[星瞳·动态表情包_星星期待]"
    ],
    "live_content": "暂无直播信息",
    "relations": [
        "星瞳邀请了@东爱璃Lovely 和 @雪糕cheese 一起直播游玩《胜利女神：新的希望》。"
    ],
    "rise_videos": [
        [
        "BV1c6jdzHE6x",
        "N/A",
        "打CALL教程❌体能测试✅",
        15472,
        3572
        ],
        [
        "BV1HMJqz3EDz",
        "N/A",
        "出道四年，从音痴到作曲【星瞳vlog】",
        72176,
        1193
        ],
        [
        "BV1x9JazNEPb",
        "N/A",
        "为你而作 | 星瞳新歌《雪中的心愿》公开❄️",
        52385,
        998
        ]
    ],
    "tieba_topic": [
        {
            "topic": "新的一天，新的加9？",
            "rank": 1,
            "heat": 5,
            "keywords": [
                "新的一天",
                "加9"
            ],
            "comments": [
                "妈妈妈",
                "急急急",
                "牛牛牛",
                "签签签",
                "签到",
                "11",
                "6",
                "3",
                "。\n凸变英雄吧怎么没人啊？"
            ]
        },
        ...
    ]
    }
    """
    mid = get_user_mid(vtuber)
    room_id = get_user_room_id(vtuber)
    info = await query_recent_info(mid, room_id)
    return info


@app.get("/info/recent/sumary")  # FIXME
async def read_summarise_rise_reason(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    获取近期活动涨掉粉原因

    Enter: http://{host}:{port}/info/recent/sumary/?vtuber=星瞳

    Returns: string

    // Example:
    根据提供的信息，星瞳近期粉丝数减少的主要原因可能包括：
    1. 内容更新频率较低：最近的视频发布于2024-11-21，距离当前已有一段时间。长时间没有新视频内容可能导致部分粉丝失去兴趣。
    2. 视频播放量增长不足：虽然有几个视频的日播放量有所增长，但增长幅度不大。最高的日增播放量为7083，相对于星瞳的粉丝基数来说并不算高，说明内容吸引 力可能有所下降。
    3. 粉丝互动质量问题：虽然星瞳最近发布了动态与粉丝互动，但从贴吧讨论来看，粉丝对星瞳的未来发展存在一些担忧，如"能再签一年我就满足了"的评论表明部 分粉丝对星瞳的长期发展缺乏信心。
    4. 账号活跃度与粉丝流失的矛盾：贴吧中有粉丝提到"现在是账号一活跃就掉粉，没有新关注"，这可能暗示星瞳的内容或互动方式未能有效吸引新粉丝，同时可能 引起了一些老粉丝的不满。
    5. 竞争加剧：星瞳参与了多次联动活动，包括与其他虚拟主播一起玩游戏和参与培训。虽然联动可能带来一些曝光，但也可能使部分粉丝将注意力转移到其他主播 身上。
    6. 粉丝期待与现实的落差：贴吧讨论中出现了对星瞳未来发展的猜测，如"小T干到2031年差不多要做幕后工作了吧"，这种长期规划的讨论可能反映出部分粉丝对星瞳当前发展状况的不满或担忧。
    总的来说，星瞳近期粉丝数减少可能是由于内容更新不及时、视频吸引力下降、粉丝互动质量问题以及市场竞争加剧等因素综合导致的。为了扭转这一趋势，星瞳可能需要调整内容策略，提高视频质量和更新频率，同时加强与粉丝的有效互动。
    """
    mid = get_user_mid(vtuber)
    target_date = datetime.now().strftime("%Y-%m-%d")

    info = await query_rise_reason_from_ai_gen_table_by_date(mid, target_date, recent)
    return info


@app.get("/board/live")  # ok2
async def read_all_live_status():
    """
    获取所有主播的直播状态

    Enter: http://{host}:{port}/board/live

    Returns:
    [
        {
            'name': 'char',
            'if_live': True,
            'live_url': 'https://live.bilibili.com/123456',
            'live_title': 'title',
            'live_cover': 'https://i0.hdslb.com/bfs/live/cover.jpg',
            'live_roomid': '123456',
        },
        ...
    ]
    """
    info = await collect_all_live_status_stuff()
    return info


@app.get("/board/stat")  # ok2
async def read_all_dahanghai_and_follower_stuff(recent: int = 1):
    """
    获取所有主播的粉丝，大航海

    Enter: http://{host}:{port}/board/stat

    Returns:
        [
            {
                'name': str,
                'dahanghai_num': int
                'dahanghai_rise_num': int
                'follower_num': int
                'follower_rise_num': int
            },
            ...
        ]
    """
    info = await collect_all_dahanghai_and_follower_rise_num_stuff(recent)
    return info


@app.get("/board/info")  # ok2
async def read_all_total_info():
    """
    获取所有主播的基本信息

    Enter: http://{host}:{port}/board/info

    Returns:
        [
            {
                'name': str,
                'face': str
                'description': str
                'birthday': str
                'cover_url': str
            },
            ...
        ]
    """
    info = await collect_whole_info_from_all_vtubers()
    return info


@app.get("/board/dahanghai/whole")  # FIXME
async def read_whole_flow_from_dahanghai_list(c: str, p: str):
    """
    获取总体大航海流动方向

    Enter: http://{host}:{port}/board/dahanghai/whole/?c=2024-12-01&p=2024-11-27

    Returns:
        {
            'direct_loss': dict, # {}
            'p2c_flow_between_anchors': dict, # {'anchor1': {'anchor2': {1}}, 'anchor2': {'anchor1': {3}}}
            'additional_users': dict, # {}
            'c2p_flow_between_anchors': dict, # {'anchor1': {'anchor2': {3}}, 'anchor2': {'anchor1': {1}}}
        }
    """
    info = await collect_whole_flow_from_all_vtubers(c, p)
    return info


@app.get("/board/dahanghai/target")  # FIXME
async def read_target_flow_dahanghai_list(target: str, c: str, p: str, n: int = 5):
    """
    获取某主播大航海流动方向

    Enter: http://{host}:{port}/board/dahanghai/target/?target=星瞳&c=2024-12-01&p=2024-11-27&n=5

    Returns:
        {
            'in': dict, # {name: List[], ...}
            'out': dict, # # {name: List[], ...}
        }
    """
    info = await collect_target_flow_with_target_vtuber(target, c, p, n)
    return info


@app.get("/board/dahanghai/target_period")
async def read_target_flow_dahanghai_list_by_period(
    target: str,
    start_time_str1: str,
    end_time_str1: str,
    start_time_str2: str,
    end_time_str2: str,
    n: int = 5,
):
    """
    获取某主播在指定两个时间段内大航海流动方向
    Get the flow direction of a specific VTuber's dahanghai between two specified periods.

    Enter: http://{host}:{port}/board/dahanghai/target_period/?target=星瞳&start_time_str1=2024-11-01&end_time_str1=2024-11-30&start_time_str2=2024-12-01&end_time_str2=2024-12-31&n=5

    Returns:
        {
            'in': dict, # {name: List[], ...}
            'out': dict, # # {name: List[], ...}
        }
    """
    info = await collect_target_flow_with_target_vtuber_by_period(
        target, start_time_str1, end_time_str1, start_time_str2, end_time_str2, n
    )
    return info


@app.get("/follower/new_list")  # ok2
async def read_follower_new_list(recent: int = 7, vtuber: Optional[str] = "星瞳"):
    """
    获取指定

    Enter: http://{host}:{port}/follower/new_list/?recent=7&vtuber=星瞳

    Returns: List

    // Example:
    [
        {
            "mid": 386169396,
            "attribute": 0,
            "mtime": 1734873176,
            "tag": null,
            "special": 0,
            "contract_info": {},
            "uname": "星辰葱姜",
            "face": "http://i0.hdslb.com/bfs/face/8dfdfb0e8ae10e88161d918f5926ccb3bc731612.jpg",
            "sign": "你不再武器，而是人如其名的人",
            "face_nft": 0,
            "official_verify": {
                "type": -1,
                "desc": ""
            },
            "vip": {
                "vipType": 1,
                "vipDueDate": 1711296000000,
                "dueRemark": "",
                "accessStatus": 0,
                "vipStatus": 0,
                "vipStatusWarn": "",
                "themeType": 0,
                "label": {
                    "path": "",
                    "text": "",
                    "label_theme": "",
                    "text_color": "",
                    "bg_style": 0,
                    "bg_color": "",
                    "border_color": ""
                },
                "avatar_subscript": 0,
                "nickname_color": "",
                "avatar_subscript_url": ""
            },
            "name_render": {},
            "nft_icon": "",
            "rec_reason": "",
            "track_id": "",
            "follow_time": ""
        },
        ...
    ]
    """
    mid = get_user_mid(vtuber)
    cur_datetime = datetime.now().strftime("%Y-%m-%d")
    info = await query_followers_list(mid, cur_datetime, recent)
    return info


@app.get("/follower/review_list")  # ok2
async def read_follower_review_list(rencent: int = 7, vtuber: Optional[str] = "星瞳"):
    """
    获取新增粉丝观看是否观看直播列表

    Enter: http://{host}:{port}/follower/review_list/?recent=7&vtuber=星瞳

    Returns: str

    Example:
    [
        [
            1,
            "401315430",
            "星瞳",
            "2025-05-19",
            7,
            "1342882756",
            "一只峨眉山野猴",
            false
        ],
        [
            2,
            "401315430",
            "星瞳",
            "2025-05-19",
            7,
            "3546611519064497",
            "浅默淡殇乀",
            false
        ],
        ...
    ]
    """
    mid = get_user_mid(vtuber)
    cur_time = datetime.now().strftime("%Y-%m-%d")
    info = await query_followers_review_list(mid, cur_time, rencent)
    return info


@app.get("/follower/review_rate")  # ok2
async def read_follower_review_rate(
    recent: Optional[int] = 7, vtuber: Optional[str] = "星瞳"
):
    """
    获取粘性粉丝比例

    Enter: http://{host}:{port}/follower/review_rate/?recent=7&vtuber=星瞳

    Returns: str

    Example: 2%

    """
    mid = get_user_mid(vtuber)
    cur_time = datetime.now().strftime("%Y-%m-%d")
    info = await query_followers_review_rate(mid, cur_time, recent)
    return info


@app.get("/live/sessions")  # ok2
async def read_live_sessions(vtuber: Optional[str] = "星瞳"):
    """
    获取vtuber的全部直播列表信息

    Enter: http://{host}:{port}/live/sessions/?vtuber=星瞳

    Returns: List[Dict]

    Example:
    [{'liveId': 'test_live_id2', 'isFinish': False, 'isFull': False, 'parentArea': 'Parent Area', 'area': 'Area', 'coverUrl': 'Test Cover', 'danmakusCount': 300, 'startDate': '2023-01-01 12:00:00', 'stopDate': '2023-01-01 14:00:00', 'title': 'Test Title', 'totalIncome': 1000.0, 'watchCount': 500, 'likeCount': 200, 'payCount': 10, 'interactionCount': 50, 'onlineRank': 100}, {'liveId': 'test_live_id', 'isFinish': False, 'isFull': False, 'parentArea': 'Parent Area', 'area': 'Area', 'coverUrl': 'Test Cover', 'danmakusCount': 300, 'startDate': '2023-01-01 12:00:00', 'stopDate': '2023-01-01 14:00:00', 'title': 'Test Title', 'totalIncome': 1000.0, 'watchCount': 500, 'likeCount': 200, 'payCount': 10, 'interactionCount': 50, 'onlineRank': 100}]
    """
    room_id = get_user_room_id(vtuber)
    info = await query_live_info_with_room_id(room_id)

    return info


@app.get("/live/one_session")  # ok2
async def read_live_one_session(liveId: str):
    """
    获取指定liveID的全部信息
    #

    Enter: http://{host}:{port}/live/one_session/?liveId="18e1008c-2737-42b1-a1b7-d74c59eecb0c"

    Returns: List[Dict]

    Example:
    {'liveId': '18e1008c-2737-42b1-a1b7-d74c59eecb0c', 'isFinish': False, 'isFull': False, 'parentArea': 'Parent Area', 'area': 'Area', 'coverUrl': 'Test Cover', 'danmakusCount': 300, 'startDate': '2023-01-01 12:00:00', 'stopDate': '2023-01-01 14:00:00', 'title': 'Test Title', 'totalIncome': 1000.0, 'watchCount': 500, 'likeCount': 200, 'payCount': 10, 'interactionCount': 50, 'onlineRank': 100}
    """

    info = await query_whole_live_info_with_live_id(liveId)
    return info


@app.get("/live/one_session/minute")  # ok2
async def read_live_one_session_with_minutes(liveId: str, inteval: Optional[int] = 10):
    """
    获取指定liveID的全部信息，包括每分钟的信息
    #

    Enter: http://{host}:{port}/live/one_session/?liveId="18e1008c-2737-42b1-a1b7-d74c59eecb0c"

    Returns: List[Dict]

    Example:
    {'liveId': '18e1008c-2737-42b1-a1b7-d74c59eecb0c', 'isFinish': False, 'isFull': False, 'parentArea': 'Parent Area', 'area': 'Area', 'coverUrl': 'Test Cover', 'danmakusCount': 300, 'startDate': '2023-01-01 12:00:00', 'stopDate': '2023-01-01 14:00:00', 'title': 'Test Title', 'totalIncome': 1000.0, 'watchCount': 500, 'likeCount': 200, 'payCount': 10, 'interactionCount': 50, 'onlineRank': 100}
    """
    info = await query_minutes_live_info_with_live_id(liveId, inteval)
    return info


# --- Creator Info API Endpoints ---

@app.get("/creator/overview/stat/by-date-range")
async def read_creator_overview_stat_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按时间范围查询创作者概览统计数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 概览统计数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_overview_stat_by_date_range(
            uid, start_date, end_date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator overview stat by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch overview statistics by date range: {str(e)}"
        )


@app.get("/creator/overview/stat/by-date")
async def read_creator_overview_stat_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按特定日期查询创作者概览统计数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 概览统计数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_overview_stat_by_date(
            uid, date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator overview stat by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch overview statistics by date: {str(e)}"
        )

@app.get("/creator/attention/analyze/by-date-range")
async def read_creator_attention_analyze_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按时间范围查询创作者涨粉分析数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 涨粉分析数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_attention_analyze_by_date_range(
            uid, start_date, end_date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator attention analyze by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch attention analyze data by date range: {str(e)}"
        )


@app.get("/creator/attention/analyze/by-date")
async def read_creator_attention_analyze_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按特定日期查询创作者涨粉分析数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 涨粉分析数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_attention_analyze_by_date(
            uid, date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator attention analyze by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch attention analyze data by date: {str(e)}"
        )


@app.get("/creator/archive/analyze/by-date-range")
async def read_creator_archive_analyze_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    period: Optional[int] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    按时间范围查询创作者播放分析数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        period: 分析周期过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 播放分析数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_archive_analyze_by_date_range(
            uid, start_date, end_date, period, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator archive analyze by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch archive analyze data by date range: {str(e)}"
        )


@app.get("/creator/archive/analyze/by-date")
async def read_creator_archive_analyze_by_date(
    uid: str,
    date: str,
    period: Optional[int] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    按特定日期查询创作者播放分析数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        period: 分析周期过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 播放分析数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_archive_analyze_by_date(
            uid, date, period, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator archive analyze by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch archive analyze data by date: {str(e)}"
        )


@app.get("/creator/fan/graph/by-date-range")
async def read_creator_fan_graph_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按时间范围查询创作者粉丝图表数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 粉丝图表数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_fan_graph_by_date_range(
            uid, start_date, end_date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator fan graph by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch fan graph data by date range: {str(e)}"
        )


@app.get("/creator/fan/graph/by-date")
async def read_creator_fan_graph_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按特定日期查询创作者粉丝图表数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 粉丝图表数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_fan_graph_by_date(
            uid, date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator fan graph by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch fan graph data by date: {str(e)}"
        )

@app.get("/creator/fan/overview/by-date-range")
async def read_creator_fan_overview_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按时间范围查询创作者粉丝概览数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 粉丝概览数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_fan_overview_by_date_range(
            uid, start_date, end_date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator fan overview by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch fan overview data by date range: {str(e)}"
        )


@app.get("/creator/fan/overview/by-date")
async def read_creator_fan_overview_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按特定日期查询创作者粉丝概览数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 粉丝概览数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_fan_overview_by_date(
            uid, date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator fan overview by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch fan overview data by date: {str(e)}"
        )


@app.get("/creator/video/compare/by-date-range")
async def read_creator_video_compare_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按时间范围查询创作者视频比较数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 视频比较数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_video_compare_by_date_range(
            uid, start_date, end_date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator video compare by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch video compare data by date range: {str(e)}"
        )


@app.get("/creator/video/compare/by-date")
async def read_creator_video_compare_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按特定日期查询创作者视频比较数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 视频比较数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_video_compare_by_date(
            uid, date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator video compare by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch video compare data by date: {str(e)}"
        )


@app.get("/creator/video/pandect/by-date-range")
async def read_creator_video_pandect_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    data_type_column: Optional[str] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    按时间范围查询创作者视频趋势数据

    Args:
        uid: 用户ID
        start_date: 开始日期，格式：YYYY-MM-DD
        end_date: 结束日期，格式：YYYY-MM-DD
        data_type_column: 数据类型过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 视频趋势数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_video_pandect_by_date_range(
            uid, start_date, end_date, data_type_column, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator video pandect by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch video pandect data by date range: {str(e)}"
        )


@app.get("/creator/video/pandect/by-date")
async def read_creator_video_pandect_by_date(
    uid: str,
    date: str,
    data_type_column: Optional[str] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    按特定日期查询创作者视频趋势数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        data_type_column: 数据类型过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 视频趋势数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_video_pandect_by_date(
            uid, date, data_type_column, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator video pandect by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch video pandect data by date: {str(e)}"
        )


@app.get("/creator/video/survey/by-date-range")
async def read_creator_video_survey_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    data_type: Optional[int] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    按时间范围查询创作者视频调查数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        data_type: 数据类型过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 视频调查数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_video_survey_by_date_range(
            uid, start_date, end_date, data_type, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator video survey by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch video survey data by date range: {str(e)}"
        )


@app.get("/creator/video/survey/by-date")
async def read_creator_video_survey_by_date(
    uid: str,
    date: str,
    data_type: Optional[int] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    按特定日期查询创作者视频调查数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        data_type: 数据类型过滤（可选）
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 视频调查数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_video_survey_by_date(
            uid, date, data_type, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator video survey by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch video survey data by date: {str(e)}"
        )


@app.get("/creator/video/source/by-date-range")
async def read_creator_video_source_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按时间范围查询创作者视频来源数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 视频来源数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_video_source_by_date_range(
            uid, start_date, end_date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator video source by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch video source data by date range: {str(e)}"
        )


@app.get("/creator/video/source/by-date")
async def read_creator_video_source_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按特定日期查询创作者视频来源数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 视频来源数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_video_source_by_date(
            uid, date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator video source by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch video source data by date: {str(e)}"
        )


@app.get("/creator/video/view_data/by-date-range")
async def read_creator_video_view_data_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按时间范围查询创作者视频观看数据

    Args:
        uid: 用户ID
        start_date: 开始时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        end_date: 结束时间，格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 视频观看数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_video_view_data_by_date_range(
            uid, start_date, end_date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator video view data by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch video view data by date range: {str(e)}"
        )


@app.get("/creator/video/view_data/by-date")
async def read_creator_video_view_data_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    按特定日期查询创作者视频观看数据

    Args:
        uid: 用户ID
        date: 查询日期，格式：YYYY-MM-DD
        limit: 返回记录数限制，默认1000
        offset: 分页偏移量，默认0

    Returns:
        dict: 包含查询结果和总数的字典
            - data: 视频观看数据列表
            - total: 总记录数
    """
    try:
        results, total_count = await query_video_view_data_by_date(
            uid, date, limit, offset
        )
        return {
            "data": results,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"Error fetching creator video view data by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch video view data by date: {str(e)}"
        )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app="app:app", host="0.0.0.0", port=9022, reload=False, workers=1)
