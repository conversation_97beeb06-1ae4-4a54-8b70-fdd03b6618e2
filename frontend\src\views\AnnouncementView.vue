<template>
  <div class="announcement-view p-4 mx-auto w-1/2">
    <h1 class="text-2xl font-bold mb-6 text-gray-800 dark:text-gray-200">网站公告</h1>
    <div v-if="loading" class="text-center text-gray-500 dark:text-gray-400">
      加载中...
    </div>
    <div v-else-if="error" class="text-center text-red-500">
      加载公告失败: {{ error }}
    </div>
    <div v-else class="space-y-6">
      <div
        v-for="post in posts"
        :key="post.id"
        class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6"
      >
        <h2 class="text-xl font-semibold mb-2 text-black dark:text-white">{{ post.title }}</h2>
        <p class="text-sm text-gray-700 dark:text-gray-300 mb-3">发布于: {{ formatDate(post.date) }}</p>
        <div class="prose dark:prose-invert max-w-none" v-html="renderMarkdown(post.content)"></div>
      </div>
      <div v-if="posts.length === 0" class="text-center text-gray-500 dark:text-gray-400">
        暂无公告
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { marked } from 'marked'; // For rendering Markdown
import { onMounted, ref } from 'vue';

interface AnnouncementPost {
  id: string;
  title: string;
  date: string; // ISO 8601 date string
  content: string; // Markdown content
}

const posts = ref<AnnouncementPost[]>([]);
const loading = ref(true);
const error = ref<string | null>(null);

const fetchAnnouncements = async () => {
  loading.value = true;
  error.value = null;
  try {
    // await new Promise(resolve => setTimeout(resolve, 1000));

    const samplePosts: AnnouncementPost[] = [
      {
        id: 'update-2024-05-27',
        title: '🎉 Vupbi V2.0版本上线！',
        date: '2024-05-27T10:00:00Z',
        content: `
### 主要更新
*   全部更新内容可见链接：☞[虚拟偶像数据看板vupbi v2版本更新日志](https://doc.weixin.qq.com/doc/w3_AR0ACAY3ADcCNuceZg9BoQTiY4mz0?scode=AJEAIQdfAAosGc9cHRAR0ACAY3ADc)
*   1.后端架构优化： 后端重构了用户信息的全部API，多线程处理提高了系统性能。
*   2.数据获取与存储改进： 优化了调度器功能；将拉取数据迁移至外部服务器；处理了多种数据拉取和存储异常情况。
*   3.数据库引入： 引入了postgres和异步连接池，并优化了多处查询逻辑，如将多个数据获取函数改为关联表查询，显著提升了数据库操作效率。
*   4.舰长流向分析优化： 增加了时间范围筛选功能，允许用户自定义时间段进行分析。
*   5.直播分析数据维护： 直播分析页面目前单独维护数据，包括存储轮播主播的数据。
*   6.白名单支持： 集成了企业微信登录方式，提升了安全性。
*   7.前端面板优化： 优化了一些数据的显示形式。
*   8.新增公告页面，方便查看网站更新日志和重要通知。
*   9.多项测试与归档完成： 完成了大量后端接口和查询功能的测试，确保了数据准确性和系统稳定性；对多个数据表结构和数据获取逻辑进行了归档整理和优化，提升了系统的可维护性。
*   10.修复了用户反馈的一些已知BUG。
*   ...

### 后续计划
*   视频分析功能：补充星瞳的后台数据，包括视频播放量变化、封面点击率、完播率、单个视频转粉、播粉比和转粉率等情况。
*   尝试重新计算评论情感（sentiment）数据。
*   开发Copilot界面，能直接沟通数据库，如“6月4日，星瞳的最近活动有哪些？”。 
        `,
      },
      {
        id: 'update-2024-02-13',
        title: '🚀 Vupbi V1.1正式发布！',
        date: '2024-02-13T08:00:00Z',
        content: `
欢迎使用Vupbi！我们致力于为用户提供全面的Vup数据分析服务。

目前已上线功能：
*   个人空间：查看VTuber详细数据。
*   动态分析：追踪VTuber动态更新。
*   视频分析：分析VTuber视频表现。
*   评论分析：洞察观众评论情感。
*   直播分析：监控直播数据。
*   VUP对比：横向对比不同VTuber。
*   舰长流向：分析用户订阅行为。

感谢您的支持！
        `,
      },
    ];
    posts.value = samplePosts;
  } catch (e) {
    console.error('Failed to fetch announcements:', e);
    error.value = '无法连接到服务器获取公告。';
  } finally {
    loading.value = false;
  }
};

const renderMarkdown = (markdownContent: string) => {
  return marked(markdownContent);
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

onMounted(() => {
  fetchAnnouncements();
});
</script>

<style scoped>
.prose h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
  color: #4b5563;
}
.dark .prose h3 {
  color: #d1d5db;
}
.prose ul {
  list-style-type: disc;
  padding-left: 1.25rem;
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  color: #4b5563;
}
.dark .prose ul {
  color: #9ca3af;
}
.prose strong {
  font-weight: 600;
  color: #1f2937;
}
.dark .prose strong {
  color: #e5e7eb;
}
</style>
