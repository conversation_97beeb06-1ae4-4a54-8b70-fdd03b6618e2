import asyncio
import copy
import time
from datetime import datetime, timedelta
from decimal import ROUND_HALF_UP, Decimal

import asyncpg

from backend.utils.db_pool import get_connection
from logger import logger
from sql.sql_const import get_danmu_table_name


async def query_danmu_by_room_and_timespan(room_id, start_ts, end_ts):
    """
    查询指定直播间在时间范围内的弹幕数据
    Args:
        room_id: 直播间ID
        start_ts: 开始时间戳(毫秒)
        end_ts: 结束时间戳(毫秒)
    Returns:
        tuple: (弹幕数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            table_name = get_danmu_table_name(room_id)

            table_exists = await conn.fetchval(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public'
                    AND table_name = $1
                );
                """,
                table_name,
            )

            if not table_exists:
                logger.info(f"表 {table_name} 不存在，无法查询弹幕数据")
                return [], 0

            # 使用 f-string 插入表名是安全的，因为 get_danmu_table_name 是受控函数
            query = f"""
                SELECT * FROM "{table_name}"
                WHERE timestamp BETWEEN $1 AND $2
                ORDER BY timestamp
            """

            results = await conn.fetch(query, start_ts, end_ts)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询弹幕数据时发生未知错误: {e}")
        return [], 0


async def query_danmu_by_room_and_datetime(room_id, start_datetime, end_datetime):
    """
    查询指定直播间在指定日期范围内的弹幕数据
    Args:
        room_id: 直播间ID
        start_datetime: 开始日期时间 (datetime 对象)
        end_datetime: 结束日期时间 (datetime 对象)
    Returns:
        tuple: (弹幕数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            table_name = get_danmu_table_name(room_id)

            table_exists = await conn.fetchval(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public'
                    AND table_name = $1
                );
                """,
                table_name,
            )

            if not table_exists:
                logger.info(f"表 {table_name} 不存在，无法查询弹幕数据")
                return [], 0

            query = f"""
                SELECT * FROM "{table_name}"
                WHERE datetime BETWEEN $1 AND $2
                ORDER BY datetime
            """

            results = await conn.fetch(query, start_datetime, end_datetime)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询弹幕数据时发生未知错误: {e}")
        return [], 0


async def query_superchat_by_room_and_timespan(room_id, start_ts, end_ts):
    """
    查询指定直播间在时间范围内的SC数据
    Args:
        room_id: 直播间ID
        start_ts: 开始时间戳(毫秒)
        end_ts: 结束时间戳(毫秒)
    Returns:
        tuple: (SC弹幕数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM super_chat_table
                WHERE room_id = $1
                  AND start_timestamp BETWEEN $2 AND $3
                ORDER BY start_timestamp
            """
            results = await conn.fetch(query, str(room_id), start_ts, end_ts)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询SC数据时发生未知错误: {e}")
        return [], 0


async def query_superchat_by_room_and_datetime(room_id, start_datetime, end_datetime):
    """
    查询指定直播间在时间范围内的SC数据
    Args:
        room_id: 直播间ID
        start_datetime: 开始日期时间 (datetime 对象)
        end_datetime: 结束日期时间 (datetime 对象)
    Returns:
        tuple: (SC弹幕数据列表, 数据条数)
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM super_chat_table
                WHERE room_id = $1
                  AND datetime BETWEEN $2 AND $3
                ORDER BY datetime
            """
            results = await conn.fetch(query, str(room_id), start_datetime, end_datetime)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询SC数据时发生未知错误: {e}")
        return [], 0


async def query_gift_by_room_and_timespan(room_id, start_ts, end_ts):
    """
    查询指定直播间在时间范围内的礼物数据
    Args:
        room_id: 直播间ID
        start_ts: 开始时间戳(毫秒)
        end_ts: 结束时间戳(毫秒)
    Returns:
        tuple: (礼物数据列表, 数据条数) 或 ([], 0) 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM gift_table
                WHERE room_id = $1
                  AND timestamp BETWEEN $2 AND $3
            """
            results = await conn.fetch(query, str(room_id), start_ts, end_ts)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询礼物数据时发生未知错误: {e}")
        return [], 0


async def query_gift_by_room_and_datetime(room_id, start_datetime, end_datetime):
    """
    查询指定直播间在时间范围内的礼物数据
    Args:
        room_id: 直播间ID
        start_datetime: 开始日期时间 (datetime 对象)
        end_datetime: 结束日期时间 (datetime 对象)
    Returns:
        tuple: (礼物数据列表, 数据条数) 或 ([], 0) 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM gift_table
                WHERE room_id = $1
                  AND datetime BETWEEN $2 AND $3
            """
            results = await conn.fetch(query, str(room_id), start_datetime, end_datetime)
            return results, len(results)

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询礼物数据时发生未知错误: {e}")
        return [], 0


async def query_enter_room_count_by_room_and_time(room_id, ts):
    """
    查询指定直播间在指定时间点的进入房间次数
    Args:
        room_id: 直播间ID
        ts: 时间戳(毫秒)
    Returns:
        int: 进入房间次数 或 0 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT COUNT(*) FROM enter_room_count_minute_table
                WHERE room_id = $1
                  AND timestamp = $2
            """
            result = await conn.fetchval(query, str(room_id), ts)
            return result or 0

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"查询进入房间次数时发生未知错误: {e}")
        return 0


async def query_interact_word_count_by_room_and_time(room_id, ts):
    """
    查询指定直播间在指定时间点的互动次数
    Args:
        room_id: 直播间ID
        ts: 时间戳(毫秒)
    Returns:
        tuple: 互动次数 或 0 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT COUNT(*) FROM interact_word_count_minute_table
                WHERE room_id = $1
                  AND timestamp = $2
            """
            result = await conn.fetchval(query, str(room_id), ts)
            return result or 0

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"查询互动次数时发生未知错误: {e}")
        return 0


async def query_active_watcher_count_by_room_and_time(room_id, ts):
    """
    查询指定直播间在指定时间点的活跃观众数量
    Args:
        room_id: 直播间ID
        ts: 时间戳(毫秒)
    Returns:
        int: 活跃观众数量 或 0 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT COUNT(*) FROM active_watcher_count_minute_table
                WHERE room_id = $1
                  AND timestamp = $2
            """
            result = await conn.fetchval(query, str(room_id), ts)
            return result or 0

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"查询活跃观众数量时发生未知错误: {e}")
        return 0


async def query_active_watcher_count_by_room_and_datetime(
    room_id, start_datetime, end_datetime
):
    """
    查询指定直播间在指定时间段的活跃观众数量累计
    Args:
        room_id: 直播间ID
        start_datetime: 开始日期时间 (datetime 对象)
        end_datetime: 结束日期时间 (datetime 对象)
    Returns:
        int: 活跃观众数量 或 0 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT MAX(count) FROM active_watcher_count_minute_table
                WHERE room_id = $1
                  AND datetime BETWEEN $2 AND $3
            """
            result = await conn.fetchval(query, str(room_id), start_datetime, end_datetime)
            return result or 0

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"查询活跃观众数量时发生未知错误: {e}")
        return 0


async def query_interact_word_count_by_room_and_datetime(
    room_id, start_datetime, end_datetime
):
    """
    查询指定直播间在指定时间段的互动次数累计次数
    Args:
        room_id: 直播间ID
        start_datetime: 开始日期时间 (datetime 对象)
        end_datetime: 结束日期时间 (datetime 对象)
    Returns:
        int: 互动次数 或 0 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT SUM(count) FROM interact_word_count_minute_table
                WHERE room_id = $1
                  AND datetime BETWEEN $2 AND $3
            """
            result = await conn.fetchval(query, str(room_id), start_datetime, end_datetime)
            return result or 0

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"查询互动次数时发生未知错误: {e}")
        return 0


async def query_live_status_by_room_and_time(room_id, ts):
    """
    查询指定直播间在指定时间点的直播状态
    Args:
        room_id: 直播间ID
        ts: 时间戳(毫秒)
    Returns:
        int: 直播状态 0下播， 1开播， 2轮播 或 0 如果出错e_s
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT live_status FROM live_status_minute_table
                WHERE room_id = $1
                  AND timestamp = $2
            """
            result = await conn.fetchrow(query, str(room_id), ts)

            if result is None:
                logger.info(f"No live status found for room_id: {room_id}")
                return 0

            return result['live_status']

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"查询直播状态时发生未知错误: {e}")
        return 0


async def query_now_live_info_by_room(room_id):
    """
    查询指定直播间当前直播状态
    Args:
        room_id: 直播间ID
    Returns:
        list
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM live_status_minute_table
                WHERE room_id = $1
                  AND timestamp = (SELECT MAX(timestamp) FROM live_status_minute_table WHERE room_id = $2)
            """
            result = await conn.fetchrow(query, str(room_id), str(room_id))
            return result

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return None
    except Exception as e:
        logger.error(f"查询当前直播信息时发生未知错误: {e}")
        return None


async def query_max_online_rank_count_by_room_and_datetime(
    room_id, start_datetime, end_datetime
):
    """
    查询指定直播间在指定时间的最大在线人数
    Args:
        room_id: 直播间IDint
        start_datetime: 开始日期时间 (datetime 对象)
        end_datetime: 结束日期时间 (datetime 对象)
    Returns:
        int: 在线人数 或 0 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT MAX(count) FROM online_rank_count_minute_table
                WHERE room_id = $1
                  AND datetime BETWEEN $2 AND $3
            """
            result = await conn.fetchval(query, str(room_id), start_datetime, end_datetime)
            return result or 0

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"查询最大在线人数时发生未知错误: {e}")
        return 0


async def query_online_rank_count_by_room_and_time(room_id, ts):
    """
    查询指定直播间在指定高能榜的在线人数
    Args:
        room_id: 直播间IDint
        ts: 时间戳(毫秒)
    Returns:
        int: 在线人数 或 0 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT COUNT(*) FROM online_rank_count_minute_table
                WHERE room_id = $1
                  AND timestamp = $2
            """
            result = await conn.fetchval(query, str(room_id), ts)
            return result or 0

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"查询高能榜在线人数时发生未知错误: {e}")
        return 0


async def query_average_online_rank_count_by_room_and_datetime(
    room_id, start_datetime, end_datetime
):
    """
    查询指定直播间在指定时间段的平均高能榜人数
    Args:
        room_id: 直播间ID
        start_datetime: 开始日期时间 (datetime 对象)
        end_datetime: 结束日期时间 (datetime 对象)
    Returns:
        float: 平均在线人数 或 0 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT ROUND(AVG(count)) FROM online_rank_count_minute_table
                WHERE room_id = $1
                  AND datetime BETWEEN $2 AND $3
            """
            result = await conn.fetchval(query, str(room_id), start_datetime, end_datetime)
            return result if result is not None else 0

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"查询平均高能榜人数时发生未知错误: {e}")
        return 0


async def query_average_enter_room_count_by_room_and_datetime(
    room_id, start_datetime, end_datetime
):
    """
    查询指定直播间在指定时间段的平均进房人数
    Args:
        room_id: 直播间ID
        start_datetime: 开始日期时间 (datetime 对象)
        end_datetime: 结束日期时间 (datetime 对象)
    Returns:
        float: 平均进房人数 或 0 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT ROUND(AVG(count)) FROM enter_room_count_minute_table
                WHERE room_id = $1
                  AND datetime BETWEEN $2 AND $3
            """
            result = await conn.fetchval(query, str(room_id), start_datetime, end_datetime)
            return result if result is not None else 0

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"查询平均进房人数时发生未知错误: {e}")
        return 0


async def query_total_enter_room_count_by_room_and_datetime(
    room_id, start_datetime, end_datetime
):
    """
    查询指定直播间在指定时间段的总进房人数
    Args:
        room_id: 直播间ID
        start_datetime: 开始日期时间 (datetime 对象)
        end_datetime: 结束日期时间 (datetime 对象)
    Returns:
        int: 总进房人数 或 0 如果出错
    """
    try:
        async with get_connection() as conn:
            query = """
                SELECT SUM(count) FROM enter_room_count_minute_table
                WHERE room_id = $1
                  AND datetime BETWEEN $2 AND $3
            """
            result = await conn.fetchval(query, str(room_id), start_datetime, end_datetime)
            return result if result is not None else 0

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0
    except Exception as e:
        logger.error(f"查询总进房人数时发生未知错误: {e}")
        return 0


async def query_pay_count_by_room_and_live_date(room_id, live_date):
    """根据房间ID和直播日期查询付费次数和收入

    参数:
        room_id: 要查询的房间ID
        live_date: 日期字符串，格式为 'YYYY-MM-DD'

    返回:
        tuple: (总付费次数, 总收入, 结果字典, 执行时间毫秒)
    """
    result = {"gift_table": [], "buy_guard_table": [], "super_chat_table": []}
    total_count = 0
    total_income = Decimal("0.0")
    start_time = time.time()

    try:
        async with get_connection() as conn:
            for table in result.keys():
                query = f"""
                    SELECT * FROM "{table}"
                    WHERE room_id = $1
                      AND datetime >= $2::DATE
                      AND datetime < $3::DATE + INTERVAL '1 DAY'
                """
                rows = await conn.fetch(query, str(room_id), live_date, live_date)
                result[table] = rows
                total_count += len(rows)

                if table == "gift_table":
                    income = sum(
                        (Decimal(str(row["total_coin"])) / Decimal(1000)).quantize(
                            Decimal("0.0"), rounding=ROUND_HALF_UP
                        )
                        for row in rows
                    )
                elif table == "buy_guard_table":
                    income = sum(
                        (
                            Decimal(str(row["gift_per_price"]))
                            * Decimal(str(row["gift_num"]))
                            / Decimal(1000)
                        ).quantize(Decimal("0.0"), rounding=ROUND_HALF_UP)
                        for row in rows
                    )
                elif table == "super_chat_table":
                    income = sum(
                        Decimal(str(round(row["price"] * row["gift_num"], 1)))
                        for row in rows
                    )
                total_income += income

            elapsed_ms = (time.time() - start_time) * 1000
            return total_count, total_income, result, elapsed_ms

    except asyncpg.PostgresError as e:
        logger.error(f"Database operation failed: {e}")
        return 0, Decimal("0.0"), {}, 0
    except Exception as e:
        logger.error(f"查询付费次数时发生未知错误: {e}")
        return 0, Decimal("0.0"), {}, 0


async def query_pay_count_by_room_and_live_start_end_time(
    room_id, in_start_time, in_end_time
):
    """根据房间ID和时间范围查询付费次数和收入

    Args:
        room_id: 直播间ID
        in_start_time: 开始时间(字符串或时间戳)
        in_end_time: 结束时间(字符串或时间戳)

    Returns:
        tuple: (总付费次数, 总收入, 结果字典, 执行时间毫秒)
    """
    result = {"gift_table": [], "buy_guard_table": [], "super_chat_table": []}
    total_count = 0
    total_income = Decimal("0.0")
    start_time = time.time()

    try:
        async with get_connection() as conn:
            for table in result.keys():
                if isinstance(in_start_time, str) and isinstance(in_end_time, str):
                    start_dt = datetime.strptime(in_start_time, "%Y-%m-%d %H:%M:%S")
                    end_dt = datetime.strptime(in_end_time, "%Y-%m-%d %H:%M:%S")
                    time_params = (str(room_id), start_dt, end_dt)
                    time_field = "datetime"
                else:
                    time_params = (str(room_id), in_start_time, in_end_time)
                    time_field = (
                        "start_timestamp"
                        if table == "super_chat_table"
                        else "timestamp"
                    )

                query = f"""
                    SELECT * FROM "{table}"
                    WHERE room_id = $1
                      AND "{time_field}" BETWEEN $2 AND $3
                """

                rows = await conn.fetch(query, *time_params)
                result[table] = rows
                total_count += len(rows)

                if table == "gift_table":
                    income = sum(
                        (Decimal(str(row["total_coin"])) / Decimal(1000)).quantize(
                            Decimal("0.0"), rounding=ROUND_HALF_UP
                        )
                        for row in rows
                    )
                elif table == "buy_guard_table":
                    income = sum(
                        (
                            Decimal(str(row["gift_per_price"]))
                            * Decimal(str(row["gift_num"]))
                            / Decimal(1000)
                        ).quantize(Decimal("0.0"), rounding=ROUND_HALF_UP)
                        for row in rows
                    )
                elif table == "super_chat_table":
                    income = sum(
                        Decimal(str(round(row["price"] * row["gift_num"], 1)))
                        for row in rows
                    )
                total_income += income

            elapsed_ms = (time.time() - start_time) * 1000
            return total_count, total_income, result, elapsed_ms

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return 0, Decimal("0.0"), {}, 0
    except Exception as e:
        logger.error(f"查询付费次数时发生未知错误: {e}")
        return 0, Decimal("0.0"), {}, 0


async def query_live_start_end_time_by_live_date(
    room_id, live_date_str, date_is_start_live_date=False
):
    """根据房间ID和日期查询直播开始和结束时间

    参数:
        room_id: 要查询的房间ID
        live_date_str: 日期字符串，格式为 'YYYY-MM-DD'
        date_is_start_live_date 传入的日期是开播时间所在的日期

    返回:
        tuple: (包含开始和结束时间对的列表, 执行时间毫秒)
    """
    start_time = time.time()
    result_time_pair_list = []
    temp_live_time_pair = {}

    try:
        async with get_connection() as conn:
            live_date_format = (
                datetime.strptime(live_date_str, "%Y-%m-%d")
                if isinstance(live_date_str, str)
                else live_date_str
            )

            query = """
                SELECT * FROM live_status_minute_table
                WHERE room_id = $1
                AND datetime >= $2::DATE
                AND datetime < $3::DATE + INTERVAL '1 DAY'
                AND (live_action = '开始直播' OR live_action = '结束直播')
                ORDER BY datetime
            """

            rows = await conn.fetch(
                query, str(room_id), live_date_format, live_date_format
            )

            for row in rows:
                if row["live_status"] == 1 and row["live_action"] == "开始直播":
                    start_time_str = datetime.fromtimestamp(
                        row["timestamp"]
                    ).strftime("%Y-%m-%d %H:%M:%S")
                    temp_live_time_pair["start_time_str"] = start_time_str
                elif (row["live_status"] in [0, 2]) and row["live_action"] == "结束直播":
                    end_time_str = datetime.fromtimestamp(
                        row["timestamp"]
                    ).strftime("%Y-%m-%d %H:%M:%S")
                    temp_live_time_pair["end_time_str"] = end_time_str

                    if not date_is_start_live_date and "start_time_str" not in temp_live_time_pair:
                        prev_live_date = live_date_format - timedelta(days=1)
                        prev_rows = await conn.fetch(
                            query, str(room_id), prev_live_date, prev_live_date
                        )
                        if prev_rows:
                            last_prev_row = prev_rows[-1]
                            if last_prev_row["live_status"] == 1 and last_prev_row["live_action"] == "开始直播":
                                prev_start_ts = last_prev_row["timestamp"]
                                temp_live_time_pair["start_time_str"] = datetime.fromtimestamp(
                                    prev_start_ts
                                ).strftime("%Y-%m-%d %H:%M:%S")

                    if "start_time_str" in temp_live_time_pair:
                        result_time_pair_list.append(copy.deepcopy(temp_live_time_pair))
                    temp_live_time_pair = {}

            if date_is_start_live_date and rows and rows[-1]["live_status"] == 1 and rows[-1]["live_action"] == "开始直播":
                last_start_time_str = datetime.fromtimestamp(
                    rows[-1]["timestamp"]
                ).strftime("%Y-%m-%d %H:%M:%S")
                next_live_date = live_date_format + timedelta(days=1)
                next_rows = await conn.fetch(
                    query, str(room_id), next_live_date, next_live_date
                )
                if next_rows:
                    first_next_row = next_rows[0]
                    if first_next_row["live_status"] in [0, 2] and first_next_row["live_action"] == "结束直播":
                        next_end_time_str = datetime.fromtimestamp(
                            first_next_row["timestamp"]
                        ).strftime("%Y-%m-%d %H:%M:%S")
                        result_time_pair_list.append({
                            "start_time_str": last_start_time_str,
                            "end_time_str": next_end_time_str,
                        })

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return [], 0
    except Exception as e:
        logger.error(f"查询直播起止时间时发生未知错误: {e}")
        return [], 0

    elapsed_ms = (time.time() - start_time) * 1000
    return result_time_pair_list, elapsed_ms


async def query_live_start_time_by_end_time(room_id, end_time_str):
    """根据直播结束时间，查询当天直播开始时间

    Args:
        room_id: 房间号
        end_time_str: 直播结束时间，格式为 %Y-%m-%d %H:%M:%S

    Returns:
        tuple: (直播开始时间字符串, 执行时间毫秒)
    """
    start_time_str = ""
    try:
        end_time_format = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
    except ValueError as e:
        logger.error(f"时间格式错误: {e}")
        return "", 0

    execution_start_time = time.time()
    try:
        async with get_connection() as conn:
            query = """
                SELECT * FROM live_status_minute_table
                WHERE room_id = $1
                AND datetime >= $2 - INTERVAL '24 HOURS'
                AND datetime < $3
                AND live_action = '开始直播'
                ORDER BY datetime DESC
                LIMIT 1
            """
            result_row = await conn.fetchrow(query, str(room_id), end_time_format, end_time_format)

            if result_row:
                start_time_str = datetime.fromtimestamp(
                    result_row["timestamp"]
                ).strftime("%Y-%m-%d %H:%M:%S")
            else:
                start_time_str = end_time_format.replace(
                    hour=0, minute=0, second=0, microsecond=0
                ).strftime("%Y-%m-%d %H:%M:%S")

            elapsed_ms = (time.time() - execution_start_time) * 1000
            return start_time_str, elapsed_ms

    except asyncpg.PostgresError as e:
        logger.error(f"数据库操作失败: {e}")
        return "", 0
    except Exception as e:
        logger.error(f"查询直播开始时间时发生未知错误: {e}")
        return "", 0


async def query_whole_live_info_with_live_id(live_id):
    """
    通过 live_id 异步查询完整的直播信息
    """
    live_info = {
        "liveId": live_id, "isFinish": False, "isFull": False, "parentArea": "", "area": "",
        "coverUrl": "", "danmakusCount": 0, "startDate": "", "stopDate": "", "title": "",
        "totalIncome": 0.0, "watchCount": 0, "likeCount": 0, "payCount": 0,
        "interactionCount": 0, "onlineRank": 0, "aveOnlineRank": 0, "aveEnterRoom": 0,
    }

    try:
        async with get_connection() as conn:
            status_data = await conn.fetchrow(
                """
                SELECT live_action, parent_area, area, cover, title
                FROM live_status_minute_table
                WHERE live_id = $1 ORDER BY timestamp DESC LIMIT 1
                """, live_id
            )

            if status_data:
                actions_rows = await conn.fetch(
                    "SELECT DISTINCT live_action FROM live_status_minute_table WHERE live_id = $1", live_id
                )
                actions = {row["live_action"] for row in actions_rows}
                live_info.update({"isFinish": "结束直播" in actions, "isFull": "开始直播" in actions})

            session_data = await conn.fetchrow(
                """
                SELECT danmu_count, start_time_str, end_time_str, parent_area, area, cover, title,
                       income, watch_change_count, like_info_update_count, pay_count,
                       interaction_count, max_online_rank, enter_room_count, ave_online_rank, ave_enter_room
                FROM live_session_table WHERE live_id = $1
                """, live_id
            )

            if session_data:
                live_info.update({
                    "danmakusCount": session_data["danmu_count"], "startDate": session_data["start_time_str"],
                    "stopDate": session_data["end_time_str"], "parentArea": session_data["parent_area"],
                    "area": session_data["area"], "coverUrl": session_data["cover"], "title": session_data["title"],
                    "totalIncome": float(session_data["income"] or 0), "watchCount": session_data["watch_change_count"],
                    "likeCount": session_data["like_info_update_count"], "payCount": session_data["pay_count"],
                    "interactionCount": session_data["interaction_count"], "onlineRank": session_data["max_online_rank"],
                    "enterRoom": session_data["enter_room_count"], "aveOnlineRank": session_data["ave_online_rank"],
                    "aveEnterRoom": session_data["ave_enter_room"],
                })
    except asyncpg.PostgresError as e:
        logger.error(f"Database operation failed for live_id {live_id}: {e}")
        return None
    except Exception as e:
        logger.error(f"查询完整直播信息时发生未知错误 for live_id {live_id}: {e}")
        return None

    return live_info


async def query_minutes_live_info_with_live_id(live_id, interval=10):
    """
    查询分钟级别的直播信息
    """
    base_live_info = await query_whole_live_info_with_live_id(live_id)
    if not base_live_info:
        logger.error(f"无法获取直播ID {live_id} 的基本信息")
        return None

    result = base_live_info.copy()
    result.update({
        "danmuCountList": [], "activeWatcherList": [], "onlineRankCountList": [],
        "incomeCountList": [], "interactCountList": [], "enterCountList": [], "timestamp": []
    })

    try:
        async with get_connection() as conn:
            if not result.get("startDate"):
                logger.error(f"无法获取直播ID {live_id} 的开始时间")
                return result

            start_time = datetime.strptime(result["startDate"], "%Y-%m-%d %H:%M:%S")
            end_time = datetime.strptime(result["stopDate"], "%Y-%m-%d %H:%M:%S") if result.get("stopDate") else datetime.now()

            time_points = [start_time + timedelta(minutes=i * interval) for i in range(int((end_time - start_time).total_seconds() / 60 / interval) + 1)]
            if not time_points: return result

            timestamps = [int(tp.timestamp()) for tp in time_points]
            result["timestamp"] = [tp.strftime("%Y-%m-%d %H:%M:%S") for tp in time_points]

            minute_tables = {
                "danmuCountList": ("danmu_count_minute_table", "SUM", "count"),
                "activeWatcherList": ("active_watcher_count_minute_table", "MAX", "count"),
                "onlineRankCountList": ("online_rank_count_minute_table", "MAX", "count"),
                "incomeCountList": ("income_minute_table", "SUM", "income"),
                "interactCountList": ("interact_word_count_minute_table", "SUM", "count"),
                "enterCountList": ("enter_room_count_minute_table", "SUM", "count"),
            }

            for i in range(len(time_points) - 1):
                start_ts, end_ts = timestamps[i], timestamps[i+1]
                for key, (table, agg_func, field) in minute_tables.items():
                    query = f"SELECT COALESCE({agg_func}({field}), 0) FROM {table} WHERE live_id = $1 AND timestamp >= $2 AND timestamp < $3"
                    value = await conn.fetchval(query, live_id, start_ts, end_ts)
                    if key == "incomeCountList":
                        value = float(Decimal(str(value or 0)).quantize(Decimal("0.0"), rounding=ROUND_HALF_UP))
                    result[key].append(value or 0)

    except asyncpg.PostgresError as e:
        logger.error(f"查询分钟级别直播信息失败: {e}")
    except Exception as e:
        logger.error(f"查询分钟级别直播信息时发生未知错误: {e}")
    
    return result


async def query_live_info_with_room_id(room_id):
    """
    异步查询给定房间ID的所有直播信息，并包含聚合统计数据。
    """
    result = {
        "lastLiveDate": "", "lastLiveDanmakuCount": 0, "lastLiveIncome": 0.0,
        "lastAveEnterRoomCount": 0, "lastEnterRoomCount": 0, "lastAveOnlineRankCount": 0,
        "totalDanmakuCount": 0, "totalIncome": 0.0, "totalLiveCount": 0,
        "totalLiveSecond": 0.0, "sessions": [],
    }
    total_duration = timedelta()

    try:
        async with get_connection() as conn:
            all_sessions_for_stats = await conn.fetch(
                """
                SELECT live_id, danmu_count, start_time_str, end_time_str, income, watch_change_count,
                       ave_online_rank, ave_enter_room, enter_room_count, datetime
                FROM live_session_table WHERE room_id = $1
                ORDER BY datetime DESC, start_time_str DESC
                """, str(room_id)
            )

            if not all_sessions_for_stats:
                logger.info(f"房间 {room_id} 没有找到直播会话记录。")
                return result

            result["totalLiveCount"] = len(all_sessions_for_stats)
            result["totalDanmakuCount"] = sum(s["danmu_count"] for s in all_sessions_for_stats if s["danmu_count"] is not None)
            result["totalIncome"] = float(sum(Decimal(str(s["income"])) for s in all_sessions_for_stats if s["income"] is not None))

            latest_session = all_sessions_for_stats[0]
            result.update({
                "lastLiveDate": latest_session["datetime"].strftime("%Y-%m-%d") if latest_session["datetime"] else "",
                "lastLiveDanmakuCount": latest_session["danmu_count"] or 0,
                "lastLiveIncome": latest_session["income"] or 0,
                "lastWatchCount": latest_session["watch_change_count"] or 0,
                "lastAveOnlineRankCount": latest_session["ave_online_rank"] or 0,
                "lastAveEnterRoomCount": latest_session["ave_enter_room"] or 0,
                "lastEnterRoomCount": latest_session["enter_room_count"] or 0,
            })

            for session in all_sessions_for_stats:
                if session["start_time_str"] and session["end_time_str"]:
                    try:
                        start_dt = datetime.strptime(session["start_time_str"], "%Y-%m-%d %H:%M:%S")
                        end_dt = datetime.strptime(session["end_time_str"], "%Y-%m-%d %H:%M:%S")
                        if end_dt > start_dt: total_duration += end_dt - start_dt
                    except (ValueError, TypeError) as e:
                        logger.warning(f"解析时间字符串时出错 live_id={session['live_id']}: {e}")
            result["totalLiveSecond"] = total_duration.total_seconds()

            live_ids = [s["live_id"] for s in all_sessions_for_stats]

        if live_ids:
            tasks = [query_whole_live_info_with_live_id(live_id) for live_id in live_ids]
            live_info_results = await asyncio.gather(*tasks, return_exceptions=True)
            result["sessions"] = [info for info in live_info_results if info and not isinstance(info, Exception)]

    except asyncpg.PostgresError as e:
        logger.error(f"查询房间 {room_id} 的数据库操作失败: {e}")
        result["sessions"] = []
    except Exception as e:
        logger.error(f"处理房间 {room_id} 数据时发生意外错误: {e}")
        result["sessions"] = []
    
    return result
