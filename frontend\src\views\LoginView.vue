<template>
  <div class="flex h-screen">
    <!-- Left Panel: Form -->
    <div class="w-full lg:w-1/3 flex flex-col justify-center items-center bg-sky-100 p-8 sm:p-12">
      <div class="w-full max-w-md">
        <!-- Login View -->
        <div v-if="isLoginView">
          <div class="text-left mb-10">
            <h1 class="text-6xl font-bold text-gray-800">登录</h1>
          </div>

          <!--
          <button
            @click="handleWechatLogin"
            class="w-full flex items-center justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 mb-6"
          >
            <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 2C5.03 2 1 5.82 1 10.5c0 2.96 1.7 5.58 4.25 6.99A8.02 8.02 0 0010 18c1.25 0 2.43-.29 3.5-.81 2.55-1.41 4.25-4.03 4.25-6.99C19 5.82 14.97 2 10 2zm3.5 10.5h-2v2h-3v-2h-2v-3h2v-2h3v2h2v3z"/>
            </svg>
            使用微信登录
          </button>

          <div class="relative mb-6">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-sky-100 text-gray-500">或</span>
            </div>
          </div>
          -->

          <form @submit.prevent="handleLogin">
            <div class="mb-4">
              <label for="login-email" class="block text-lg font-medium text-gray-700 text-left ml-1">邮箱</label>
              <input
                type="email"
                id="login-email"
                v-model="email"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="<EMAIL>"
              />
            </div>

            <div class="mb-4">
              <label for="login-password" class="block text-lg font-medium text-gray-700 text-left ml-1">密码</label>
              <div class="relative">
                <input
                  :type="showPassword ? 'text' : 'password'"
                  id="login-password"
                  v-model="password"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  @click="togglePasswordVisibility"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                >
                  <svg v-if="!showPassword" class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>
                  <svg v-else class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7 1.274 4.057 5.064 7 9.542 7 .847 0 1.673.126 2.468.368M18.438 10.092A8.917 8.917 0 0121.542 12c-1.274 4.057-5.064 7-9.542 7a9.01 9.01 0 01-1.312-.11M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3l18 18"></path></svg>
                </button>
              </div>
            </div>

            <div class="flex items-center justify-between mb-6">
              <div class="text-sm">
                <a href="#" @click.prevent="handleForgotPassword" class="font-medium text-blue-600 hover:text-blue-500">忘记密码?</a>
              </div>
            </div>

            <button
              type="submit"
              class="w-full flex justify-center py-3 px-4 border border-blue-600 rounded-md shadow-sm text-base font-medium text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              登录！
            </button>
          </form>

          <p class="mt-8 text-center text-sm text-gray-600">
            暂时没有账号?
            <button @click="toggleView" class="font-medium text-blue-600 hover:text-blue-500">注册</button>
          </p>
        </div>

        <!-- Sign Up View -->
        <div v-else>
          <div class="text-left mb-10">
            <h1 class="text-6xl font-bold text-gray-800">注册</h1>
          </div>

          <form @submit.prevent="handleSignUp">
            <div class="mb-4">
              <label for="signup-email" class="block text-lg font-medium text-gray-700 text-left ml-1">邮箱</label>
              <input
                type="email"
                id="signup-email"
                v-model="email"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="<EMAIL>"
              />
            </div>

            <div class="mb-4">
              <label for="signup-password" class="block text-lg font-medium text-gray-700 text-left ml-1">密码</label>
              <div class="relative">
                <input
                  :type="showPassword ? 'text' : 'password'"
                  id="signup-password"
                  v-model="password"
                  required
                  class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  @click="togglePasswordVisibility"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5"
                >
                  <!-- Eye Icon (SVG or Font Icon) -->
                  <svg v-if="!showPassword" class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>
                  <svg v-else class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7 1.274 4.057 5.064 7 9.542 7 .847 0 1.673.126 2.468.368M18.438 10.092A8.917 8.917 0 0121.542 12c-1.274 4.057-5.064 7-9.542 7a9.01 9.01 0 01-1.312-.11M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3l18 18"></path></svg>
                </button>
              </div>
            </div>

            <div class="mb-4">
              <label for="confirmPassword" class="block text-sm font-medium text-gray-700 text-left">确认密码</label>
              <input
                type="password"
                id="confirmPassword"
                v-model="confirmPassword"
                required
                class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="••••••••"
              />
            </div>

            <div class="mb-4">
              <label for="verificationCode" class="block text-sm font-medium text-gray-700 text-left">验证码</label>
              <div class="flex items-center mt-1">
                <input
                  type="text"
                  id="verificationCode"
                  v-model="verificationCode"
                  required
                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="输入验证码"
                />
                <button
                  type="button"
                  @click="getVerificationCode"
                  :disabled="isCodeButtonDisabled"
                  class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {{ codeButtonText }}
                </button>
              </div>
            </div>

            <button
              type="submit"
              class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mt-6"
            >
              注册
            </button>
          </form>

          <p class="mt-8 text-center text-sm text-gray-600">
            已有账号?
            <button @click="toggleView" class="font-medium text-blue-600 hover:text-blue-500">登录</button>
          </p>
        </div>

        <!-- Forgot Password Modal -->
        <div v-if="showForgotPasswordModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex justify-center items-center z-50" @click.self="closeForgotPasswordModal">
          <div class="relative mx-auto p-8 border w-full max-w-md shadow-lg rounded-md bg-white" @click.stop>
            <button @click="closeForgotPasswordModal" class="absolute top-0 right-0 mt-4 mr-4 text-gray-400 hover:text-gray-600">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
            </button>
            <div class="text-center">
              <h3 class="text-2xl font-bold text-gray-900 mb-6">重置密码</h3>

              <!-- Step 1: Enter Email -->
              <form v-if="forgotPasswordStep === 1" @submit.prevent="sendResetCode">
                <p class="text-sm text-gray-600 mb-4">请输入您的邮箱地址，我们将向您发送验证码以重置密码。</p>
                <div class="mb-4">
                  <label for="forgot-email" class="block text-sm font-medium text-gray-700 text-left">邮箱</label>
                  <input
                    type="email"
                    id="forgot-email"
                    v-model="forgotPasswordEmail"
                    required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="<EMAIL>"
                  />
                </div>
                <button
                  type="submit"
                  class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  发送验证码
                </button>
              </form>

              <!-- Step 2: Enter Code and New Password -->
              <form v-if="forgotPasswordStep === 2" @submit.prevent="handleResetPassword">
                <p class="text-sm text-gray-600 mb-4">验证码已发送至 {{ forgotPasswordEmail }}。请输入验证码和您的新密码。</p>
                <div class="mb-4">
                  <label for="reset-code" class="block text-sm font-medium text-gray-700 text-left">验证码</label>
                  <div class="flex items-center mt-1">
                    <input
                      type="text"
                      id="reset-code"
                      v-model="resetCode"
                      required
                      class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="输入验证码"
                    />
                    <button
                      type="button"
                      @click="sendResetCode"
                      :disabled="isResetCodeButtonDisabled"
                      class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      {{ resetCodeButtonText }}
                    </button>
                  </div>
                </div>
                <div class="mb-4">
                  <label for="new-password" class="block text-sm font-medium text-gray-700 text-left">新密码</label>
                  <input
                    type="password"
                    id="new-password"
                    v-model="newPassword"
                    required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="输入新密码"
                  />
                </div>
                <div class="mb-6">
                  <label for="confirm-new-password" class="block text-sm font-medium text-gray-700 text-left">确认新密码</label>
                  <input
                    type="password"
                    id="confirm-new-password"
                    v-model="confirmNewPassword"
                    required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="再次输入新密码"
                  />
                </div>
                <button
                  type="submit"
                  class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  重置密码
                </button>
              </form>
            </div>
          </div>
        </div>

        <!-- add a pic for pics/icon.png -->
        <div class="mt-10 flex justify-center mb-6">
            <img
              src="/pics/icon.png"
              alt="App Icon"
              class="h-24 w-24 object-contain transition-all duration-300 hover:scale-110 hover:rotate-6"
            >
        </div>

        <p class="text-center text-xs text-gray-500">
          &copy; {{ new Date().getFullYear() }} CDD Tech. All rights reserved.
        </p>
      </div>
    </div>

    <!-- Right Panel: Background Image Wall -->
    <div class="hidden lg:block lg:w-3/4 bg-cover bg-center relative overflow-hidden" style="background-image: url('/pics/bg_v1_43.jpeg');">
      <!-- Image with animation -->
      <div class="absolute inset-0 bg-cover bg-center animate-zoom-in-out" style="background-image: url('/pics/bg_v1_43.jpeg');"></div>
      <!-- Overlay to make the image less prominent -->
      <div class="absolute inset-0 bg-black opacity-30"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '../api';

const router = useRouter();

// Common state for both forms
const email = ref('');
const password = ref('');
const showPassword = ref(false);

// State for view toggle
const isLoginView = ref(true); // true for login, false for signup

// Sign Up specific state
const confirmPassword = ref(''); // Used in Sign Up form
const verificationCode = ref(''); // Used in Sign Up form for email verification
const countdown = ref(0); // Used for Sign Up verification code button

// Forgot Password specific state
const showForgotPasswordModal = ref(false);
const forgotPasswordEmail = ref('');
const resetCode = ref(''); // Verification code for password reset
const newPassword = ref('');
const confirmNewPassword = ref('');
const forgotPasswordStep = ref(1); // 1: enter email, 2: enter code and new password
const resetCodeCountdown = ref(0); // Countdown for reset password verification code

// Computed properties for sign up
const codeButtonText = computed(() => { // For sign-up
  return countdown.value > 0 ? `${countdown.value}s` : '获取验证码';
});
const isCodeButtonDisabled = computed(() => countdown.value > 0); // For sign-up

// Computed properties for forgot password
const resetCodeButtonText = computed(() => {
  return resetCodeCountdown.value > 0 ? `${resetCodeCountdown.value}s` : '获取验证码';
});
const isResetCodeButtonDisabled = computed(() => resetCodeCountdown.value > 0);


// --- Common Methods ---
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

const resetFormFields = () => {
  email.value = ''; // For login/signup form
  password.value = ''; // For login/signup form
  confirmPassword.value = ''; // For signup form
  verificationCode.value = ''; // For signup form
  showPassword.value = false;

  // Reset sign-up countdown
  if (countdown.value > 0) {
    countdown.value = 0;
  }

  // Reset forgot password fields and state
  forgotPasswordEmail.value = '';
  resetCode.value = '';
  newPassword.value = '';
  confirmNewPassword.value = '';
  forgotPasswordStep.value = 1;
  if (resetCodeCountdown.value > 0) {
    resetCodeCountdown.value = 0;
  }
  showForgotPasswordModal.value = false; // Ensure modal is hidden
};

const toggleView = () => {
  isLoginView.value = !isLoginView.value;
  resetFormFields(); // Clear fields when switching views, this will also reset forgot password state
};

// --- Login Methods ---
const handleLogin = async () => {
  if (email.value && password.value) {
    try {
      const response = await api.loginUser({
        email: email.value,
        password: password.value,
      });
      console.log('Login response:', response);
      // Assuming the API returns a token in response.access_token or response.token
      // And user info might be in response.user or response.data.user
      // Adjust based on the actual structure of your backend response
      if (response && response.access_token) {
        localStorage.setItem('userToken', response.access_token);
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('email', email.value);
        localStorage.setItem('isLoggedIn', 'true');
        // Optionally store user info if returned, e.g., response.user
        // localStorage.setItem('userInfo', JSON.stringify(response.user));
        router.push('/'); // Redirect to home page
        console.log('Login successful, redirecting to home.');
      } else if (response && response.data && response.data.access_token) { // Fallback for nested token
        localStorage.setItem('userToken', response.data.access_token);
        localStorage.setItem('isLoggedIn', 'true');
        router.push('/');
        console.log('Login successful (nested token), redirecting to home.');
      }
      else {
        // Handle cases where login is "successful" by API call but no token
        alert('登录成功，但未收到预期的用户信息。请联系管理员。');
        console.error('Login successful but no token/expected data received:', response);
      }
    } catch (error: any) {
      console.error('Login error:', error);
      if (error.response && error.response.data && error.response.data.detail) {
        alert(`登录失败: ${error.response.data.detail}`);
      } else if (error.message) {
        alert(`登录失败: ${error.message}`);
      }
      else {
        alert('登录失败。请检查您的邮箱和密码，然后重试。');
      }
    }
  } else {
    alert('请输入邮箱和密码。');
    console.log('Login attempt failed: Email or password missing.');
  }
};

const handleWechatLogin = () => {
  // Placeholder for WeChat login logic
  alert('微信登录功能暂未实现。');
  console.log('Attempted WeChat Login.');
};

const handleForgotPassword = () => {
  // Open the forgot password modal
  // Clear previous login/signup form fields before showing modal for forgot password
  email.value = '';
  password.value = '';
  confirmPassword.value = '';
  verificationCode.value = '';
  showPassword.value = false;
  if (countdown.value > 0) countdown.value = 0;

  forgotPasswordEmail.value = ''; // Clear any previous email in forgot password form
  resetCode.value = '';
  newPassword.value = '';
  confirmNewPassword.value = '';
  forgotPasswordStep.value = 1; // Start at step 1
  if (resetCodeCountdown.value > 0) resetCodeCountdown.value = 0; // Reset countdown
  showForgotPasswordModal.value = true;
  console.log('Forgot password modal opened.');
};

// --- Forgot Password Methods ---
const closeForgotPasswordModal = () => {
  showForgotPasswordModal.value = false;
  // Reset forgot password specific fields when closing modal
  forgotPasswordEmail.value = '';
  resetCode.value = '';
  newPassword.value = '';
  confirmNewPassword.value = '';
  forgotPasswordStep.value = 1;
  if (resetCodeCountdown.value > 0) {
    resetCodeCountdown.value = 0;
  }
  // Optionally, reset common form fields if they were used by forgot password
  // resetFormFields(); // Or be more selective
};

const sendResetCode = async () => {
  if (!forgotPasswordEmail.value) {
    alert('请输入您的邮箱地址。');
    return;
  }
  try {
    const response = await api.sendVerificationCode({
      email: forgotPasswordEmail.value,
      type: 'reset_password',
    });
    alert(response.message || `重置密码的验证码已发送至 ${forgotPasswordEmail.value}。`);
    forgotPasswordStep.value = 2; // Move to next step

    // Start reset code countdown
    resetCodeCountdown.value = 60;
    const timer = setInterval(() => {
      resetCodeCountdown.value--;
      if (resetCodeCountdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);

  } catch (error: any) {
    console.error('Error sending reset code:', error);
    if (error.response && error.response.data && error.response.data.detail) {
      alert(`发送验证码失败: ${error.response.data.detail}`);
    } else if (error.message) {
      alert(`发送验证码失败: ${error.message}`);
    } else {
      alert('发送验证码失败。请稍后重试。');
    }
  }
};

const handleResetPassword = async () => {
  if (newPassword.value !== confirmNewPassword.value) {
    alert('两次输入的新密码不一致。');
    return;
  }
  if (!forgotPasswordEmail.value || !resetCode.value || !newPassword.value) {
    alert('请填写所有必填项。');
    return;
  }
  try {
    const response = await api.resetPassword({
      email: forgotPasswordEmail.value,
      new_password: newPassword.value,
      code: resetCode.value,
    });
    alert(response.message || '密码已成功重置。请使用新密码登录。');
    closeForgotPasswordModal();
    // Optionally switch to login view and prefill email if desired
    isLoginView.value = true;
    email.value = forgotPasswordEmail.value; // Prefill email for login
    password.value = ''; // Clear password field
  } catch (error: any) {
    console.error('Error resetting password:', error);
    if (error.response && error.response.data && error.response.data.detail) {
      alert(`重置密码失败: ${error.response.data.detail}`);
    } else if (error.message) {
      alert(`重置密码失败: ${error.message}`);
    } else {
      alert('重置密码失败。请稍后重试。');
    }
  }
};

// --- Sign Up Methods ---
const getVerificationCode = async () => {
  if (!email.value) {
    alert('请先输入您的邮箱地址。');
    return;
  }
  try {
    console.log(`Requesting verification code for ${email.value}`);
    // Call the sendVerificationCode API
    const response = await api.sendVerificationCode({
      email: email.value,
      type: 'register', // Or 'reset_password' or 'change_email' depending on context
    });
    console.log('Verification code response:', response);
    // Handle successful code sending
    // Assuming the API returns a success message, e.g., in response.message
    alert(response.message || `验证码已发送至 ${email.value}。请检查您的邮箱。`);

    // Start countdown
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  } catch (error: any) {
    console.error('Error sending verification code:', error);
    if (error.response && error.response.data && error.response.data.detail) {
      alert(`发送验证码失败: ${error.response.data.detail}`);
    } else if (error.message) {
      alert(`发送验证码失败: ${error.message}`);
    }
    else {
      alert('发送验证码失败。请稍后重试。');
    }
  }
};

const handleSignUp = async () => {
  if (password.value !== confirmPassword.value) {
    alert('两次输入的密码不一致。');
    return;
  }
  if (email.value && password.value && verificationCode.value) {
    try {
      const response = await api.registerUser({
        email: email.value,
        password: password.value,
        code: verificationCode.value, // Assuming 'code' is the field name for verification code
      });
      console.log('Sign up response:', response);
      // Handle successful registration
      // Assuming the API returns a success message or user info
      alert(response.message || '注册成功！请登录。');
      toggleView(); // Switch to login view
      // email.value is already set, clear other fields for login
      password.value = '';
      confirmPassword.value = '';
      verificationCode.value = '';
    } catch (error: any) {
      console.error('Sign up error:', error);
      if (error.response && error.response.data && error.response.data.detail) {
        alert(`注册失败: ${error.response.data.detail}`);
      } else if (error.message) {
        alert(`注册失败: ${error.message}`);
      }
      else {
        alert('注册失败。请稍后重试。');
      }
    }
  } else {
    alert('请填写所有必填项。');
  }
};
</script>

<style scoped>
@keyframes zoom-in-out {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05); /* Slightly zoom in */
  }
}

.animate-zoom-in-out {
  animation: zoom-in-out 10s infinite ease-in-out; /* 10s duration, infinite loop, ease-in-out timing */
}
</style>
