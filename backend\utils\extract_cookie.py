# -*- coding: utf-8 -*-

cookie = """
buvid3=7CC068A5-4472-F3A9-6B7A-C629B3E03F1068933infoc; b_nut=1720164468; _uuid=82D9EAC5-1035D-C157-3F5F-C37F7C1DDF10669271infoc; enable_web_push=DISABLE; buvid4=FEFDA7F5-36FA-E09B-F9B3-F33FAAB8599869673-024070507-UI0EsRBcZsD03y8ke8JmWA%3D%3D; header_theme_version=CLOSE; rpdid=|(um|kmkJ|~)0J'u~k|u~)~Ju; buvid_fp_plain=undefined; CURRENT_QUALITY=80; LIVE_BUVID=AUTO8617206034164898; hit-dyn-v2=1; enable_feed_channel=ENABLE; DedeUserID=1514288514; DedeUserID__ckMd5=4be5ecb5f22edc8c; bsource=search_bing; PVID=1; fingerprint=********************************; home_feed_column=5; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTAzMDgwMTYsImlhdCI6MTc1MDA0ODc1NiwicGx0IjotMX0.MtXD8y5kwefXpZpWQuEgjN77veWfN0h5jScBsdo5wpg; bili_ticket_expires=1750307956; buvid_fp=********************************; SESSDATA=a5b70b79%2C1765600817%2C010da%2A61CjDrWESBcpOwcBdGHpsIaDo1D4I-ieV7M4tkmlGky8mudpoO2PqLWRJWg7oMNnAwwUUSVllzZ244T2tmUUdqY2pYQ0RCYkg1ZWhDaTdCbkk2V1liTWR6WXpnNDQxbDNTNUkyQ19OMFFER2VHS0gtOHNLT0NrU0VWeDdLNlpPUFhQV0FkVEdfLTNRIIEC; bili_jct=4787c0ed677bc804672b34bf334d1343; sid=4jn8vpdf; bp_t_offset_1514288514=1078951619330048000; b_lsid=76A109442_19777B2B187; CURRENT_FNVAL=2000; browser_resolution=1519-911
"""


# cookie = """
# buvid3=7A9033AB-2843-E30C-D6A5-CEA14A0A71A880933infoc; b_nut=1725987880; _uuid=E104A4F5E-51B4-C8D2-7794-165EC102DDB8E81048infoc; enable_web_push=DISABLE; buvid4=9871B804-287B-FA73-2F90-1BD647880D9681439-024091017-qdXK1QUQFKw0pLifwa9zNQ%3D%3D; header_theme_version=CLOSE; rpdid=|(um|kmkJ|~Y0J'u~klm))|~); DedeUserID=224780442; DedeUserID__ckMd5=7c2f92cdd5fd4d6a; hit-dyn-v2=1; LIVE_BUVID=AUTO9717315048529878; buvid_fp_plain=undefined; CURRENT_QUALITY=80; go-back-dyn=0; enable_feed_channel=ENABLE; SESSDATA=d3fbed55%2C1765168820%2Cdcb92%2A61CjA7J7XcYFzx-5zUzoNz5H9e3NYFrzTEFu6iPkVn6fROaIw1lg25SSje98YQupTTxU0SVkNwMV9jM25SbVVrZGhfZDYxWEo3dXNOMERFZ3Q0STMyeEY5VVhLZTlxOTQtZzBjb3pNdDFyc25GZWNHRFRobG5qeXhGQ21lbUVTZDMtc0dlY1VsbXFRIIEC; bili_jct=73b892971dfc1647574d073f474dbe2e; fingerprint=9c23685b4ff99eb4b022de7784c57446; timeMachine=0; sid=71e9ino0; share_source_origin=WEIXIN; bsource=share_source_weixinchat; b_lsid=1D3CF4F5_197681738F8; CURRENT_FNVAL=2000; home_feed_column=5; browser_resolution=3165-1598; buvid_fp=7A9033AB-2843-E30C-D6A5-CEA14A0A71A880933infoc; bp_t_offset_224780442=1077874815194365952; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTAwNTk4ODgsImlhdCI6MTc0OTgwMDYyOCwicGx0IjotMX0.QKaE_Er-rWQ0U0o5xcaLcF5Nb8kignoAVeTFb8vPD2E; bili_ticket_expires=1750059828
# """

# 需要提取的字段及其对应的cookie名
fields = {
    "SESSDATA": "SESSDATA",
    "BILI_JCT": "bili_jct",
    "B_NUT": "b_nut",
    "SID": "sid",
    "BUVID3": "buvid3",
    "BUVID4": "buvid4",
    "DEDEUSERID": "DedeUserID",
}

# 解析cookie为字典
cookie_dict = {}
for item in cookie.split(";"):
    if "=" in item:
        k, v = item.strip().split("=", 1)
        cookie_dict[k] = v

# 提取并输出
output = []
for key, cookie_name in fields.items():
    value = cookie_dict.get(cookie_name, "")
    output.append(f'{key} = "{value}"')

# 输出为text
result = "\n".join(output)
print(result)
