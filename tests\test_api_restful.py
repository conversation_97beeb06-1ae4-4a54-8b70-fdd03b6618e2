"""
RESTful API 接口测试
测试修改后的 API 接口是否符合 RESTful 标准
"""

import pytest
import httpx
from fastapi.testclient import TestClient
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from backend.app import app

client = TestClient(app)

class TestUserAuthenticationAPIs:
    """测试用户认证相关的 RESTful API"""
    
    def test_create_user_new_endpoint(self):
        """测试新的用户创建接口 POST /web/users"""
        user_data = {
            "email": "<EMAIL>",
            "username": "testuser",
            "password": "testpassword123",
            "phone": "1234567890"
        }
        
        response = client.post("/web/users", json=user_data)
        
        # 检查状态码
        assert response.status_code == 201
        
        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 201
        assert response_data["message"] == "User created successfully"
        
        # 检查返回的用户数据
        user_info = response_data["data"]
        assert user_info["email"] == user_data["email"]
        assert user_info["username"] == user_data["username"]
        assert "id" in user_info
        assert "created_at" in user_info
        # 确保密码不在响应中
        assert "password" not in user_info
        assert "password_hash" not in user_info
    
    def test_create_user_old_endpoint_backward_compatibility(self):
        """测试旧的用户注册接口向后兼容性"""
        user_data = {
            "email": "<EMAIL>",
            "username": "testuser2",
            "password": "testpassword123",
            "phone": "1234567890"
        }
        
        response = client.post("/web/users/register", json=user_data)
        
        # 旧接口应该仍然工作
        assert response.status_code == 201
        
        # 旧接口返回的是 UserInDB 格式
        response_data = response.json()
        assert "email" in response_data
        assert "username" in response_data
        assert response_data["email"] == user_data["email"]
    
    def test_create_auth_session_new_endpoint(self):
        """测试新的认证会话创建接口 POST /web/auth/sessions"""
        # 首先创建一个用户
        user_data = {
            "email": "<EMAIL>",
            "username": "authuser",
            "password": "testpassword123"
        }
        client.post("/web/users", json=user_data)
        
        # 然后尝试登录
        login_data = {
            "email": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = client.post("/web/auth/sessions", json=login_data)
        
        # 检查状态码
        assert response.status_code == 200
        
        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 200
        assert response_data["message"] == "Authentication successful"
        
        # 检查返回的认证数据
        auth_data = response_data["data"]
        assert "access_token" in auth_data
        assert "token_type" in auth_data
        assert "user" in auth_data
        assert auth_data["token_type"] == "bearer"
        
        user_info = auth_data["user"]
        assert user_info["email"] == login_data["email"]
        assert user_info["username"] == user_data["username"]
    
    def test_update_password_new_method(self):
        """测试新的密码更新接口 PATCH /web/users/password"""
        # 首先创建一个用户
        user_data = {
            "email": "<EMAIL>",
            "username": "passworduser",
            "password": "oldpassword123"
        }
        client.post("/web/users", json=user_data)
        
        # 更新密码
        update_data = {
            "email": "<EMAIL>",
            "new_password": "newpassword123"
        }
        
        response = client.patch("/web/users/password", json=update_data)
        
        # 检查状态码
        assert response.status_code == 200
        
        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert response_data["code"] == 200
        assert response_data["message"] == "Password updated successfully"
    
    def test_delete_user_new_endpoint(self):
        """测试新的用户删除接口 DELETE /web/users"""
        # 首先创建一个用户
        user_data = {
            "email": "<EMAIL>",
            "username": "deleteuser",
            "password": "testpassword123"
        }
        client.post("/web/users", json=user_data)
        
        # 删除用户
        delete_data = {
            "email": "<EMAIL>"
        }
        
        response = client.request("DELETE", "/web/users", json=delete_data)
        
        # 检查状态码 - 删除成功应该返回 204 No Content
        assert response.status_code == 204

class TestVTuberAPIs:
    """测试 VTuber 相关的 RESTful API"""

    def test_get_vtuber_mid_new_endpoint(self):
        """测试新的 VTuber UID 获取接口 GET /vtubers/{name}/mid"""
        vtuber_name = "星瞳"

        response = client.get(f"/vtubers/{vtuber_name}/mid")

        # 检查状态码
        assert response.status_code == 200

        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 200

        # 检查返回的数据
        vtuber_data = response_data["data"]
        assert "mid" in vtuber_data
        assert "vtuber_name" in vtuber_data
        assert vtuber_data["vtuber_name"] == vtuber_name

    def test_get_vtuber_current_dahanghai_new_endpoint(self):
        """测试新的 VTuber 当前大航海数获取接口"""
        vtuber_name = "星瞳"

        response = client.get(f"/vtubers/{vtuber_name}/dahanghai/current")

        # 检查状态码
        assert response.status_code == 200

        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 200

        # 检查返回的数据
        dahanghai_data = response_data["data"]
        assert "vtuber_name" in dahanghai_data
        assert "mid" in dahanghai_data
        assert "current_dahanghai" in dahanghai_data
        assert "timestamp" in dahanghai_data
        assert dahanghai_data["vtuber_name"] == vtuber_name

    def test_get_vtuber_stats_by_period_new_endpoint(self):
        """测试新的 VTuber 时期统计数据获取接口"""
        vtuber_name = "星瞳"
        start_date = "2024-01-01"
        end_date = "2024-01-31"

        response = client.get(f"/vtubers/{vtuber_name}/stats/period?start_date={start_date}&end_date={end_date}")

        # 检查状态码
        assert response.status_code == 200

        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 200

        # 检查返回的数据
        stats_data = response_data["data"]
        assert "vtuber_name" in stats_data
        assert "mid" in stats_data
        assert "period" in stats_data
        assert "statistics" in stats_data
        assert stats_data["vtuber_name"] == vtuber_name
        assert stats_data["period"]["start_date"] == start_date
        assert stats_data["period"]["end_date"] == end_date
    
    def test_get_vtuber_current_followers_new_endpoint(self):
        """测试新的 VTuber 当前粉丝数获取接口"""
        vtuber_name = "星瞳"
        
        response = client.get(f"/vtubers/{vtuber_name}/followers/current")
        
        # 检查状态码
        assert response.status_code == 200
        
        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 200
        
        # 检查返回的数据
        follower_data = response_data["data"]
        assert "vtuber_name" in follower_data
        assert "mid" in follower_data
        assert "current_followers" in follower_data
        assert "timestamp" in follower_data
        assert follower_data["vtuber_name"] == vtuber_name
    
    def test_get_vtuber_follower_history_new_endpoint(self):
        """测试新的 VTuber 粉丝历史数据获取接口"""
        vtuber_name = "星瞳"
        
        response = client.get(f"/vtubers/{vtuber_name}/followers/history?recent=30&limit=10&offset=0")
        
        # 检查状态码
        assert response.status_code == 200
        
        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 200
        
        # 检查返回的数据
        history_data = response_data["data"]
        assert "vtuber_name" in history_data
        assert "mid" in history_data
        assert "history" in history_data
        assert "pagination" in history_data
        assert history_data["vtuber_name"] == vtuber_name
        
        # 检查分页信息
        pagination = history_data["pagination"]
        assert "total" in pagination
        assert "limit" in pagination
        assert "offset" in pagination
        assert "has_more" in pagination

class TestVideoAPIs:
    """测试视频相关的 RESTful API"""

    def test_get_video_conclusion_new_endpoint(self):
        """测试新的视频结论获取接口 GET /videos/{bvid}/conclusion"""
        bvid = "BV11k4y1Y71i"

        response = client.get(f"/videos/{bvid}/conclusion")

        # 检查状态码
        assert response.status_code == 200

        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 200

        # 检查返回的数据
        conclusion_data = response_data["data"]
        assert "bvid" in conclusion_data
        assert "conclusion" in conclusion_data
        assert "generated_at" in conclusion_data
        assert conclusion_data["bvid"] == bvid

    def test_get_video_current_views_new_endpoint(self):
        """测试新的视频当前播放量获取接口"""
        bvid = "BV11k4y1Y71i"

        response = client.get(f"/videos/{bvid}/views/current")

        # 检查状态码
        assert response.status_code == 200

        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 200

        # 检查返回的数据结构
        view_data = response_data["data"]
        # 数据可能是解析后的字典格式或原始格式
        if isinstance(view_data, dict):
            assert "bvid" in view_data or "date" in view_data
        else:
            # 原始格式应该是列表
            assert isinstance(view_data, list)

class TestDynamicsAPIs:
    """测试动态相关的 RESTful API"""

    def test_get_vtuber_dynamics_new_endpoint(self):
        """测试新的 VTuber 动态获取接口"""
        vtuber_name = "星瞳"

        response = client.get(f"/vtubers/{vtuber_name}/dynamics?limit=5&sort=popularity")

        # 检查状态码
        assert response.status_code == 200

        # 检查响应格式
        response_data = response.json()
        assert "code" in response_data
        assert "message" in response_data
        assert "data" in response_data
        assert response_data["code"] == 200

        # 检查返回的数据
        dynamics_data = response_data["data"]
        assert "vtuber_name" in dynamics_data
        assert "mid" in dynamics_data
        assert "filters" in dynamics_data
        assert "dynamics" in dynamics_data
        assert "pagination" in dynamics_data
        assert dynamics_data["vtuber_name"] == vtuber_name

class TestBackwardCompatibility:
    """测试向后兼容性"""
    
    def test_old_endpoints_still_work(self):
        """测试旧的接口仍然可以工作"""
        # 测试旧的 mid 接口
        response = client.get("/basic/mid?vtuber=星瞳")
        assert response.status_code == 200
        
        # 测试旧的粉丝数接口
        response = client.get("/basic/follower/current?vtuber=星瞳")
        assert response.status_code == 200
        
        # 测试旧的粉丝历史接口
        response = client.get("/basic/follower/all?vtuber=星瞳&recent=30")
        assert response.status_code == 200

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
